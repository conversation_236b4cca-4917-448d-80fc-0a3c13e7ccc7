<?php
/**
 * News/Blog Page - Latest Updates and Insights
 * Features image-based articles instead of icons
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Fetch blog posts from database
$db = Database::getConnection();
$stmt = $db->prepare("SELECT * FROM blog_posts WHERE status = 'published' ORDER BY published_at DESC");
$stmt->execute();
$blog_posts = $stmt->fetchAll();

// Get featured article (latest post)
$featured_article = !empty($blog_posts) ? $blog_posts[0] : null;

// Get other articles (excluding featured)
$other_articles = array_slice($blog_posts, 1);

$pageTitle = 'News & Insights - Latest from Monolith Design';
$pageDescription = 'Stay updated with the latest news, insights, and trends in architecture and design from our expert team at Monolith Design.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>

    <!-- News Page Specific CSS -->
    <style>
        /* ===== NEWS PAGE STYLES ===== */

        /* News Hero Section */
        .news-hero {
            background: var(--light-accent);
            padding: 6rem 0 4rem;
            margin-top: 80px;
            position: relative;
            overflow: hidden;
        }

        .news-hero-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }

        .hero-pattern {
            position: absolute;
            top: 0;
            right: 0;
            width: 60%;
            height: 100%;
            background: linear-gradient(135deg, var(--accent-color) 0%, #d35400 100%);
            opacity: 0.03;
            clip-path: polygon(30% 0%, 100% 0%, 100% 100%, 0% 100%);
        }

        .news-hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 2;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 4rem;
            align-items: start;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 3rem;
            font-size: 0.875rem;
            color: var(--text-light);
            grid-column: 1 / -1;
        }

        .breadcrumb a {
            color: var(--text-light);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb a:hover {
            color: var(--accent-color);
        }

        .breadcrumb-separator {
            color: var(--text-light);
            opacity: 0.5;
        }

        .news-hero-content {
            padding-top: 1rem;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--accent-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 2rem;
        }

        .news-hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 1.5rem;
            line-height: 1.1;
        }

        .news-hero-subtitle {
            font-size: 1.2rem;
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 3rem;
            max-width: 500px;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
        }

        .hero-stat {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-light);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .news-hero-visual {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            padding-top: 2rem;
        }

        .hero-card {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .hero-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: var(--light-accent);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            color: var(--accent-color);
        }

        .hero-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        .hero-card p {
            color: var(--text-light);
            font-size: 0.9rem;
            line-height: 1.5;
            margin: 0;
        }

        /* Featured Article */
        .featured-article {
            padding: 8rem 0;
            background: white;
        }
        
        .featured-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6rem;
            align-items: center;
        }
        
        .featured-image {
            position: relative;
        }
        
        .featured-image img {
            width: 100%;
            border-radius: 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .featured-image::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: -20px;
            bottom: -20px;
            border: 3px solid var(--accent-color);
            z-index: -1;
        }
        
        .featured-info {
            padding-left: 2rem;
        }
        
        .featured-category {
            color: var(--accent-color);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1rem;
        }
        
        .featured-info h2 {
            color: var(--text-color);
            font-size: 2.5rem;
            margin-bottom: 1rem;
            line-height: 1.2;
        }
        
        .featured-meta {
            display: flex;
            align-items: center;
            gap: 2rem;
            margin-bottom: 2rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .featured-author {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .featured-description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 2rem;
        }
        
        .read-more-btn {
            background: var(--accent-color);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            font-weight: 600;
            transition: background 0.3s ease;
            display: inline-block;
        }
        
        .read-more-btn:hover {
            background: #d4711d;
            color: white;
            text-decoration: none;
        }
        
        /* News Grid */
        .news-grid {
            padding: 8rem 0;
            background: #f8f9fa;
        }
        
        .news-grid h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 4rem;
            color: var(--text-color);
        }
        
        .news-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 3rem;
        }
        
        .news-card {
            background: white;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .news-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }
        
        .news-image {
            height: 250px;
            overflow: hidden;
            position: relative;
        }
        
        .news-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .news-card:hover .news-image img {
            transform: scale(1.1);
        }
        
        .news-badge {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: var(--accent-color);
            color: white;
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .news-content {
            padding: 2rem;
        }
        
        .news-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .news-title {
            font-size: 1.4rem;
            margin-bottom: 1rem;
            color: var(--text-color);
            line-height: 1.3;
        }
        
        .news-title a {
            color: inherit;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .news-title a:hover {
            color: var(--accent-color);
        }
        
        .news-excerpt {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .news-link {
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }
        
        .news-link:hover {
            color: #d4711d;
            text-decoration: none;
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .news-hero-container {
                grid-template-columns: 1fr;
                gap: 3rem;
            }

            .news-hero-visual {
                grid-template-columns: 1fr 1fr;
                display: grid;
                gap: 1.5rem;
            }

            .featured-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }

            .featured-info {
                padding-left: 0;
            }
            

            
            .featured-info h2 {
                font-size: 2rem;
            }
        }
        
        @media (max-width: 768px) {
            .news-hero {
                padding: 4rem 0 3rem;
            }

            .news-hero-container {
                padding: 0 1rem;
                gap: 2rem;
            }

            .news-hero-title {
                font-size: 2.5rem;
            }

            .news-hero-subtitle {
                font-size: 1rem;
            }

            .hero-stats {
                justify-content: center;
                gap: 1.5rem;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .news-hero-visual {
                display: block;
            }

            .hero-card {
                padding: 1.5rem;
            }

            .featured-article,
            .news-grid {
                padding: 4rem 0;
            }

            .news-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Custom News Hero Section -->
    <section class="news-hero">
        <div class="news-hero-background">
            <div class="hero-pattern"></div>
        </div>

        <div class="news-hero-container">
            <div class="breadcrumb">
                <a href="<?php echo siteUrl(); ?>">Home</a>
                <span class="breadcrumb-separator">›</span>
                <span>News & Insights</span>
            </div>

            <div class="news-hero-content">
                <div class="hero-badge">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                    </svg>
                    <span>Latest Updates</span>
                </div>

                <h1 class="news-hero-title">News & Insights</h1>
                <p class="news-hero-subtitle">Stay updated with the latest trends, projects, and insights from the world of modern architecture and design.</p>

                <div class="hero-stats">
                    <div class="hero-stat">
                        <span class="stat-number"><?php echo count($blog_posts); ?>+</span>
                        <span class="stat-label">Articles</span>
                    </div>
                    <div class="hero-stat">
                        <span class="stat-number">5</span>
                        <span class="stat-label">Categories</span>
                    </div>
                    <div class="hero-stat">
                        <span class="stat-number">1000+</span>
                        <span class="stat-label">Readers</span>
                    </div>
                </div>
            </div>

            <div class="news-hero-visual">
                <div class="hero-card">
                    <div class="card-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <h3>Expert Insights</h3>
                    <p>Professional perspectives on architecture and design trends</p>
                </div>

                <div class="hero-card">
                    <div class="card-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <h3>Project Updates</h3>
                    <p>Latest developments from our ongoing architectural projects</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Article -->
    <?php if ($featured_article): ?>
    <section class="featured-article">
        <div class="container">
            <div class="featured-content">
                <div class="featured-image">
                    <img src="<?php echo $featured_article['featured_image'] ?: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=800&h=600&fit=crop'; ?>" alt="<?php echo htmlspecialchars($featured_article['title']); ?>">
                </div>
                <div class="featured-info">
                    <div class="featured-category"><?php echo htmlspecialchars($featured_article['category']); ?></div>
                    <h2><?php echo htmlspecialchars($featured_article['title']); ?></h2>
                    <div class="featured-meta">
                        <div class="featured-author">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                            <span><?php echo htmlspecialchars($featured_article['author']); ?></span>
                        </div>
                        <div class="featured-date">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                            </svg>
                            <span><?php echo formatDate($featured_article['published_at'] ?: $featured_article['created_at']); ?></span>
                        </div>
                    </div>
                    <div class="featured-description">
                        <p><?php echo htmlspecialchars($featured_article['excerpt']); ?></p>
                    </div>
                    <a href="<?php echo siteUrl('news/' . $featured_article['slug']); ?>" class="read-more-btn">Read Full Article</a>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- News Grid -->
    <section class="news-grid">
        <div class="container">
            <h2>Latest News & Updates</h2>
            <div class="news-container">
                <?php if (!empty($other_articles)): ?>
                    <?php foreach ($other_articles as $article): ?>
                    <article class="news-card">
                        <div class="news-image">
                            <img src="<?php echo $article['featured_image'] ?: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=600&h=400&fit=crop'; ?>" alt="<?php echo htmlspecialchars($article['title']); ?>">
                            <div class="news-badge"><?php echo htmlspecialchars($article['category']); ?></div>
                        </div>
                        <div class="news-content">
                            <div class="news-meta">
                                <span><?php echo htmlspecialchars($article['author']); ?></span>
                                <span>•</span>
                                <span><?php echo formatDate($article['published_at'] ?: $article['created_at']); ?></span>
                            </div>
                            <h3 class="news-title">
                                <a href="<?php echo siteUrl('news/' . $article['slug']); ?>"><?php echo htmlspecialchars($article['title']); ?></a>
                            </h3>
                            <p class="news-excerpt"><?php echo htmlspecialchars($article['excerpt']); ?></p>
                            <a href="<?php echo siteUrl('news/' . $article['slug']); ?>" class="news-link">Read More →</a>
                        </div>
                    </article>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p style="text-align: center; color: #666; padding: 2rem;">No news articles found. Check back soon for updates!</p>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Newsletter Hero Section -->
    <?php include 'templates/newsletter-hero.php'; ?>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
</body>
</html>
