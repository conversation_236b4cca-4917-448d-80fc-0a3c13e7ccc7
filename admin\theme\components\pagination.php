<?php
/**
 * Pagination Component
 * Reusable pagination for admin tables
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Default pagination settings
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = isset($per_page) ? $per_page : 20;
$total_items = isset($total_items) ? $total_items : 0;
$total_pages = ceil($total_items / $per_page);
$offset = ($page - 1) * $per_page;

// Calculate pagination range
$range = 2; // Show 2 pages before and after current page
$start_page = max(1, $page - $range);
$end_page = min($total_pages, $page + $range);

// Only show pagination if there are multiple pages
if ($total_pages > 1): ?>
    <div class="pagination-wrapper">
        <div class="pagination-info">
            <span class="text-muted">
                Showing <?php echo min($offset + 1, $total_items); ?> to 
                <?php echo min($offset + $per_page, $total_items); ?> of 
                <?php echo $total_items; ?> entries
            </span>
        </div>
        
        <nav aria-label="Table pagination">
            <ul class="pagination pagination-sm">
                <!-- First page -->
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=1<?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?>" title="First page">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                <?php endif; ?>
                
                <!-- Previous page -->
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?>" title="Previous page">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                <?php endif; ?>
                
                <!-- Page numbers -->
                <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                <?php endfor; ?>
                
                <!-- Next page -->
                <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?>" title="Next page">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                <?php endif; ?>
                
                <!-- Last page -->
                <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $total_pages; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?>" title="Last page">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
        
        <!-- Items per page selector -->
        <div class="per-page-selector">
            <select class="form-select form-select-sm" onchange="changePerPage(this.value)" style="width: auto;">
                <option value="10" <?php echo $per_page == 10 ? 'selected' : ''; ?>>10 per page</option>
                <option value="20" <?php echo $per_page == 20 ? 'selected' : ''; ?>>20 per page</option>
                <option value="50" <?php echo $per_page == 50 ? 'selected' : ''; ?>>50 per page</option>
                <option value="100" <?php echo $per_page == 100 ? 'selected' : ''; ?>>100 per page</option>
            </select>
        </div>
    </div>

    <style>
    .pagination-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
        padding: 1rem 0;
        border-top: 1px solid #dee2e6;
    }
    
    .pagination-info {
        font-size: 0.875rem;
    }
    
    .pagination {
        margin: 0;
    }
    
    .page-link {
        color: var(--admin-primary-color);
        border-color: #dee2e6;
        padding: 0.375rem 0.75rem;
    }
    
    .page-link:hover {
        color: var(--admin-accent-color);
        background-color: #f8f9fa;
        border-color: #dee2e6;
    }
    
    .page-item.active .page-link {
        background-color: var(--admin-accent-color);
        border-color: var(--admin-accent-color);
        color: white;
    }
    
    .per-page-selector {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    @media (max-width: 768px) {
        .pagination-wrapper {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }
        
        .pagination {
            order: 2;
        }
        
        .per-page-selector {
            order: 3;
        }
        
        .pagination-info {
            order: 1;
        }
    }
    </style>

    <script>
    function changePerPage(perPage) {
        const url = new URL(window.location);
        url.searchParams.set('per_page', perPage);
        url.searchParams.set('page', '1'); // Reset to first page
        window.location.href = url.toString();
    }
    </script>
<?php endif; ?>

<?php
// Export pagination variables for use in queries
$GLOBALS['pagination'] = [
    'page' => $page,
    'per_page' => $per_page,
    'offset' => $offset,
    'total_items' => $total_items,
    'total_pages' => $total_pages
];
?>
