<?php
/**
 * Hero Sections Create Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure pages_without_heroes variable is available
if (!isset($pages_without_heroes)) {
    $pages_without_heroes = [];
}
?>

<?php if (!empty($pages_without_heroes)): ?>
<div class="admin-card">
    <div class="admin-card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-plus-circle me-2"></i>
            Pages Without Hero Sections (<?php echo count($pages_without_heroes); ?>)
        </h5>
        <form method="POST" style="display: inline;">
            <input type="hidden" name="action" value="scan_pages">
            <button type="submit" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-sync me-1"></i>
                Refresh <PERSON>an
            </button>
        </form>
    </div>
    
    <div class="admin-card-body">
        <p class="text-muted mb-3">
            These pages don't have hero sections yet. Click "Create Hero" to add one with default content.
        </p>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Page Title</th>
                        <th>Page File</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pages_without_heroes as $page): ?>
                    <tr>
                        <td>
                            <strong><?php echo htmlspecialchars($page['page_title']); ?></strong>
                        </td>
                        <td>
                            <code><?php echo htmlspecialchars($page['page_name']); ?></code>
                        </td>
                        <td>
                            <button type="button" 
                                    class="btn btn-primary btn-sm" 
                                    data-bs-toggle="modal" 
                                    data-bs-target="#createHeroModal" 
                                    onclick="setCreateHeroPage('<?php echo htmlspecialchars($page['page_name']); ?>', '<?php echo htmlspecialchars($page['page_title']); ?>')">
                                <i class="fas fa-plus me-1"></i>
                                Create Hero
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create Hero Modal -->
<div class="modal fade" id="createHeroModal" tabindex="-1" aria-labelledby="createHeroModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createHeroModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>
                    Create Hero Section
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" id="createHeroForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_hero_for_page">
                    <input type="hidden" name="page_name" id="modal_page_name">
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Creating hero section for: <strong id="modal_page_title"></strong>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="modal_title" class="form-label">
                                    Hero Title <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       id="modal_title" 
                                       name="title" 
                                       class="form-control" 
                                       required 
                                       placeholder="Welcome to Our Page">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="modal_caption" class="form-label">Caption (Optional)</label>
                                <input type="text" 
                                       id="modal_caption" 
                                       name="caption" 
                                       class="form-control" 
                                       placeholder="Discover Excellence">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="modal_description" class="form-label">Description</label>
                        <textarea id="modal_description" 
                                  name="description" 
                                  class="form-control" 
                                  rows="3" 
                                  placeholder="Brief description of what this page offers..."></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="modal_button_text" class="form-label">Button Text</label>
                                <input type="text" 
                                       id="modal_button_text" 
                                       name="button_text" 
                                       class="form-control" 
                                       placeholder="Learn More">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="modal_button_link" class="form-label">Button Link</label>
                                <select id="modal_button_link" name="button_link" class="form-control">
                                    <option value="">No button</option>
                                    <option value="contact">Contact Us</option>
                                    <option value="services">Our Services</option>
                                    <option value="projects">Our Projects</option>
                                    <option value="about">About Us</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label class="form-label">Background Style</label>
                        <div class="background-style-options">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="radio" 
                                       name="background_type" 
                                       value="gradient" 
                                       id="modal_bg_gradient" 
                                       checked>
                                <label class="form-check-label" for="modal_bg_gradient">
                                    Professional Gradient
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="radio" 
                                       name="background_type" 
                                       value="image" 
                                       id="modal_bg_image">
                                <label class="form-check-label" for="modal_bg_image">
                                    Upload Image Later
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="modal_height_type" class="form-label">Hero Height</label>
                        <select name="height_type" id="modal_height_type" class="form-control">
                            <option value="small">Small (300px)</option>
                            <option value="medium" selected>Medium (400px)</option>
                            <option value="large">Large (600px)</option>
                        </select>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Note:</strong> This will create a basic hero section. You can edit it afterwards to customize colors, background images, and advanced settings.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        Create Hero Section
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function setCreateHeroPage(pageName, pageTitle) {
    document.getElementById('modal_page_name').value = pageName;
    document.getElementById('modal_page_title').textContent = pageTitle;
    
    // Set default title based on page title
    document.getElementById('modal_title').value = 'Welcome to ' + pageTitle;
    
    // Set default description based on page type
    let defaultDescription = '';
    switch(pageName) {
        case 'services':
            defaultDescription = 'Discover our comprehensive range of professional services designed to meet your needs.';
            break;
        case 'projects':
            defaultDescription = 'Explore our portfolio of successful projects and see the quality of our work.';
            break;
        case 'about':
            defaultDescription = 'Learn more about our company, our team, and our commitment to excellence.';
            break;
        case 'contact':
            defaultDescription = 'Get in touch with us today to discuss your project and requirements.';
            break;
        case 'team':
            defaultDescription = 'Meet our experienced team of professionals dedicated to your success.';
            break;
        default:
            defaultDescription = 'Discover what makes us the right choice for your needs.';
    }
    
    document.getElementById('modal_description').value = defaultDescription;
    
    // Set default button text based on page type
    let defaultButtonText = '';
    let defaultButtonLink = '';
    switch(pageName) {
        case 'services':
            defaultButtonText = 'View Our Services';
            defaultButtonLink = 'services';
            break;
        case 'projects':
            defaultButtonText = 'View Our Work';
            defaultButtonLink = 'projects';
            break;
        case 'about':
            defaultButtonText = 'Learn More';
            defaultButtonLink = 'about';
            break;
        case 'contact':
            defaultButtonText = 'Contact Us';
            defaultButtonLink = 'contact';
            break;
        case 'team':
            defaultButtonText = 'Meet Our Team';
            defaultButtonLink = 'team';
            break;
        default:
            defaultButtonText = 'Learn More';
            defaultButtonLink = 'contact';
    }
    
    document.getElementById('modal_button_text').value = defaultButtonText;
    document.getElementById('modal_button_link').value = defaultButtonLink;
}
</script>

<style>
.background-style-options .form-check {
    margin-bottom: 0.5rem;
}

.form-check-input:checked {
    background-color: var(--admin-accent-color);
    border-color: var(--admin-accent-color);
}

.alert {
    border-radius: 0.375rem;
}

.modal-body .form-group {
    margin-bottom: 1rem;
}

.form-text {
    font-size: 0.8rem;
}
</style>
<?php endif; ?>
