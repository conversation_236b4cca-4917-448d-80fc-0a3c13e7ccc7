/**
 * Mobile App Experience JavaScript
 * Enhanced mobile interactions and app-like behavior
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile app features
    initMobileAppFeatures();
    initTouchInteractions();
    initMobileTabBar();
    initAppAnimations();
    initMobileOptimizations();
});

/**
 * Initialize mobile app features
 */
function initMobileAppFeatures() {
    // Add mobile app class to body
    if (window.innerWidth <= 768) {
        document.body.classList.add('mobile-app-mode');
    }
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            document.body.classList.add('mobile-app-mode');
        } else {
            document.body.classList.remove('mobile-app-mode');
        }
    });
    
    // Prevent zoom on double tap (iOS)
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);
    
    // Add viewport meta tag for mobile optimization
    if (!document.querySelector('meta[name="viewport"]')) {
        const viewport = document.createElement('meta');
        viewport.name = 'viewport';
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
        document.head.appendChild(viewport);
    }
}

/**
 * Initialize touch interactions
 */
function initTouchInteractions() {
    // Add touch feedback to interactive elements
    const touchElements = document.querySelectorAll('.mobile-nav-link, .mobile-tab-item, .btn, .primary-button, .secondary-button, .service-card, .project-card, .team-member');
    
    touchElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.classList.add('touch-active');
        });
        
        element.addEventListener('touchend', function() {
            setTimeout(() => {
                this.classList.remove('touch-active');
            }, 150);
        });
        
        element.addEventListener('touchcancel', function() {
            this.classList.remove('touch-active');
        });
    });
    
    // Swipe gestures for mobile navigation
    let touchStartX = 0;
    let touchStartY = 0;
    
    document.addEventListener('touchstart', function(e) {
        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
    });
    
    document.addEventListener('touchmove', function(e) {
        if (!touchStartX || !touchStartY) return;
        
        const touchEndX = e.touches[0].clientX;
        const touchEndY = e.touches[0].clientY;
        
        const diffX = touchStartX - touchEndX;
        const diffY = touchStartY - touchEndY;
        
        // Horizontal swipe detection
        if (Math.abs(diffX) > Math.abs(diffY)) {
            if (Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    // Swipe left - close mobile menu if open
                    const mobileNav = document.getElementById('mobile-nav');
                    const mobileToggle = document.getElementById('mobile-toggle');
                    if (mobileNav && mobileNav.classList.contains('active')) {
                        mobileToggle.click();
                    }
                } else {
                    // Swipe right - open mobile menu if closed
                    const mobileNav = document.getElementById('mobile-nav');
                    const mobileToggle = document.getElementById('mobile-toggle');
                    if (mobileNav && !mobileNav.classList.contains('active') && touchStartX < 50) {
                        mobileToggle.click();
                    }
                }
            }
        }
        
        touchStartX = 0;
        touchStartY = 0;
    });
}

/**
 * Initialize mobile tab bar functionality
 */
function initMobileTabBar() {
    const tabItems = document.querySelectorAll('.mobile-tab-item');
    
    tabItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Add ripple effect
            const ripple = document.createElement('div');
            ripple.classList.add('ripple');
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
            
            // Add bounce animation
            this.classList.add('app-bounce');
            setTimeout(() => {
                this.classList.remove('app-bounce');
            }, 500);
        });
    });
}

/**
 * Initialize app animations
 */
function initAppAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('app-fade-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.service-card, .project-card, .team-member, .work-card, .section-title');
    animateElements.forEach(el => {
        observer.observe(el);
    });
    
    // Stagger animations for grid items
    const gridContainers = document.querySelectorAll('.services-grid, .projects-grid, .team-members, .work-list');
    gridContainers.forEach(container => {
        const items = container.children;
        Array.from(items).forEach((item, index) => {
            item.style.animationDelay = `${index * 0.1}s`;
        });
    });
}

/**
 * Initialize mobile optimizations
 */
function initMobileOptimizations() {
    // Lazy loading for images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
    
    // Optimize scroll performance
    let ticking = false;
    function updateScrollPosition() {
        const scrollTop = window.pageYOffset;
        
        // Add/remove scrolled class to header
        const header = document.querySelector('.arkify-header');
        if (header) {
            if (scrollTop > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        }
        
        ticking = false;
    }
    
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateScrollPosition);
            ticking = true;
        }
    });
    
    // Handle orientation change
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            // Recalculate viewport height
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }, 100);
    });
    
    // Set initial viewport height
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
}

/**
 * Enhanced mobile menu functionality
 */
function enhancedMobileMenu() {
    const mobileToggle = document.getElementById('mobile-toggle');
    const mobileNav = document.getElementById('mobile-nav');
    
    if (!mobileToggle || !mobileNav) return;
    
    // Add backdrop blur when menu is open
    mobileToggle.addEventListener('click', function() {
        setTimeout(() => {
            if (mobileNav.classList.contains('active')) {
                document.body.style.backdropFilter = 'blur(10px)';
            } else {
                document.body.style.backdropFilter = '';
            }
        }, 50);
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (mobileNav.classList.contains('active') && 
            !mobileNav.contains(e.target) && 
            !mobileToggle.contains(e.target)) {
            mobileToggle.click();
        }
    });
}

// Initialize enhanced mobile menu
enhancedMobileMenu();

// Add CSS for touch feedback
const style = document.createElement('style');
style.textContent = `
    .touch-active {
        transform: scale(0.95) !important;
        opacity: 0.8 !important;
        transition: all 0.1s ease !important;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .arkify-header.scrolled {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
    }
    
    @media (max-width: 768px) {
        .mobile-app-mode {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -webkit-tap-highlight-color: transparent;
        }
        
        .mobile-app-mode * {
            -webkit-tap-highlight-color: transparent;
        }
    }
`;
document.head.appendChild(style);
