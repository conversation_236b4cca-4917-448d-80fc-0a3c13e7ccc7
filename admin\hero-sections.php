<?php
/**
 * Hero Sections Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/hero-page-detection.php';

// Session is already started in functions.php

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_hero':
                try {
                    $db = Database::getConnection();
                    
                    // Handle background image upload
                    $background_image = $_POST['current_background_image'] ?? '';
                    if (isset($_FILES['background_image']) && $_FILES['background_image']['error'] === UPLOAD_ERR_OK) {
                        $upload_result = uploadFile($_FILES['background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload_result['success']) {
                            $background_image = $upload_result['url'];
                        } else {
                            $error = 'Image upload failed: ' . $upload_result['message'];
                            break;
                        }
                    }
                    
                    $stmt = $db->prepare("
                        UPDATE hero_sections SET
                            caption = ?, title = ?, description = ?,
                            button_text = ?, button_link = ?,
                            show_newsletter_input = ?, newsletter_placeholder = ?, newsletter_button_text = ?, newsletter_success_message = ?,
                            background_type = ?, background_image = ?, background_gradient = ?,
                            height_type = ?, height_custom = ?,
                            caption_color = ?, title_color = ?, description_color = ?,
                            button_bg_color = ?, button_text_color = ?, button_hover_bg_color = ?,
                            background_color = ?, background_opacity = ?,
                            updated_at = NOW()
                        WHERE id = ?
                    ");

                    $result = $stmt->execute([
                        sanitizeInput($_POST['caption']),
                        sanitizeInput($_POST['title']),
                        sanitizeInput($_POST['description']),
                        sanitizeInput($_POST['button_text'] ?? ''),
                        ($_POST['button_link'] ?? '') === 'custom' ? sanitizeInput($_POST['custom_button_link'] ?? '') : sanitizeInput($_POST['button_link'] ?? ''),
                        isset($_POST['show_newsletter_input']) ? 1 : 0,
                        sanitizeInput($_POST['newsletter_placeholder'] ?? 'Enter your email address'),
                        sanitizeInput($_POST['newsletter_button_text'] ?? 'Subscribe'),
                        sanitizeInput($_POST['newsletter_success_message'] ?? 'Thank you for subscribing!'),
                        sanitizeInput($_POST['background_type']),
                        $background_image,
                        sanitizeInput($_POST['background_gradient']),
                        sanitizeInput($_POST['height_type']),
                        $_POST['height_type'] === 'custom' ? intval($_POST['height_custom']) : null,
                        sanitizeInput($_POST['caption_color'] ?? '#ffffff'),
                        sanitizeInput($_POST['title_color'] ?? '#ffffff'),
                        sanitizeInput($_POST['description_color'] ?? '#ffffff'),
                        sanitizeInput($_POST['button_bg_color'] ?? '#E67E22'),
                        sanitizeInput($_POST['button_text_color'] ?? '#ffffff'),
                        sanitizeInput($_POST['button_hover_bg_color'] ?? '#d35400'),
                        sanitizeInput($_POST['background_color'] ?? '#000000'),
                        floatval($_POST['background_opacity'] ?? 0.6),
                        intval($_POST['hero_id'])
                    ]);
                    
                    if ($result) {
                        $message = 'Hero section updated successfully!';
                    } else {
                        $error = 'Failed to update hero section.';
                    }
                } catch (Exception $e) {
                    $error = 'Error: ' . $e->getMessage();
                }
                break;
                
            case 'toggle_active':
                try {
                    $db = Database::getConnection();
                    $stmt = $db->prepare("UPDATE hero_sections SET active = NOT active WHERE id = ?");
                    $result = $stmt->execute([intval($_POST['hero_id'])]);
                    
                    if ($result) {
                        $message = 'Hero section status updated!';
                    } else {
                        $error = 'Failed to update status.';
                    }
                } catch (Exception $e) {
                    $error = 'Error: ' . $e->getMessage();
                }
                break;

            case 'create_hero_for_page':
                try {
                    $page_name = sanitizeInput($_POST['page_name']);
                    $page_title = sanitizeInput($_POST['page_title']);

                    $hero_data = [
                        'caption' => sanitizeInput($_POST['caption']),
                        'title' => sanitizeInput($_POST['title']),
                        'description' => sanitizeInput($_POST['description']),
                        'button_text' => sanitizeInput($_POST['button_text'] ?? ''),
                        'button_link' => ($_POST['button_link'] ?? '') === 'custom' ? sanitizeInput($_POST['custom_button_link'] ?? '') : sanitizeInput($_POST['button_link'] ?? ''),
                        'show_newsletter_input' => isset($_POST['show_newsletter_input']) ? 1 : 0,
                        'newsletter_placeholder' => sanitizeInput($_POST['newsletter_placeholder'] ?? 'Enter your email address'),
                        'newsletter_button_text' => sanitizeInput($_POST['newsletter_button_text'] ?? 'Subscribe'),
                        'newsletter_success_message' => sanitizeInput($_POST['newsletter_success_message'] ?? 'Thank you for subscribing!'),
                        'height_type' => sanitizeInput($_POST['height_type']),
                        'caption_color' => sanitizeInput($_POST['caption_color'] ?? '#ffffff'),
                        'title_color' => sanitizeInput($_POST['title_color'] ?? '#ffffff'),
                        'description_color' => sanitizeInput($_POST['description_color'] ?? '#ffffff'),
                        'button_bg_color' => sanitizeInput($_POST['button_bg_color'] ?? '#E67E22'),
                        'button_text_color' => sanitizeInput($_POST['button_text_color'] ?? '#ffffff'),
                        'button_hover_bg_color' => sanitizeInput($_POST['button_hover_bg_color'] ?? '#d35400'),
                        'background_color' => sanitizeInput($_POST['background_color'] ?? '#000000'),
                        'background_opacity' => floatval($_POST['background_opacity'] ?? 0.6)
                    ];

                    if ($_POST['height_type'] === 'custom') {
                        $hero_data['height_custom'] = intval($_POST['height_custom']);
                    }

                    $result = createHeroSectionForPage($page_name, $page_title, $hero_data);

                    if ($result) {
                        $message = "Hero section created successfully for $page_title!";
                    } else {
                        $error = 'Failed to create hero section.';
                    }
                } catch (Exception $e) {
                    $error = 'Error: ' . $e->getMessage();
                }
                break;

            case 'scan_pages':
                try {
                    $new_pages = updateDetectedPages();
                    if (empty($new_pages)) {
                        $message = 'No new pages detected.';
                    } else {
                        $message = 'Found ' . count($new_pages) . ' new pages!';
                    }
                } catch (Exception $e) {
                    $error = 'Error scanning pages: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get all hero sections
try {
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM hero_sections ORDER BY page_name");
    $hero_sections = $stmt->fetchAll();
} catch (Exception $e) {
    $error = 'Error loading hero sections: ' . $e->getMessage();
    $hero_sections = [];
}

// Get pages without hero sections for dynamic creation
try {
    $pages_without_heroes = getPagesWithoutHeroSections();
} catch (Exception $e) {
    $pages_without_heroes = [];
}

// Get hero for editing
$edit_hero = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    try {
        $stmt = $db->prepare("SELECT * FROM hero_sections WHERE id = ?");
        $stmt->execute([intval($_GET['edit'])]);
        $edit_hero = $stmt->fetch();
    } catch (Exception $e) {
        $error = 'Error loading hero section: ' . $e->getMessage();
    }
}

// Get statistics
$stats = [
    'total' => count($hero_sections),
    'active' => count(array_filter($hero_sections, fn($h) => $h['active'] == 1)),
    'inactive' => count(array_filter($hero_sections, fn($h) => $h['active'] == 0)),
    'pages_without' => count($pages_without_heroes)
];

// Render the page using the new theme system
renderAdminPage('management', [
    'page_title' => 'Hero Sections Management',
    'page_icon' => 'fas fa-image',
    'page_description' => 'Manage hero sections that appear at the top of your pages to create impactful first impressions.',
    'management_title' => 'Hero Sections',
    'management_description' => 'Create and customize compelling hero sections with backgrounds, content, and call-to-action elements.',
    'management_actions' => [
        [
            'url' => '#',
            'label' => 'Scan for Pages',
            'class' => 'btn-outline-primary',
            'icon' => 'fas fa-search',
            'onclick' => 'document.getElementById("scanPagesForm").submit();'
        ]
    ],
    'show_search' => true,
    'search_placeholder' => 'Search hero sections by page, title, or content...',
    'search_target' => '.searchable-item',
    'show_table' => true,
    'table_title' => 'All Hero Sections',
    'table_content_file' => __DIR__ . '/theme/content/hero-sections-table.php',
    'show_stats' => true,
    'stats_cards' => [
        [
            'icon' => 'fas fa-image',
            'number' => $stats['total'],
            'label' => 'Total Heroes'
        ],
        [
            'icon' => 'fas fa-check-circle',
            'number' => $stats['active'],
            'label' => 'Active'
        ],
        [
            'icon' => 'fas fa-pause-circle',
            'number' => $stats['inactive'],
            'label' => 'Inactive'
        ],
        [
            'icon' => 'fas fa-plus-circle',
            'number' => $stats['pages_without'],
            'label' => 'Need Heroes'
        ]
    ],
    'custom_content_before_table' => function() use ($edit_hero, $pages_without_heroes) {
        include __DIR__ . '/theme/content/hero-sections-form.php';
        echo '<div class="mt-4"></div>';
        include __DIR__ . '/theme/content/hero-sections-create.php';

        // Hidden form for scan pages action
        echo '<form id="scanPagesForm" method="POST" style="display: none;">';
        echo '<input type="hidden" name="action" value="scan_pages">';
        echo '</form>';
    },
    'message' => $message,
    'error' => $error,
    'hero_sections' => $hero_sections,
    'edit_hero' => $edit_hero,
    'pages_without_heroes' => $pages_without_heroes
]);
?>
