<?php
/**
 * Services Table Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure services variable is available
if (!isset($services)) {
    $services = [];
}
?>

<?php if (empty($services)): ?>
    <div class="no-data-state">
        <div class="no-data-icon">
            <i class="fas fa-cogs"></i>
        </div>
        <h3>No services yet</h3>
        <p>Start showcasing your services by adding your first service with icon and detailed description.</p>
        <button type="button" class="btn btn-primary" onclick="toggleForm()">
            <i class="fas fa-plus me-2"></i>
            Add Your First Service
        </button>
    </div>
<?php else: ?>
    <div class="table-responsive">
        <table class="table table-hover table-striped">
            <thead>
                <tr>
                    <th width="50">#</th>
                    <th>Service</th>
                    <th>Description</th>
                    <th>Sort Order</th>
                    <th>Status</th>
                    <th>Date Added</th>
                    <th width="140">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($services as $index => $service): ?>
                <tr class="searchable-item" 
                    data-search="<?php echo htmlspecialchars(strtolower($service['title'] . ' ' . $service['description'] . ' ' . $service['content'])); ?>">
                    <td class="text-center">
                        <span class="text-muted"><?php echo $index + 1; ?></span>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <?php if ($service['icon']): ?>
                                <div class="service-icon me-3">
                                    <?php if (strpos($service['icon'], '.svg') !== false): ?>
                                        <img src="<?php echo ensureAbsoluteUrl($service['icon']); ?>" 
                                             alt="<?php echo htmlspecialchars($service['title']); ?>" 
                                             class="service-icon-img">
                                    <?php elseif (strpos($service['icon'], 'fa-') !== false): ?>
                                        <i class="fas <?php echo htmlspecialchars($service['icon']); ?> service-icon-font"></i>
                                    <?php else: ?>
                                        <img src="<?php echo ensureAbsoluteUrl($service['icon']); ?>" 
                                             alt="<?php echo htmlspecialchars($service['title']); ?>" 
                                             class="service-icon-img">
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="service-icon me-3">
                                    <div class="icon-placeholder d-flex align-items-center justify-content-center">
                                        <i class="fas fa-cog text-muted"></i>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($service['title']); ?></h6>
                                <small class="text-muted">
                                    Slug: <code><?php echo htmlspecialchars($service['slug']); ?></code>
                                </small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="service-description" style="max-width: 300px;">
                            <?php 
                            $description = htmlspecialchars($service['description']);
                            echo strlen($description) > 100 ? substr($description, 0, 100) . '...' : $description;
                            ?>
                            <?php if (strlen($service['description']) > 100): ?>
                                <button type="button" 
                                        class="btn btn-link btn-sm p-0 ms-1" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#serviceModal<?php echo $service['id']; ?>"
                                        title="View full description">
                                    <i class="fas fa-expand-alt"></i>
                                </button>
                            <?php endif; ?>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-secondary"><?php echo $service['sort_order']; ?></span>
                    </td>
                    <td>
                        <?php if ($service['active']): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Active
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary">
                                <i class="fas fa-pause-circle me-1"></i>
                                Inactive
                            </span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <small class="text-muted">
                            <?php echo date('M j, Y', strtotime($service['created_at'])); ?>
                        </small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" 
                                    class="btn btn-outline-primary" 
                                    data-bs-toggle="modal" 
                                    data-bs-target="#serviceModal<?php echo $service['id']; ?>"
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <a href="?edit=<?php echo $service['id']; ?>" 
                               class="btn btn-outline-secondary" 
                               title="Edit Service">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form method="POST" 
                                  style="display: inline;" 
                                  onsubmit="return confirm('Are you sure you want to delete this service? This action cannot be undone.');">
                                <input type="hidden" name="action" value="delete_service">
                                <input type="hidden" name="id" value="<?php echo $service['id']; ?>">
                                <button type="submit" 
                                        class="btn btn-outline-danger" 
                                        title="Delete Service">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <?php 
    $total_items = count($services);
    $per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
    include __DIR__ . '/../components/pagination.php'; 
    ?>
    
    <!-- Service Details Modals -->
    <?php foreach ($services as $service): ?>
        <div class="modal fade" id="serviceModal<?php echo $service['id']; ?>" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-cogs me-2"></i>
                            Service Details
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <?php if ($service['icon']): ?>
                                    <div class="service-icon-large mb-3">
                                        <?php if (strpos($service['icon'], '.svg') !== false): ?>
                                            <img src="<?php echo ensureAbsoluteUrl($service['icon']); ?>" 
                                                 alt="<?php echo htmlspecialchars($service['title']); ?>" 
                                                 class="img-fluid" 
                                                 style="max-width: 80px;">
                                        <?php elseif (strpos($service['icon'], 'fa-') !== false): ?>
                                            <i class="fas <?php echo htmlspecialchars($service['icon']); ?> fa-4x text-primary"></i>
                                        <?php else: ?>
                                            <img src="<?php echo ensureAbsoluteUrl($service['icon']); ?>" 
                                                 alt="<?php echo htmlspecialchars($service['title']); ?>" 
                                                 class="img-fluid" 
                                                 style="max-width: 80px;">
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                                <h5><?php echo htmlspecialchars($service['title']); ?></h5>
                                <p class="text-muted mb-3">
                                    <small>Slug: <code><?php echo htmlspecialchars($service['slug']); ?></code></small>
                                </p>
                            </div>
                            <div class="col-md-9">
                                <div class="mb-3">
                                    <h6>Description</h6>
                                    <p><?php echo nl2br(htmlspecialchars($service['description'])); ?></p>
                                </div>
                                
                                <?php if ($service['content']): ?>
                                <div class="mb-3">
                                    <h6>Detailed Content</h6>
                                    <div class="border rounded p-3 bg-light">
                                        <?php echo $service['content']; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Sort Order</label>
                                            <p><span class="badge bg-secondary"><?php echo $service['sort_order']; ?></span></p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Status</label>
                                            <p>
                                                <?php if ($service['active']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle me-1"></i>
                                                        Active
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-pause-circle me-1"></i>
                                                        Inactive
                                                    </span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Date Added</label>
                                    <p><?php echo date('F j, Y \a\t g:i A', strtotime($service['created_at'])); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="?edit=<?php echo $service['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>
                            Edit Service
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
<?php endif; ?>

<style>
.service-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.service-icon-img {
    max-width: 32px;
    max-height: 32px;
    object-fit: contain;
}

.service-icon-font {
    font-size: 1.5rem;
    color: var(--admin-accent-color);
}

.icon-placeholder {
    width: 40px;
    height: 40px;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 0.375rem;
}

.service-description {
    font-size: 0.875rem;
    line-height: 1.4;
}

.no-data-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-data-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
