<?php
/**
 * <PERSON> Header Template
 * Page header hero sections with titles, breadcrumbs, and backgrounds
 * Used at the top of pages for page headers
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Get hero header data from database
$page_name = $hero_page_name ?? basename($_SERVER['PHP_SELF'], '.php');
$hero_header = getHeroHeader($page_name);

if (!$hero_header || !$hero_header['active']) {
    return; // Don't show if no hero header or inactive
}

// Background styles
$background_style = '';
$overlay_style = '';

switch ($hero_header['background_type']) {
    case 'image':
        if (!empty($hero_header['background_image'])) {
            $background_style = "background-image: url('" . ensureAbsoluteUrl($hero_header['background_image']) . "');";
            // For images, apply overlay on top
            $overlay_style = "background-color: " . $hero_header['background_color'] . "; opacity: " . $hero_header['background_opacity'] . ";";
        }
        break;
        
    case 'gradient':
        if (!empty($hero_header['background_gradient'])) {
            $background_style = "background: " . $hero_header['background_gradient'] . ";";
        }
        break;
        
    case 'color':
        $background_style = "background-color: " . $hero_header['background_color'] . ";";
        break;
}

// Height settings
$height_style = '';
switch ($hero_header['height_type']) {
    case 'small': $height_style = 'min-height: 300px;'; break;
    case 'medium': $height_style = 'min-height: 400px;'; break;
    case 'large': $height_style = 'min-height: 600px;'; break;
    case 'custom': $height_style = 'min-height: ' . ($hero_header['height_custom'] ?? 400) . 'px;'; break;
    default: $height_style = 'min-height: 400px;';
}

// Color settings
$title_color = $hero_header['title_color'] ?? '#ffffff';
$subtitle_color = $hero_header['subtitle_color'] ?? '#ffffff';
$breadcrumb_color = $hero_header['breadcrumb_color'] ?? '#ffffff';

// Current page for breadcrumbs
$current_page_title = $hero_header['page_title'] ?? ucfirst($page_name);
?>

<section class="hero-header" style="<?php echo $background_style . $height_style; ?>">
    <?php if ($hero_header['background_type'] === 'image' && $overlay_style): ?>
        <div class="hero-header-overlay" style="<?php echo $overlay_style; ?>"></div>
    <?php endif; ?>
    
    <div class="container">
        <div class="hero-header-content">
            <?php if ($hero_header['show_breadcrumbs']): ?>
                <nav class="hero-breadcrumb" style="color: <?php echo $breadcrumb_color; ?>;">
                    <a href="<?php echo siteUrl(); ?>" style="color: <?php echo $breadcrumb_color; ?>;">Home</a>
                    <span class="breadcrumb-separator">›</span>
                    <span class="current-page"><?php echo htmlspecialchars($current_page_title); ?></span>
                </nav>
            <?php endif; ?>
            
            <h1 class="hero-header-title" style="color: <?php echo $title_color; ?>;">
                <?php echo htmlspecialchars($hero_header['page_title']); ?>
            </h1>
            
            <?php if (!empty($hero_header['subtitle'])): ?>
                <p class="hero-header-subtitle" style="color: <?php echo $subtitle_color; ?>;">
                    <?php echo htmlspecialchars($hero_header['subtitle']); ?>
                </p>
            <?php endif; ?>
            
            <?php if ($hero_header['show_cta_button'] && !empty($hero_header['cta_button_text'])): ?>
                <div class="hero-header-cta">
                    <a href="<?php echo htmlspecialchars($hero_header['cta_button_link']); ?>" 
                       class="hero-cta-button"
                       style="background-color: <?php echo $hero_header['cta_button_color']; ?>; 
                              color: <?php echo $hero_header['cta_button_text_color']; ?>;">
                        <?php echo htmlspecialchars($hero_header['cta_button_text']); ?>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M7 17L17 7M17 7H7M17 7V17"/>
                        </svg>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<style>
/* Hero Header Styles */
.hero-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: white;
    text-align: center;
}

.hero-header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-header-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.hero-breadcrumb {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.hero-breadcrumb a {
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.hero-breadcrumb a:hover {
    opacity: 0.8;
}

.breadcrumb-separator {
    margin: 0 0.5rem;
    opacity: 0.7;
}

.current-page {
    opacity: 0.8;
}

.hero-header-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-header-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-header-cta {
    margin-top: 2rem;
}

.hero-cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.hero-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 0.9;
}

.hero-cta-button svg {
    transition: transform 0.3s ease;
}

.hero-cta-button:hover svg {
    transform: translateX(2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-header-title {
        font-size: 2.5rem;
    }
    
    .hero-header-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-header-content {
        padding: 1.5rem 1rem;
    }
    
    .hero-breadcrumb {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .hero-header-title {
        font-size: 2rem;
    }
    
    .hero-header-subtitle {
        font-size: 1rem;
    }
    
    .hero-cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
}
</style>


