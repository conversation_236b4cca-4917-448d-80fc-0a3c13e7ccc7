<?php
/**
 * Check for Missing Admin Files
 */

echo "<h1>CHECKING MISSING ADMIN FILES</h1><pre>";

$admin_files = [
    'team.php',
    'testimonials.php', 
    'sliders.php'
];

echo "=== CHECKING ADMIN FILES ===\n\n";

foreach ($admin_files as $file) {
    $path = __DIR__ . '/../admin/' . $file;
    if (file_exists($path)) {
        echo "✅ EXISTS: admin/$file\n";
    } else {
        echo "❌ MISSING: admin/$file\n";
    }
}

echo "\n=== CURRENT ADMIN DIRECTORY ===\n";
$admin_dir = __DIR__ . '/../admin/';
$files = scandir($admin_dir);
foreach ($files as $file) {
    if ($file !== '.' && $file !== '..' && pathinfo($file, PATHINFO_EXTENSION) === 'php') {
        echo "- $file\n";
    }
}

echo "</pre>";
?>
