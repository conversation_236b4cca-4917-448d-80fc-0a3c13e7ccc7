<?php
/**
 * Reusable Hero CTA Template
 * Can be used across different pages with custom content
 * Enhanced with dynamic styling controls
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Include hero page detection functions
require_once __DIR__ . '/../includes/hero-page-detection.php';

// Determine page name for database lookup
$current_page = isset($hero_page_name) ? $hero_page_name : 'home';

// Get hero section from database or use custom data
$db_hero = getHeroSection($current_page);

// Use custom data if provided, otherwise use database data, then fallback to defaults
$hero_caption = isset($hero_data['caption']) ? $hero_data['caption'] :
                ($db_hero ? $db_hero['caption'] : getThemeOption('hero_cta_caption', "Ready to Build?"));

$hero_title = isset($hero_data['title']) ? $hero_data['title'] :
              ($db_hero ? $db_hero['title'] : getThemeOption('hero_cta_title', 'Ready to Get Started?'));

$hero_description = isset($hero_data['description']) ? $hero_data['description'] :
                    ($db_hero ? $db_hero['description'] : getThemeOption('hero_cta_description', "Let's transform your vision into reality with our innovative architectural solutions and expert craftsmanship."));

$hero_button_text = isset($hero_data['button_text']) ? $hero_data['button_text'] :
                    ($db_hero ? $db_hero['button_text'] : getThemeOption('hero_cta_button_text', 'Start Your Project'));

$hero_button_link = isset($hero_data['button_link']) ? $hero_data['button_link'] :
                    ($db_hero ? $db_hero['button_link'] : getThemeOption('hero_cta_button_link', 'contact'));

// Handle background and styling based on database settings or custom data
$background_style = '';
$background_class = 'cta-section';

// Get styling options from database hero section
$height_style = '';
$text_colors = [];
$button_colors = [];

if ($db_hero) {
    // Height styling
    $height_value = getHeroHeight($db_hero['height_type'] ?? 'medium', $db_hero['height_custom'] ?? null);
    $height_style = "min-height: $height_value;";

    // Text colors
    $text_colors = [
        'caption' => $db_hero['caption_color'] ?? '#ffffff',
        'title' => $db_hero['title_color'] ?? '#ffffff',
        'description' => $db_hero['description_color'] ?? '#ffffff'
    ];

    // Button colors
    $button_colors = [
        'background' => $db_hero['button_bg_color'] ?? '#E67E22',
        'text' => $db_hero['button_text_color'] ?? '#ffffff',
        'hover_bg' => $db_hero['button_hover_bg_color'] ?? '#d35400'
    ];

    // Background opacity
    $bg_opacity = $db_hero['background_opacity'] ?? 0.60;
} else {
    // Fallback values
    $height_style = 'min-height: 400px;';
    $text_colors = [
        'caption' => '#ffffff',
        'title' => '#ffffff',
        'description' => '#ffffff'
    ];
    $button_colors = [
        'background' => '#E67E22',
        'text' => '#ffffff',
        'hover_bg' => '#d35400'
    ];
    $bg_opacity = 0.60;
}

// Generate overlay with custom opacity
$overlay_style = "background: linear-gradient(135deg, rgba(0, 0, 0, $bg_opacity) 0%, rgba(0, 0, 0, " . ($bg_opacity * 0.5) . ") 100%);";

if ($db_hero) {
    if ($db_hero['background_type'] === 'image' && !empty($db_hero['background_image'])) {
        $background_style = "background-image: url('" . ensureAbsoluteUrl($db_hero['background_image']) . "'); background-size: cover; background-position: center;";
        $background_class .= ' cta-image-bg';
    } else {
        // Use gradient background with custom opacity
        $gradient = $db_hero['background_gradient'] ?: "linear-gradient(135deg, rgba(0, 0, 0, $bg_opacity) 0%, rgba(0, 0, 0, " . ($bg_opacity * 0.5) . ") 100%)";
        // Update gradient opacity if it contains rgba values
        if (strpos($gradient, 'rgba') !== false) {
            $gradient = preg_replace('/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/', "rgba($1, $2, $3, $bg_opacity)", $gradient);
        }
        $background_style = "background: $gradient;";
        $background_class .= ' cta-gradient-bg';
    }
} elseif (isset($hero_data['background']) && !empty($hero_data['background'])) {
    $background_style = "background-image: url('" . $hero_data['background'] . "'); background-size: cover; background-position: center;";
    $background_class .= ' cta-image-bg';
} else {
    // Default gradient with custom opacity
    $background_style = "background: linear-gradient(135deg, rgba(0, 0, 0, $bg_opacity) 0%, rgba(0, 0, 0, " . ($bg_opacity * 0.5) . ") 100%);";
    $background_class .= ' cta-gradient-bg';
}
?>

<!-- Dynamic Hero CTA Section -->
<section class="<?php echo $background_class; ?>" style="<?php echo $background_style; ?> <?php echo $height_style; ?> padding: 6rem 0; position: relative; color: white; display: flex; align-items: center;">
    <!-- Overlay for image backgrounds -->
    <?php if (strpos($background_class, 'cta-image-bg') !== false): ?>
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; <?php echo $overlay_style; ?> z-index: 1;"></div>
    <?php endif; ?>

    <div class="container" style="position: relative; z-index: 2; width: 100%;">
        <div class="hero-content" style="text-align: center; max-width: 600px; margin: 0 auto;">
            <?php if (!empty($hero_caption)): ?>
            <div class="caption" style="color: <?php echo $text_colors['caption']; ?>; font-size: 0.9rem; opacity: 0.8; margin-bottom: 0.5rem; text-transform: uppercase; letter-spacing: 1px;">
                <?php echo htmlspecialchars($hero_caption); ?>
            </div>
            <?php endif; ?>

            <h2 class="hero-title" style="color: <?php echo $text_colors['title']; ?>; font-size: 2.5rem; margin-bottom: 1rem; font-weight: 700;">
                <?php echo htmlspecialchars($hero_title); ?>
            </h2>

            <?php if (!empty($hero_description)): ?>
            <p class="hero-subtitle" style="color: <?php echo $text_colors['description']; ?>; font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; line-height: 1.6;">
                <?php echo htmlspecialchars($hero_description); ?>
            </p>
            <?php endif; ?>

            <a href="<?php
                // Handle external URLs, phone numbers, and email addresses
                if (strpos($hero_button_link, 'http') === 0 ||
                    strpos($hero_button_link, 'tel:') === 0 ||
                    strpos($hero_button_link, 'mailto:') === 0 ||
                    strpos($hero_button_link, '#') === 0) {
                    echo $hero_button_link;
                } else {
                    echo siteUrl($hero_button_link);
                }
            ?>"
               class="btn btn-primary hero-cta-button"
               style="display: inline-flex; align-items: center; gap: 0.5rem;
                      background: <?php echo $button_colors['background']; ?>;
                      color: <?php echo $button_colors['text']; ?>;
                      padding: 1rem 2rem; border-radius: 4px; text-decoration: none;
                      font-weight: 500; transition: all 0.3s ease;"
               onmouseover="this.style.background='<?php echo $button_colors['hover_bg']; ?>'"
               onmouseout="this.style.background='<?php echo $button_colors['background']; ?>'">
                <?php echo htmlspecialchars($hero_button_text); ?>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M7 17L17 7M17 7H7M17 7V17"/>
                </svg>
            </a>
        </div>
    </div>
</section>
