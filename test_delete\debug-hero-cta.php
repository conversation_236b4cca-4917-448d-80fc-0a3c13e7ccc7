<?php
/**
 * Debug Hero CTA Settings
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>HERO CTA DEBUG</h1><pre>";

try {
    $db = Database::getConnection();
    
    echo "=== CURRENT HERO CTA SETTINGS ===\n\n";
    
    // Get all hero CTA related theme options
    $hero_options = [
        'hero_cta_caption',
        'hero_cta_title', 
        'hero_cta_description',
        'hero_cta_button_text',
        'hero_cta_button_link',
        'hero_cta_background'
    ];
    
    foreach ($hero_options as $option) {
        $value = getThemeOption($option, 'NOT SET');
        echo "$option: $value\n";
    }
    
    echo "\n=== TESTING HERO CTA TEMPLATE ===\n";
    
    // Test with default values
    echo "Testing with default values...\n";
    $hero_data = []; // Empty array to test defaults
    
    ob_start();
    include __DIR__ . '/../templates/hero-cta.php';
    $output = ob_get_clean();
    
    echo "Template rendered successfully: " . (strlen($output) > 0 ? 'YES' : 'NO') . "\n";
    echo "Output length: " . strlen($output) . " characters\n";
    
    echo "\n=== TESTING WITH CUSTOM DATA ===\n";
    
    // Test with custom data
    $hero_data = [
        'caption' => 'Custom Caption',
        'title' => 'Custom Title',
        'description' => 'Custom description for testing',
        'button_text' => 'Custom Button',
        'button_link' => 'custom-link',
        'background' => 'https://example.com/bg.jpg'
    ];
    
    ob_start();
    include __DIR__ . '/../templates/hero-cta.php';
    $custom_output = ob_get_clean();
    
    echo "Custom template rendered: " . (strlen($custom_output) > 0 ? 'YES' : 'NO') . "\n";
    echo "Custom output length: " . strlen($custom_output) . " characters\n";
    
    echo "\n=== CHECKING WHERE HERO CTA IS USED ===\n";
    
    // Check which pages use the hero CTA template
    $pages_to_check = ['index.php', 'about.php', 'services.php', 'contact.php'];
    
    foreach ($pages_to_check as $page) {
        if (file_exists(__DIR__ . '/../' . $page)) {
            $content = file_get_contents(__DIR__ . '/../' . $page);
            if (strpos($content, 'hero-cta.php') !== false) {
                echo "$page: USES hero-cta template\n";
            } else {
                echo "$page: does NOT use hero-cta template\n";
            }
        }
    }
    
    echo "\n=== ADMIN INTERFACE CHECK ===\n";
    
    // Check if admin interface exists for hero CTA
    if (file_exists(__DIR__ . '/../admin/index.php')) {
        $admin_content = file_get_contents(__DIR__ . '/../admin/index.php');
        if (strpos($admin_content, 'hero_cta') !== false) {
            echo "Admin interface: INCLUDES hero CTA management\n";
        } else {
            echo "Admin interface: does NOT include hero CTA management\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
