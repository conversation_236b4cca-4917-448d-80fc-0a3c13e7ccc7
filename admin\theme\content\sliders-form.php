<?php
$edit_slider = $edit_slider ?? null;
$is_edit = !empty($edit_slider);
$form_title = $is_edit ? 'Edit Slider: ' . htmlspecialchars($edit_slider['title']) : 'Add New Slider';
$form_action = $is_edit ? 'update_slider' : 'add_slider';
?>

<?php if ($is_edit): ?>
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-edit"></i> <?php echo $form_title; ?></h5>
        <a href="sliders.php" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>
    <div class="card-body">
<?php else: ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-plus"></i> <?php echo $form_title; ?></h5>
    </div>
    <div class="card-body">
<?php endif; ?>

        <form method="POST" enctype="multipart/form-data" id="sliderForm">
            <input type="hidden" name="action" value="<?php echo $form_action; ?>">
            <?php if ($is_edit): ?>
                <input type="hidden" name="slider_id" value="<?php echo $edit_slider['id']; ?>">
                <input type="hidden" name="current_background_image" value="<?php echo htmlspecialchars($edit_slider['background_image']); ?>">
            <?php endif; ?>

            <div class="row">
                <!-- Left Column - Content -->
                <div class="col-lg-6">
                    <!-- Basic Content Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-align-left"></i> Content</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="<?php echo $is_edit ? htmlspecialchars($edit_slider['title']) : ''; ?>" 
                                       required placeholder="Enter slider title">
                            </div>

                            <div class="mb-3">
                                <label for="subtitle" class="form-label">Subtitle</label>
                                <textarea class="form-control" id="subtitle" name="subtitle" rows="2" 
                                          placeholder="Enter subtitle or description"><?php echo $is_edit ? htmlspecialchars($edit_slider['subtitle']) : ''; ?></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <label for="button_text" class="form-label">Button Text</label>
                                    <input type="text" class="form-control" id="button_text" name="button_text" 
                                           value="<?php echo $is_edit ? htmlspecialchars($edit_slider['button_text']) : 'Learn More'; ?>" 
                                           placeholder="Learn More">
                                </div>
                                <div class="col-md-6">
                                    <label for="button_link" class="form-label">Button Link</label>
                                    <select class="form-select" id="button_link" name="button_link" onchange="toggleCustomUrl()">
                                        <?php foreach ($available_pages as $value => $label): ?>
                                            <option value="<?php echo htmlspecialchars($value); ?>" 
                                                    <?php echo ($is_edit && $edit_slider['button_link'] === $value) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($label); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <input type="text" class="form-control mt-2" id="custom_url" name="custom_url" 
                                           placeholder="Enter custom URL" style="display: none;">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Background Image Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-image"></i> Background Image</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="background_image" class="form-label">
                                    Background Image <?php echo !$is_edit ? '<span class="text-danger">*</span>' : ''; ?>
                                </label>
                                <input type="file" class="form-control" id="background_image" name="background_image" 
                                       accept="image/*" <?php echo !$is_edit ? 'required' : ''; ?>>
                                <div class="form-text">Recommended: 1920x1080px or larger. Formats: JPG, PNG, WebP</div>
                            </div>

                            <?php if ($is_edit && !empty($edit_slider['background_image'])): ?>
                                <div class="current-image">
                                    <label class="form-label">Current Image:</label>
                                    <div class="border rounded p-2">
                                        <img src="<?php echo ensureAbsoluteUrl($edit_slider['background_image']); ?>" 
                                             alt="Current Background" class="img-fluid" style="max-height: 150px;">
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Layout & Alignment Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-align-center"></i> Layout & Alignment</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="content_alignment" class="form-label">Content Alignment</label>
                                    <select class="form-select" id="content_alignment" name="content_alignment">
                                        <option value="left" <?php echo ($is_edit && $edit_slider['content_alignment'] === 'left') ? 'selected' : ''; ?>>Left</option>
                                        <option value="center" <?php echo ($is_edit && $edit_slider['content_alignment'] === 'center') ? 'selected' : 'selected'; ?>>Center</option>
                                        <option value="right" <?php echo ($is_edit && $edit_slider['content_alignment'] === 'right') ? 'selected' : ''; ?>>Right</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="vertical_alignment" class="form-label">Vertical Alignment</label>
                                    <select class="form-select" id="vertical_alignment" name="vertical_alignment">
                                        <option value="top" <?php echo ($is_edit && $edit_slider['vertical_alignment'] === 'top') ? 'selected' : ''; ?>>Top</option>
                                        <option value="center" <?php echo ($is_edit && $edit_slider['vertical_alignment'] === 'center') ? 'selected' : 'selected'; ?>>Center</option>
                                        <option value="bottom" <?php echo ($is_edit && $edit_slider['vertical_alignment'] === 'bottom') ? 'selected' : ''; ?>>Bottom</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="padding_top" class="form-label">Padding Top (px)</label>
                                    <input type="number" class="form-control" id="padding_top" name="padding_top" 
                                           value="<?php echo $is_edit ? $edit_slider['padding_top'] : '90'; ?>" 
                                           min="0" max="500">
                                </div>
                                <div class="col-md-6">
                                    <label for="padding_bottom" class="form-label">Padding Bottom (px)</label>
                                    <input type="number" class="form-control" id="padding_bottom" name="padding_bottom" 
                                           value="<?php echo $is_edit ? $edit_slider['padding_bottom'] : '40'; ?>" 
                                           min="0" max="500">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Advanced Settings -->
                <div class="col-lg-6">
                    <!-- Typography Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-font"></i> Typography</h6>
                        </div>
                        <div class="card-body">
                            <!-- Title Typography -->
                            <h6 class="text-muted mb-2">Title Typography</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="title_font_size" class="form-label">Font Size (rem)</label>
                                    <input type="number" class="form-control" id="title_font_size" name="title_font_size" 
                                           value="<?php echo $is_edit ? $edit_slider['title_font_size'] : '3.5'; ?>" 
                                           step="0.1" min="0.5" max="10">
                                </div>
                                <div class="col-md-6">
                                    <label for="title_font_weight" class="form-label">Font Weight</label>
                                    <select class="form-select" id="title_font_weight" name="title_font_weight">
                                        <option value="300" <?php echo ($is_edit && $edit_slider['title_font_weight'] == 300) ? 'selected' : ''; ?>>Light (300)</option>
                                        <option value="400" <?php echo ($is_edit && $edit_slider['title_font_weight'] == 400) ? 'selected' : ''; ?>>Normal (400)</option>
                                        <option value="500" <?php echo ($is_edit && $edit_slider['title_font_weight'] == 500) ? 'selected' : ''; ?>>Medium (500)</option>
                                        <option value="600" <?php echo ($is_edit && $edit_slider['title_font_weight'] == 600) ? 'selected' : ''; ?>>Semi-Bold (600)</option>
                                        <option value="700" <?php echo ($is_edit && $edit_slider['title_font_weight'] == 700) ? 'selected' : 'selected'; ?>>Bold (700)</option>
                                        <option value="800" <?php echo ($is_edit && $edit_slider['title_font_weight'] == 800) ? 'selected' : ''; ?>>Extra Bold (800)</option>
                                        <option value="900" <?php echo ($is_edit && $edit_slider['title_font_weight'] == 900) ? 'selected' : ''; ?>>Black (900)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="title_font_family" class="form-label">Font Family</label>
                                    <select class="form-select" id="title_font_family" name="title_font_family">
                                        <?php foreach ($font_families as $font): ?>
                                            <option value="<?php echo htmlspecialchars($font['name']); ?>"
                                                    <?php echo ($is_edit && $edit_slider['title_font_family'] === $font['name']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($font['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="title_line_height" class="form-label">Line Height</label>
                                    <input type="number" class="form-control" id="title_line_height" name="title_line_height"
                                           value="<?php echo $is_edit ? $edit_slider['title_line_height'] : '1.2'; ?>"
                                           step="0.1" min="0.8" max="3">
                                </div>
                            </div>

                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="title_text_transform" class="form-label">Text Transform</label>
                                    <select class="form-select" id="title_text_transform" name="title_text_transform">
                                        <option value="none" <?php echo ($is_edit && $edit_slider['title_text_transform'] === 'none') ? 'selected' : 'selected'; ?>>None</option>
                                        <option value="uppercase" <?php echo ($is_edit && $edit_slider['title_text_transform'] === 'uppercase') ? 'selected' : ''; ?>>UPPERCASE</option>
                                        <option value="lowercase" <?php echo ($is_edit && $edit_slider['title_text_transform'] === 'lowercase') ? 'selected' : ''; ?>>lowercase</option>
                                        <option value="capitalize" <?php echo ($is_edit && $edit_slider['title_text_transform'] === 'capitalize') ? 'selected' : ''; ?>>Capitalize</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="title_break_type" class="form-label">Break Type</label>
                                    <select class="form-select" id="title_break_type" name="title_break_type">
                                        <option value="none" <?php echo ($is_edit && $edit_slider['title_break_type'] === 'none') ? 'selected' : 'selected'; ?>>No Breaking</option>
                                        <option value="character" <?php echo ($is_edit && $edit_slider['title_break_type'] === 'character') ? 'selected' : ''; ?>>Character Based</option>
                                        <option value="word" <?php echo ($is_edit && $edit_slider['title_break_type'] === 'word') ? 'selected' : ''; ?>>Word Based</option>
                                        <option value="manual" <?php echo ($is_edit && $edit_slider['title_break_type'] === 'manual') ? 'selected' : ''; ?>>Manual</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="title_break_limit" class="form-label">Break Limit</label>
                                    <input type="number" class="form-control" id="title_break_limit" name="title_break_limit"
                                           value="<?php echo $is_edit ? $edit_slider['title_break_limit'] : '50'; ?>"
                                           min="0" max="500">
                                    <div class="form-text">Characters/words before break</div>
                                </div>
                            </div>

                            <hr class="my-3">

                            <!-- Description Typography -->
                            <h6 class="text-muted mb-2">Description Typography</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="description_font_size" class="form-label">Font Size (rem)</label>
                                    <input type="number" class="form-control" id="description_font_size" name="description_font_size"
                                           value="<?php echo $is_edit ? $edit_slider['description_font_size'] : '1.25'; ?>"
                                           step="0.1" min="0.5" max="5">
                                </div>
                                <div class="col-md-6">
                                    <label for="description_font_weight" class="form-label">Font Weight</label>
                                    <select class="form-select" id="description_font_weight" name="description_font_weight">
                                        <option value="300" <?php echo ($is_edit && $edit_slider['description_font_weight'] == 300) ? 'selected' : ''; ?>>Light (300)</option>
                                        <option value="400" <?php echo ($is_edit && $edit_slider['description_font_weight'] == 400) ? 'selected' : 'selected'; ?>>Normal (400)</option>
                                        <option value="500" <?php echo ($is_edit && $edit_slider['description_font_weight'] == 500) ? 'selected' : ''; ?>>Medium (500)</option>
                                        <option value="600" <?php echo ($is_edit && $edit_slider['description_font_weight'] == 600) ? 'selected' : ''; ?>>Semi-Bold (600)</option>
                                        <option value="700" <?php echo ($is_edit && $edit_slider['description_font_weight'] == 700) ? 'selected' : ''; ?>>Bold (700)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="description_font_family" class="form-label">Font Family</label>
                                    <select class="form-select" id="description_font_family" name="description_font_family">
                                        <?php foreach ($font_families as $font): ?>
                                            <option value="<?php echo htmlspecialchars($font['name']); ?>"
                                                    <?php echo ($is_edit && $edit_slider['description_font_family'] === $font['name']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($font['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="description_line_height" class="form-label">Line Height</label>
                                    <input type="number" class="form-control" id="description_line_height" name="description_line_height"
                                           value="<?php echo $is_edit ? $edit_slider['description_line_height'] : '1.6'; ?>"
                                           step="0.1" min="0.8" max="3">
                                </div>
                            </div>

                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="description_text_transform" class="form-label">Text Transform</label>
                                    <select class="form-select" id="description_text_transform" name="description_text_transform">
                                        <option value="none" <?php echo ($is_edit && $edit_slider['description_text_transform'] === 'none') ? 'selected' : 'selected'; ?>>None</option>
                                        <option value="uppercase" <?php echo ($is_edit && $edit_slider['description_text_transform'] === 'uppercase') ? 'selected' : ''; ?>>UPPERCASE</option>
                                        <option value="lowercase" <?php echo ($is_edit && $edit_slider['description_text_transform'] === 'lowercase') ? 'selected' : ''; ?>>lowercase</option>
                                        <option value="capitalize" <?php echo ($is_edit && $edit_slider['description_text_transform'] === 'capitalize') ? 'selected' : ''; ?>>Capitalize</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="description_break_type" class="form-label">Break Type</label>
                                    <select class="form-select" id="description_break_type" name="description_break_type">
                                        <option value="none" <?php echo ($is_edit && $edit_slider['description_break_type'] === 'none') ? 'selected' : 'selected'; ?>>No Breaking</option>
                                        <option value="character" <?php echo ($is_edit && $edit_slider['description_break_type'] === 'character') ? 'selected' : ''; ?>>Character Based</option>
                                        <option value="word" <?php echo ($is_edit && $edit_slider['description_break_type'] === 'word') ? 'selected' : ''; ?>>Word Based</option>
                                        <option value="manual" <?php echo ($is_edit && $edit_slider['description_break_type'] === 'manual') ? 'selected' : ''; ?>>Manual</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="description_break_limit" class="form-label">Break Limit</label>
                                    <input type="number" class="form-control" id="description_break_limit" name="description_break_limit"
                                           value="<?php echo $is_edit ? $edit_slider['description_break_limit'] : '100'; ?>"
                                           min="0" max="1000">
                                    <div class="form-text">Characters/words before break</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Heights Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-arrows-alt-v"></i> Responsive Heights</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="height_desktop" class="form-label">Desktop (px)</label>
                                    <input type="number" class="form-control" id="height_desktop" name="height_desktop"
                                           value="<?php echo $is_edit ? $edit_slider['height_desktop'] : '500'; ?>"
                                           min="200" max="1200">
                                </div>
                                <div class="col-md-4">
                                    <label for="height_tablet" class="form-label">Tablet (px)</label>
                                    <input type="number" class="form-control" id="height_tablet" name="height_tablet"
                                           value="<?php echo $is_edit ? $edit_slider['height_tablet'] : '400'; ?>"
                                           min="200" max="1000">
                                </div>
                                <div class="col-md-4">
                                    <label for="height_mobile" class="form-label">Mobile (px)</label>
                                    <input type="number" class="form-control" id="height_mobile" name="height_mobile"
                                           value="<?php echo $is_edit ? $edit_slider['height_mobile'] : '300'; ?>"
                                           min="200" max="800">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Colors & Overlay Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-palette"></i> Colors & Overlay</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="text_color" class="form-label">Text Color</label>
                                    <input type="color" class="form-control form-control-color" id="text_color" name="text_color"
                                           value="<?php echo $is_edit ? $edit_slider['text_color'] : '#ffffff'; ?>">
                                </div>
                                <div class="col-md-4">
                                    <label for="overlay_color" class="form-label">Overlay Color</label>
                                    <input type="color" class="form-control form-control-color" id="overlay_color" name="overlay_color"
                                           value="<?php echo $is_edit ? $edit_slider['overlay_color'] : '#000000'; ?>">
                                </div>
                                <div class="col-md-4">
                                    <label for="overlay_opacity" class="form-label">Overlay Opacity</label>
                                    <input type="range" class="form-range" id="overlay_opacity" name="overlay_opacity"
                                           value="<?php echo $is_edit ? $edit_slider['overlay_opacity'] : '0.5'; ?>"
                                           min="0" max="1" step="0.1" oninput="updateOpacityValue(this.value)">
                                    <div class="form-text">Opacity: <span id="opacity_value"><?php echo $is_edit ? ($edit_slider['overlay_opacity'] * 100) : '50'; ?>%</span></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Animation Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-play"></i> Animation & Behavior</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="animation_type" class="form-label">Animation Type</label>
                                    <select class="form-select" id="animation_type" name="animation_type">
                                        <?php foreach ($animation_presets as $preset): ?>
                                            <option value="<?php echo htmlspecialchars($preset['value']); ?>"
                                                    <?php echo ($is_edit && $edit_slider['animation_type'] === $preset['value']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($preset['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="animation_duration" class="form-label">Duration (seconds)</label>
                                    <input type="number" class="form-control" id="animation_duration" name="animation_duration"
                                           value="<?php echo $is_edit ? $edit_slider['animation_duration'] : '5.0'; ?>"
                                           step="0.5" min="1" max="30">
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="auto_play" name="auto_play"
                                               <?php echo ($is_edit && $edit_slider['auto_play']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="auto_play">Auto Play</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="pause_on_hover" name="pause_on_hover"
                                               <?php echo ($is_edit && $edit_slider['pause_on_hover']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="pause_on_hover">Pause on Hover</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="show_navigation_dots" name="show_navigation_dots"
                                               <?php echo ($is_edit && $edit_slider['show_navigation_dots']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="show_navigation_dots">Show Navigation Dots</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Settings Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-cogs"></i> Advanced Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="title_char_limit" class="form-label">Title Character Limit</label>
                                    <input type="number" class="form-control" id="title_char_limit" name="title_char_limit"
                                           value="<?php echo $is_edit ? $edit_slider['title_char_limit'] : '50'; ?>"
                                           min="0" max="500">
                                    <div class="form-text">0 = No limit</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="description_char_limit" class="form-label">Description Character Limit</label>
                                    <input type="number" class="form-control" id="description_char_limit" name="description_char_limit"
                                           value="<?php echo $is_edit ? $edit_slider['description_char_limit'] : '100'; ?>"
                                           min="0" max="1000">
                                    <div class="form-text">0 = No limit</div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order"
                                           value="<?php echo $is_edit ? $edit_slider['sort_order'] : '0'; ?>"
                                           min="0" max="999">
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="active" name="active"
                                               <?php echo ($is_edit && $edit_slider['active']) ? 'checked' : 'checked'; ?>>
                                        <label class="form-check-label" for="active">Active</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end gap-2 mt-4">
                <a href="sliders.php" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> <?php echo $is_edit ? 'Update Slider' : 'Add Slider'; ?>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function toggleCustomUrl() {
    const buttonLink = document.getElementById('button_link');
    const customUrl = document.getElementById('custom_url');

    if (buttonLink.value === 'custom') {
        customUrl.style.display = 'block';
        customUrl.required = true;
    } else {
        customUrl.style.display = 'none';
        customUrl.required = false;
        customUrl.value = '';
    }
}

function updateOpacityValue(value) {
    document.getElementById('opacity_value').textContent = Math.round(value * 100) + '%';
}

// Initialize form on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleCustomUrl();

    // Set initial opacity value
    const opacitySlider = document.getElementById('overlay_opacity');
    if (opacitySlider) {
        updateOpacityValue(opacitySlider.value);
    }
});
</script>
