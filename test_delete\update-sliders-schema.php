<?php
/**
 * Update Sliders Table Schema
 * Adds new columns for enhanced slider functionality
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

try {
    $db = Database::getConnection();

    echo "=== UPDATING SLIDERS TABLE SCHEMA ===\n\n";

    // Check if new columns already exist
    $stmt = $db->query("DESCRIBE sliders");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    $new_columns = [
        'text_color' => "VARCHAR(7) DEFAULT '#ffffff' AFTER button_link",
        'overlay_color' => "VARCHAR(7) DEFAULT '#000000' AFTER text_color",
        'overlay_opacity' => "DECIMAL(3,2) DEFAULT 0.50 AFTER overlay_color",
        'title_char_limit' => "INT DEFAULT 50 AFTER overlay_opacity",
        'description_char_limit' => "INT DEFAULT 100 AFTER title_char_limit"
    ];

    foreach ($new_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $columns)) {
            $sql = "ALTER TABLE sliders ADD COLUMN $column_name $column_definition";
            $db->exec($sql);
            echo "✅ Added column: $column_name\n";
        } else {
            echo "ℹ️  Column already exists: $column_name\n";
        }
    }

    echo "\n=== UPDATING EXISTING SLIDERS WITH DEFAULT VALUES ===\n\n";

    // Update existing sliders with default values
    $update_sql = "
        UPDATE sliders SET
            text_color = COALESCE(text_color, '#ffffff'),
            overlay_color = COALESCE(overlay_color, '#000000'),
            overlay_opacity = COALESCE(overlay_opacity, 0.50),
            title_char_limit = COALESCE(title_char_limit, 50),
            description_char_limit = COALESCE(description_char_limit, 100)
        WHERE text_color IS NULL OR overlay_color IS NULL OR overlay_opacity IS NULL
           OR title_char_limit IS NULL OR description_char_limit IS NULL
    ";

    $result = $db->exec($update_sql);
    echo "✅ Updated $result existing sliders with default styling values\n";

    echo "\n=== ADDING TESTIMONIAL BACKGROUND IMAGE COLUMN ===\n\n";

    // Check if testimonials table needs background_image column
    $stmt = $db->query("DESCRIBE testimonials");
    $testimonial_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    if (!in_array('background_image', $testimonial_columns)) {
        $sql = "ALTER TABLE testimonials ADD COLUMN background_image VARCHAR(500) DEFAULT NULL AFTER client_image";
        $db->exec($sql);
        echo "✅ Added background_image column to testimonials table\n";
    } else {
        echo "ℹ️  background_image column already exists in testimonials table\n";
    }

    echo "\n=== SCHEMA UPDATE COMPLETE ===\n\n";
    echo "The sliders table now supports:\n";
    echo "- Custom text colors\n";
    echo "- Custom overlay colors\n";
    echo "- Adjustable overlay opacity\n";
    echo "- Title character limits\n";
    echo "- Description character limits\n";
    echo "\nThe testimonials table now supports:\n";
    echo "- Background images for each testimonial\n";
    echo "\nYou can now use the admin slider management page to customize these settings.\n";

} catch (Exception $e) {
    echo "❌ Error updating schema: " . $e->getMessage() . "\n";
}
?>
