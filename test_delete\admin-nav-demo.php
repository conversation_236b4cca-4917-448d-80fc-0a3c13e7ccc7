<?php
/**
 * Admin Navigation Demo
 * Visual demonstration of the updated admin navigation
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

$pageTitle = 'Admin Navigation Demo - Hero Headers Added';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .admin-sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem 0;
        }
        .nav-item {
            margin-bottom: 0.5rem;
        }
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            margin: 0 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }
        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-weight: 600;
        }
        .nav-link.hero-item {
            background: rgba(255, 255, 255, 0.05);
            border-left: 3px solid #ffd700;
        }
        .nav-link.hero-item:hover {
            background: rgba(255, 255, 255, 0.15);
            border-left-color: #ffed4e;
        }
        .admin-header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        .feature-highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }
        .comparison-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .badge-new {
            background: #28a745;
            color: white;
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Admin Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar">
                <div class="text-center mb-4">
                    <h4><i class="fas fa-cog me-2"></i>Admin Panel</h4>
                    <small class="text-light">Monolith Design</small>
                </div>
                
                <nav class="nav flex-column">
                    <?php 
                    $nav_items = getAdminNavigation();
                    foreach ($nav_items as $item): 
                        $isHeroItem = in_array($item['title'], ['Hero Sections', 'Hero Headers']);
                        $isNew = $item['title'] === 'Hero Headers';
                    ?>
                        <div class="nav-item">
                            <a href="<?php echo $item['url']; ?>" 
                               class="nav-link <?php echo $item['active'] ? 'active' : ''; ?> <?php echo $isHeroItem ? 'hero-item' : ''; ?>">
                                <i class="<?php echo $item['icon']; ?>"></i>
                                <?php echo $item['title']; ?>
                                <?php if ($isNew): ?>
                                    <span class="badge-new">NEW</span>
                                <?php endif; ?>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="admin-header">
                    <div class="container-fluid">
                        <h1><i class="fas fa-image me-2"></i>Admin Navigation Updated</h1>
                        <p class="mb-0 text-muted">Hero Headers successfully added to admin navigation</p>
                    </div>
                </div>
                
                <div class="container-fluid">
                    <!-- Feature Highlight -->
                    <div class="feature-highlight">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h3><i class="fas fa-star me-2"></i>Hero Headers Added to Admin Navigation</h3>
                                <p class="mb-0">The new Hero Headers admin interface is now accessible through the admin navigation menu, providing dedicated control for page header sections.</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <i class="fas fa-check-circle fa-4x text-success"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Navigation Comparison -->
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="comparison-card">
                                <h5><i class="fas fa-star text-warning me-2"></i>Hero Sections</h5>
                                <p class="text-muted mb-2">Footer Hero CTAs</p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Newsletter signup forms</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Call-to-action sections</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Before footer placement</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Glassmorphism styling</li>
                                </ul>
                                <a href="../admin/hero-sections.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-cog me-1"></i>Manage Hero Sections
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-lg-6">
                            <div class="comparison-card">
                                <h5><i class="fas fa-image text-primary me-2"></i>Hero Headers <span class="badge-new">NEW</span></h5>
                                <p class="text-muted mb-2">Page Header Sections</p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Page titles and subtitles</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Breadcrumb navigation</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Background images/gradients</li>
                                    <li><i class="fas fa-check text-success me-2"></i>CTA button integration</li>
                                </ul>
                                <a href="../admin/hero-headers.php" class="btn btn-primary btn-sm">
                                    <i class="fas fa-cog me-1"></i>Manage Hero Headers
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Navigation Items List -->
                    <div class="row">
                        <div class="col-12">
                            <div class="comparison-card">
                                <h5><i class="fas fa-list me-2"></i>Complete Admin Navigation</h5>
                                <p class="text-muted">All available admin navigation items:</p>
                                
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Icon</th>
                                                <th>Title</th>
                                                <th>URL</th>
                                                <th>Purpose</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($nav_items as $item): ?>
                                                <tr>
                                                    <td><i class="<?php echo $item['icon']; ?> text-primary"></i></td>
                                                    <td>
                                                        <?php echo $item['title']; ?>
                                                        <?php if ($item['title'] === 'Hero Headers'): ?>
                                                            <span class="badge-new">NEW</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><code><?php echo $item['url']; ?></code></td>
                                                    <td>
                                                        <?php
                                                        $purposes = [
                                                            'Theme Options' => 'Site-wide theme settings',
                                                            'Sliders' => 'Homepage slider management',
                                                            'Hero Sections' => 'Footer CTA sections',
                                                            'Hero Headers' => 'Page header sections',
                                                            'Services' => 'Service offerings management',
                                                            'Projects' => 'Portfolio project management',
                                                            'Team' => 'Team member profiles',
                                                            'Testimonials' => 'Client testimonials',
                                                            'Blog' => 'Blog post management',
                                                            'Contact' => 'Contact form submissions',
                                                            'Email Settings' => 'Email configuration'
                                                        ];
                                                        echo $purposes[$item['title']] ?? 'Admin functionality';
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($item['title'] === 'Hero Headers'): ?>
                                                            <span class="badge bg-success">Added</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary">Existing</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Implementation Summary -->
                    <div class="row">
                        <div class="col-12">
                            <div class="comparison-card">
                                <h5><i class="fas fa-code me-2"></i>Implementation Details</h5>
                                <p>The Hero Headers navigation item was added to the <code>getAdminNavigation()</code> function in <code>includes/functions.php</code>:</p>
                                
                                <div class="bg-light p-3 rounded">
                                    <pre><code>[
    'title' => 'Hero Headers',
    'url' => 'hero-headers.php',
    'icon' => 'fas fa-image',
    'active' => $current_page === 'hero-headers.php'
]</code></pre>
                                </div>
                                
                                <div class="mt-3">
                                    <h6>Key Features:</h6>
                                    <ul>
                                        <li><strong>Positioned after Hero Sections</strong> - Logical grouping of hero-related functionality</li>
                                        <li><strong>Unique Icon</strong> - <code>fas fa-image</code> to distinguish from Hero Sections</li>
                                        <li><strong>Active State Detection</strong> - Highlights when on the Hero Headers page</li>
                                        <li><strong>Consistent Styling</strong> - Matches existing admin navigation design</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
