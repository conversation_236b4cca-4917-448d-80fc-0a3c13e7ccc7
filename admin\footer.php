<?php
/**
 * Footer Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Session is already started in functions.php

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    redirect(siteUrl('admin/login.php'));
}

// Handle form submissions
$message = '';
$message_type = '';

if ($_POST) {
    if (isset($_POST['update_footer_settings'])) {
        // Update footer logo and tagline
        if (isset($_POST['footer_logo'])) {
            updateThemeOption('footer_logo', sanitizeInput($_POST['footer_logo']));
        }
        if (isset($_POST['site_logo_white'])) {
            updateThemeOption('site_logo_white', sanitizeInput($_POST['site_logo_white']));
        }
        if (isset($_POST['footer_tagline'])) {
            updateThemeOption('footer_tagline', sanitizeInput($_POST['footer_tagline']));
        }
        
        // Update footer sections visibility
        updateThemeOption('footer_show_company', isset($_POST['show_company']) ? '1' : '0');
        updateThemeOption('footer_show_services', isset($_POST['show_services']) ? '1' : '0');
        updateThemeOption('footer_show_projects', isset($_POST['show_projects']) ? '1' : '0');
        updateThemeOption('footer_show_contact', isset($_POST['show_contact']) ? '1' : '0');
        updateThemeOption('footer_show_newsletter', isset($_POST['show_newsletter']) ? '1' : '0');
        
        // Update footer sections
        if (isset($_POST['company_links'])) {
            updateThemeOption('footer_company_links', $_POST['company_links']);
        }
        
        if (isset($_POST['services_links'])) {
            updateThemeOption('footer_services_links', $_POST['services_links']);
        }
        
        if (isset($_POST['projects_links'])) {
            updateThemeOption('footer_projects_links', $_POST['projects_links']);
        }
        
        // Update section titles
        updateThemeOption('footer_company_title', sanitizeInput($_POST['company_title']));
        updateThemeOption('footer_services_title', sanitizeInput($_POST['services_title']));
        updateThemeOption('footer_projects_title', sanitizeInput($_POST['projects_title']));
        updateThemeOption('footer_contact_title', sanitizeInput($_POST['contact_title']));
        
        // Update contact information
        updateThemeOption('footer_contact_description', sanitizeInput($_POST['contact_description']));
        
        // Update newsletter settings
        updateThemeOption('newsletter_title', sanitizeInput($_POST['newsletter_title']));
        updateThemeOption('newsletter_description', sanitizeInput($_POST['newsletter_description']));
        
        $message = 'Footer settings updated successfully!';
        $message_type = 'success';
    }
}

// Get current footer settings
$footer_logo = getThemeOption('footer_logo', themeUrl('images/logo-white.svg'));
$company_title = getThemeOption('footer_company_title', 'Company');
$services_title = getThemeOption('footer_services_title', 'Services');
$projects_title = getThemeOption('footer_projects_title', 'Projects');

// Get current links (if stored as JSON)
$company_links = json_decode(getThemeOption('footer_company_links', ''), true) ?: [
    ['title' => 'Home', 'url' => ''],
    ['title' => 'About Us', 'url' => 'about'],
    ['title' => 'Our Services', 'url' => 'services'],
    ['title' => 'Our Team', 'url' => 'team'],
    ['title' => 'Projects', 'url' => 'projects'],
    ['title' => 'News & Insights', 'url' => 'news'],
    ['title' => 'Contact', 'url' => 'contact'],
];

$services_links = json_decode(getThemeOption('footer_services_links', ''), true) ?: [
    ['title' => 'Architectural Design', 'url' => 'service/architectural-design'],
    ['title' => 'Structural Engineering', 'url' => 'service/structural-engineering'],
    ['title' => 'Construction Management', 'url' => 'service/construction-management'],
    ['title' => 'Sustainable Design', 'url' => 'service/sustainable-design'],
];

$projects_links = json_decode(getThemeOption('footer_projects_links', ''), true) ?: [
    ['title' => 'All Projects', 'url' => 'projects'],
    ['title' => 'Featured Work', 'url' => 'work'],
    ['title' => 'Commercial', 'url' => 'projects'],
    ['title' => 'Residential', 'url' => 'projects'],
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Footer Management - Admin Panel</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <style>
        body { background: #f8f9fa; }
        .admin-header { background: #343a40; color: white; padding: 1rem 0; margin-bottom: 2rem; }
        .card { box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: none; margin-bottom: 1.5rem; }
        .card-header { background: #495057; color: white; font-weight: 600; }
        .link-group { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 10px; }
        .btn-add-link { background: #28a745; border: none; }
        .btn-remove-link { background: #dc3545; border: none; }
        .logo-preview { max-width: 200px; max-height: 60px; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="container">
            <h1 class="h3 mb-0">Footer Management</h1>
            <nav>
                <a href="index.php" class="text-light me-3">Dashboard</a>
                <a href="services.php" class="text-light me-3">Services</a>
                <a href="contacts.php" class="text-light me-3">Contacts</a>
                <a href="footer.php" class="text-light me-3"><strong>Footer</strong></a>
                <a href="logout.php" class="text-light">Logout</a>
            </nav>
        </div>
    </div>

    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <!-- Footer Logo Section -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Footer Logo</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="footer_logo" class="form-label">Footer Logo URL</label>
                                <input type="url" class="form-control" id="footer_logo" name="footer_logo" 
                                       value="<?php echo htmlspecialchars($footer_logo); ?>"
                                       placeholder="https://example.com/logo-white.svg">
                                <div class="form-text">Dedicated footer logo (white/light version for dark background).</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="site_logo_white" class="form-label">White Logo Fallback</label>
                                <input type="url" class="form-control" id="site_logo_white" name="site_logo_white" 
                                       value="<?php echo htmlspecialchars(getThemeOption('site_logo_white', themeUrl('images/logo-white.svg'))); ?>"
                                       placeholder="https://example.com/logo-white.svg">
                                <div class="form-text">Fallback white logo if no dedicated footer logo is set.</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="footer_tagline" class="form-label">Footer Tagline</label>
                        <textarea class="form-control" id="footer_tagline" name="footer_tagline" rows="2"
                                  placeholder="Enter your company tagline..."><?php echo htmlspecialchars(getThemeOption('footer_tagline', 'Crafting architectural excellence through innovative design and sustainable solutions.')); ?></textarea>
                    </div>
                    
                    <?php if ($footer_logo): ?>
                        <div class="mb-3">
                            <label class="form-label">Logo Preview</label><br>
                            <img src="<?php echo htmlspecialchars($footer_logo); ?>" alt="Footer Logo Preview" class="logo-preview">
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Footer Sections Visibility -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Footer Sections Control</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_company" name="show_company" 
                                       <?php echo getThemeOption('footer_show_company', '1') === '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_company">Show Company Section</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_services" name="show_services" 
                                       <?php echo getThemeOption('footer_show_services', '1') === '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_services">Show Services Section</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_projects" name="show_projects" 
                                       <?php echo getThemeOption('footer_show_projects', '1') === '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_projects">Show Projects Section</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_contact" name="show_contact" 
                                       <?php echo getThemeOption('footer_show_contact', '1') === '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_contact">Show Contact Section</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_newsletter" name="show_newsletter" 
                                       <?php echo getThemeOption('footer_show_newsletter', '1') === '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_newsletter">Show Newsletter Section</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Company Section -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Company Section</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="company_title" class="form-label">Section Title</label>
                        <input type="text" class="form-control" id="company_title" name="company_title" 
                               value="<?php echo htmlspecialchars($company_title); ?>">
                    </div>
                    
                    <label class="form-label">Menu Links</label>
                    <div id="company-links">
                        <?php foreach ($company_links as $index => $link): ?>
                            <div class="link-group">
                                <div class="row">
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" 
                                               name="company_links[<?php echo $index; ?>][title]" 
                                               placeholder="Link Title" 
                                               value="<?php echo htmlspecialchars($link['title']); ?>">
                                    </div>
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" 
                                               name="company_links[<?php echo $index; ?>][url]" 
                                               placeholder="URL (relative, e.g., 'about')" 
                                               value="<?php echo htmlspecialchars($link['url']); ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-remove-link btn-sm w-100" onclick="removeLink(this)">Remove</button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <button type="button" class="btn btn-add-link btn-sm" onclick="addLink('company')">Add Link</button>
                </div>
            </div>

            <!-- Services Section -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Services Section</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="services_title" class="form-label">Section Title</label>
                        <input type="text" class="form-control" id="services_title" name="services_title" 
                               value="<?php echo htmlspecialchars($services_title); ?>">
                    </div>
                    
                    <label class="form-label">Menu Links</label>
                    <div id="services-links">
                        <?php foreach ($services_links as $index => $link): ?>
                            <div class="link-group">
                                <div class="row">
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" 
                                               name="services_links[<?php echo $index; ?>][title]" 
                                               placeholder="Link Title" 
                                               value="<?php echo htmlspecialchars($link['title']); ?>">
                                    </div>
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" 
                                               name="services_links[<?php echo $index; ?>][url]" 
                                               placeholder="URL (relative, e.g., 'service/architectural-design')" 
                                               value="<?php echo htmlspecialchars($link['url']); ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-remove-link btn-sm w-100" onclick="removeLink(this)">Remove</button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <button type="button" class="btn btn-add-link btn-sm" onclick="addLink('services')">Add Link</button>
                </div>
            </div>

            <!-- Projects Section -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Projects Section</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="projects_title" class="form-label">Section Title</label>
                        <input type="text" class="form-control" id="projects_title" name="projects_title" 
                               value="<?php echo htmlspecialchars($projects_title); ?>">
                    </div>
                    
                    <label class="form-label">Menu Links</label>
                    <div id="projects-links">
                        <?php foreach ($projects_links as $index => $link): ?>
                            <div class="link-group">
                                <div class="row">
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" 
                                               name="projects_links[<?php echo $index; ?>][title]" 
                                               placeholder="Link Title" 
                                               value="<?php echo htmlspecialchars($link['title']); ?>">
                                    </div>
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" 
                                               name="projects_links[<?php echo $index; ?>][url]" 
                                               placeholder="URL (relative, e.g., 'projects')" 
                                               value="<?php echo htmlspecialchars($link['url']); ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-remove-link btn-sm w-100" onclick="removeLink(this)">Remove</button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <button type="button" class="btn btn-add-link btn-sm" onclick="addLink('projects')">Add Link</button>
                </div>
            </div>

            <!-- Contact Section -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Contact Section</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="contact_title" class="form-label">Section Title</label>
                        <input type="text" class="form-control" id="contact_title" name="contact_title" 
                               value="<?php echo htmlspecialchars(getThemeOption('footer_contact_title', 'Get In Touch')); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="contact_description" class="form-label">Contact Description</label>
                        <textarea class="form-control" id="contact_description" name="contact_description" rows="2"
                                  placeholder="Brief description for the contact section..."><?php echo htmlspecialchars(getThemeOption('footer_contact_description', 'Ready to start your next project? Get in touch with our team today.')); ?></textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <strong>Note:</strong> Contact details (address, phone, email) are managed in the main site settings and will automatically appear in the footer.
                    </div>
                </div>
            </div>

            <!-- Newsletter Section -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Newsletter Section</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="newsletter_title" class="form-label">Newsletter Title</label>
                        <input type="text" class="form-control" id="newsletter_title" name="newsletter_title" 
                               value="<?php echo htmlspecialchars(getThemeOption('newsletter_title', 'Stay Updated')); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="newsletter_description" class="form-label">Newsletter Description</label>
                        <textarea class="form-control" id="newsletter_description" name="newsletter_description" rows="2"
                                  placeholder="Description for newsletter signup..."><?php echo htmlspecialchars(getThemeOption('newsletter_description', 'Get the latest insights on architecture and design trends.')); ?></textarea>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" name="update_footer_settings" class="btn btn-primary btn-lg">Update Footer Settings</button>
            </div>
        </form>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        let linkCounters = {
            company: <?php echo count($company_links); ?>,
            services: <?php echo count($services_links); ?>,
            projects: <?php echo count($projects_links); ?>
        };

        function addLink(section) {
            const container = document.getElementById(section + '-links');
            const counter = linkCounters[section];
            
            const linkGroup = document.createElement('div');
            linkGroup.className = 'link-group';
            linkGroup.innerHTML = `
                <div class="row">
                    <div class="col-md-5">
                        <input type="text" class="form-control" 
                               name="${section}_links[${counter}][title]" 
                               placeholder="Link Title">
                    </div>
                    <div class="col-md-5">
                        <input type="text" class="form-control" 
                               name="${section}_links[${counter}][url]" 
                               placeholder="URL (relative)">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-remove-link btn-sm w-100" onclick="removeLink(this)">Remove</button>
                    </div>
                </div>
            `;
            
            container.appendChild(linkGroup);
            linkCounters[section]++;
        }

        function removeLink(button) {
            button.closest('.link-group').remove();
        }
    </script>
</body>
</html>
