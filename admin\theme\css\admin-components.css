/**
 * Admin Components CSS
 * Specific styling for admin dashboard components
 * Based on Bootstrap 5 with custom enhancements
 */

/* ===== ADMIN CARDS ===== */
.admin-card {
    background: white;
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-shadow-soft);
    border: none;
    margin-bottom: var(--admin-spacing-lg);
    overflow: hidden;
    transition: var(--admin-transition-medium);
}

.admin-card:hover {
    box-shadow: var(--admin-shadow-medium);
    transform: translateY(-2px);
}

.admin-card-header {
    background: linear-gradient(135deg, var(--admin-primary-color), #2c3e50);
    color: white;
    padding: var(--admin-spacing-md) var(--admin-spacing-lg);
    font-weight: 600;
    font-size: 1.1rem;
    border-bottom: none;
}

.admin-card-body {
    padding: var(--admin-spacing-lg);
}

.admin-card-footer {
    background: #f8f9fa;
    padding: var(--admin-spacing-md) var(--admin-spacing-lg);
    border-top: 1px solid #e9ecef;
}

/* ===== FORM COMPONENTS ===== */
.form-section {
    margin-bottom: var(--admin-spacing-xl);
    padding: var(--admin-spacing-lg);
    background: white;
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-shadow-soft);
}

.form-section-title {
    font-family: var(--admin-font-secondary);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--admin-primary-color);
    margin-bottom: var(--admin-spacing-md);
    padding-bottom: var(--admin-spacing-sm);
    border-bottom: 2px solid var(--admin-accent-color);
    display: flex;
    align-items: center;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--admin-spacing-md);
}

.form-group {
    margin-bottom: var(--admin-spacing-md);
}

.form-group label {
    font-weight: 500;
    color: var(--admin-primary-color);
    margin-bottom: var(--admin-spacing-xs);
    display: block;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--admin-border-radius);
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: var(--admin-transition-fast);
    background-color: white;
}

.form-control:focus {
    border-color: var(--admin-accent-color);
    box-shadow: 0 0 0 0.2rem rgba(230, 126, 34, 0.15);
    background-color: white;
}

.form-control:hover {
    border-color: #ced4da;
}

/* Color Input Groups */
.color-input-group {
    display: flex;
    gap: var(--admin-spacing-xs);
}

.color-picker {
    width: 60px;
    height: 45px;
    padding: 0;
    border: 2px solid #e9ecef;
    cursor: pointer;
}

.color-text {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

/* ===== BUTTONS ===== */
.btn {
    border-radius: var(--admin-border-radius);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--admin-transition-fast);
    border: none;
    display: inline-flex;
    align-items: center;
    gap: var(--admin-spacing-xs);
}

.btn-primary {
    background: linear-gradient(135deg, var(--admin-accent-color), #d35400);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #d35400, var(--admin-accent-color));
    transform: translateY(-1px);
    box-shadow: var(--admin-shadow-soft);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

/* ===== TABLES ===== */
.admin-table {
    background: white;
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-shadow-soft);
    overflow: hidden;
    margin-bottom: var(--admin-spacing-lg);
}

.admin-table .table {
    margin-bottom: 0;
}

.admin-table .table thead th {
    background: var(--admin-primary-color);
    color: white;
    font-weight: 600;
    border: none;
    padding: var(--admin-spacing-md);
}

.admin-table .table tbody td {
    padding: var(--admin-spacing-md);
    border-color: #f1f3f4;
    vertical-align: middle;
}

.admin-table .table tbody tr:hover {
    background-color: rgba(230, 126, 34, 0.05);
}

/* ===== ALERTS ===== */
.alert {
    border: none;
    border-radius: var(--admin-border-radius);
    padding: var(--admin-spacing-md) var(--admin-spacing-lg);
    margin-bottom: var(--admin-spacing-md);
    box-shadow: var(--admin-shadow-soft);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* ===== TABS ===== */
.admin-tabs {
    background: white;
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-shadow-soft);
    overflow: hidden;
    margin-bottom: var(--admin-spacing-lg);
}

.tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab {
    flex: 1;
    padding: var(--admin-spacing-md) var(--admin-spacing-lg);
    background: transparent;
    color: #6c757d;
    cursor: pointer;
    text-align: center;
    font-weight: 500;
    transition: var(--admin-transition-fast);
    border: none;
    border-bottom: 3px solid transparent;
}

.tab:hover {
    background: rgba(230, 126, 34, 0.05);
    color: var(--admin-accent-color);
}

.tab.active {
    background: white;
    color: var(--admin-accent-color);
    border-bottom-color: var(--admin-accent-color);
}

.tab-content {
    display: none;
    padding: var(--admin-spacing-lg);
}

.tab-content.active {
    display: block;
}

/* ===== BADGES ===== */
.badge {
    border-radius: calc(var(--admin-border-radius) / 2);
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

.badge-success {
    background: #28a745;
}

.badge-warning {
    background: #ffc107;
    color: #212529;
}

.badge-danger {
    background: #dc3545;
}

.badge-info {
    background: #17a2b8;
}

/* ===== DROPDOWNS ===== */
.dropdown-menu {
    border: none;
    box-shadow: var(--admin-shadow-medium);
    border-radius: var(--admin-border-radius);
    padding: var(--admin-spacing-xs) 0;
}

.dropdown-item {
    padding: var(--admin-spacing-xs) var(--admin-spacing-md);
    transition: var(--admin-transition-fast);
}

.dropdown-item:hover {
    background: rgba(230, 126, 34, 0.1);
    color: var(--admin-accent-color);
}

.dropdown-header {
    padding: var(--admin-spacing-sm) var(--admin-spacing-md);
    font-weight: 600;
    color: var(--admin-primary-color);
}

/* ===== UTILITIES ===== */
.text-accent {
    color: var(--admin-accent-color) !important;
}

.bg-accent {
    background-color: var(--admin-accent-color) !important;
}

.shadow-soft {
    box-shadow: var(--admin-shadow-soft) !important;
}

.shadow-medium {
    box-shadow: var(--admin-shadow-medium) !important;
}

.shadow-strong {
    box-shadow: var(--admin-shadow-strong) !important;
}

.rounded-admin {
    border-radius: var(--admin-border-radius) !important;
}

/* ===== LOADING STATES ===== */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--admin-accent-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
