<?php
/**
 * Hero Sections Table Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure hero_sections variable is available
if (!isset($hero_sections)) {
    $hero_sections = [];
}
?>

<?php if (empty($hero_sections)): ?>
    <div class="no-data-state">
        <div class="no-data-icon">
            <i class="fas fa-image"></i>
        </div>
        <h3>No hero sections yet</h3>
        <p>Hero sections appear at the top of pages to create impactful first impressions. Scan for pages to get started.</p>
        <form method="POST" style="display: inline;">
            <input type="hidden" name="action" value="scan_pages">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>
                Scan for Pages
            </button>
        </form>
    </div>
<?php else: ?>
    <div class="table-responsive">
        <table class="table table-hover table-striped">
            <thead>
                <tr>
                    <th width="50">#</th>
                    <th>Page</th>
                    <th>Hero Content</th>
                    <th>Background</th>
                    <th>Height</th>
                    <th>Status</th>
                    <th>Last Updated</th>
                    <th width="140">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($hero_sections as $index => $hero): ?>
                <tr class="searchable-item" 
                    data-search="<?php echo htmlspecialchars(strtolower($hero['page_title'] . ' ' . $hero['title'] . ' ' . $hero['caption'] . ' ' . $hero['description'])); ?>">
                    <td class="text-center">
                        <span class="text-muted"><?php echo $index + 1; ?></span>
                    </td>
                    <td>
                        <div class="page-info">
                            <h6 class="mb-1"><?php echo htmlspecialchars($hero['page_title']); ?></h6>
                            <small class="text-muted">
                                <code><?php echo htmlspecialchars($hero['page_name']); ?></code>
                            </small>
                        </div>
                    </td>
                    <td>
                        <div class="hero-content-preview">
                            <?php if ($hero['caption']): ?>
                                <div class="caption-preview">
                                    <small class="text-muted"><?php echo htmlspecialchars($hero['caption']); ?></small>
                                </div>
                            <?php endif; ?>
                            <div class="title-preview">
                                <strong><?php echo htmlspecialchars($hero['title']); ?></strong>
                            </div>
                            <?php if ($hero['description']): ?>
                                <div class="description-preview">
                                    <small class="text-muted">
                                        <?php 
                                        $description = htmlspecialchars($hero['description']);
                                        echo strlen($description) > 60 ? substr($description, 0, 60) . '...' : $description;
                                        ?>
                                    </small>
                                </div>
                            <?php endif; ?>
                            <?php if ($hero['button_text']): ?>
                                <div class="button-preview mt-1">
                                    <span class="badge bg-primary"><?php echo htmlspecialchars($hero['button_text']); ?></span>
                                </div>
                            <?php endif; ?>
                            <?php if ($hero['show_newsletter_input']): ?>
                                <div class="newsletter-preview mt-1">
                                    <span class="badge bg-info">
                                        <i class="fas fa-envelope me-1"></i>
                                        Newsletter Signup
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </td>
                    <td>
                        <div class="background-preview">
                            <?php if ($hero['background_type'] === 'gradient'): ?>
                                <div class="gradient-preview" 
                                     style="width: 50px; height: 30px; background: <?php echo htmlspecialchars($hero['background_gradient']); ?>; border-radius: 4px; border: 1px solid #dee2e6;">
                                </div>
                                <small class="d-block mt-1 text-muted">Gradient</small>
                            <?php else: ?>
                                <?php if ($hero['background_image']): ?>
                                    <img src="<?php echo ensureAbsoluteUrl($hero['background_image']); ?>" 
                                         alt="Background" 
                                         class="background-thumb"
                                         style="width: 50px; height: 30px; object-fit: cover; border-radius: 4px; border: 1px solid #dee2e6;">
                                    <small class="d-block mt-1 text-muted">Image</small>
                                <?php else: ?>
                                    <div class="no-background" 
                                         style="width: 50px; height: 30px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-image text-muted" style="font-size: 12px;"></i>
                                    </div>
                                    <small class="d-block mt-1 text-muted">No image</small>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </td>
                    <td>
                        <div class="height-info">
                            <?php 
                            $height_display = '';
                            switch($hero['height_type']) {
                                case 'small': $height_display = 'Small (300px)'; break;
                                case 'medium': $height_display = 'Medium (400px)'; break;
                                case 'large': $height_display = 'Large (600px)'; break;
                                case 'custom': $height_display = 'Custom (' . $hero['height_custom'] . 'px)'; break;
                                default: $height_display = 'Medium (400px)';
                            }
                            ?>
                            <span class="badge bg-secondary"><?php echo $height_display; ?></span>
                        </div>
                    </td>
                    <td>
                        <?php if ($hero['active']): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Active
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary">
                                <i class="fas fa-pause-circle me-1"></i>
                                Inactive
                            </span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <small class="text-muted">
                            <?php echo date('M j, Y', strtotime($hero['updated_at'])); ?>
                        </small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="?edit=<?php echo $hero['id']; ?>" 
                               class="btn btn-outline-primary" 
                               title="Edit Hero Section">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form method="POST" 
                                  style="display: inline;" 
                                  onsubmit="return confirm('Toggle status for this hero section?');">
                                <input type="hidden" name="action" value="toggle_active">
                                <input type="hidden" name="hero_id" value="<?php echo $hero['id']; ?>">
                                <button type="submit" 
                                        class="btn btn-outline-secondary" 
                                        title="<?php echo $hero['active'] ? 'Disable' : 'Enable'; ?> Hero Section">
                                    <i class="fas <?php echo $hero['active'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <?php 
    $total_items = count($hero_sections);
    $per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
    include __DIR__ . '/../components/pagination.php'; 
    ?>
<?php endif; ?>

<style>
.hero-content-preview {
    max-width: 250px;
}

.caption-preview {
    font-style: italic;
    margin-bottom: 2px;
}

.title-preview {
    margin-bottom: 4px;
}

.description-preview {
    line-height: 1.3;
    margin-bottom: 4px;
}

.button-preview .badge {
    font-size: 0.7rem;
}

.newsletter-preview .badge {
    font-size: 0.7rem;
}

.background-preview {
    text-align: center;
}

.gradient-preview {
    margin: 0 auto;
}

.background-thumb {
    margin: 0 auto;
    display: block;
}

.no-background {
    margin: 0 auto;
}

.height-info .badge {
    font-size: 0.7rem;
}

.page-info h6 {
    margin-bottom: 2px;
}

.no-data-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-data-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
