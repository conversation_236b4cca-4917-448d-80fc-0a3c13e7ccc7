<?php
/**
 * Test Hero Headers System
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>HERO HEADERS SYSTEM TEST</h1><pre>";

try {
    $db = Database::getConnection();
    
    // 1. Create table if it doesn't exist
    echo "=== STEP 1: CREATING HERO HEADERS TABLE ===\n";
    
    $stmt = $db->query("SHOW TABLES LIKE 'hero_headers'");
    $table_exists = $stmt->fetch();
    
    if (!$table_exists) {
        echo "📝 Creating hero_headers table...\n";
        
        $sql = "
        CREATE TABLE hero_headers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_name VARCHAR(100) NOT NULL UNIQUE,
            page_title VARCHAR(255),
            subtitle VARCHAR(255),
            show_breadcrumbs BOOLEAN DEFAULT TRUE,
            background_type ENUM('image', 'gradient', 'color') DEFAULT 'gradient',
            background_image VARCHAR(255),
            background_gradient TEXT DEFAULT 'linear-gradient(135deg, rgba(55, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)',
            background_color VARCHAR(7) DEFAULT '#a99eff',
            background_opacity DECIMAL(3,2) DEFAULT 0.60,
            height_type ENUM('small', 'medium', 'large', 'custom') DEFAULT 'medium',
            height_custom INT DEFAULT 400,
            title_color VARCHAR(7) DEFAULT '#ffffff',
            subtitle_color VARCHAR(7) DEFAULT '#ffffff',
            breadcrumb_color VARCHAR(7) DEFAULT '#ffffff',
            show_cta_button BOOLEAN DEFAULT FALSE,
            cta_button_text VARCHAR(100),
            cta_button_link VARCHAR(255),
            cta_button_color VARCHAR(7) DEFAULT '#E67E22',
            cta_button_text_color VARCHAR(7) DEFAULT '#ffffff',
            active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_page_name (page_name),
            INDEX idx_active (active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $db->exec($sql);
        echo "✅ hero_headers table created successfully\n";
    } else {
        echo "✅ hero_headers table already exists\n";
    }
    
    // 2. Insert test data
    echo "\n=== STEP 2: INSERTING TEST DATA ===\n";
    
    $test_pages = [
        [
            'page_name' => 'contact',
            'page_title' => 'Contact Us',
            'subtitle' => 'Get in touch to discuss your architectural project',
            'show_cta_button' => 1,
            'cta_button_text' => 'Schedule Consultation',
            'cta_button_link' => '/contact#form'
        ],
        [
            'page_name' => 'about',
            'page_title' => 'About Monolith Design',
            'subtitle' => 'Crafting architectural excellence through innovation and expertise',
            'background_type' => 'gradient',
            'background_gradient' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        ],
        [
            'page_name' => 'services',
            'page_title' => 'Our Services',
            'subtitle' => 'Professional architectural and engineering solutions',
            'height_type' => 'large'
        ]
    ];
    
    $stmt = $db->prepare("
        INSERT INTO hero_headers (
            page_name, page_title, subtitle, show_breadcrumbs, background_type, 
            background_gradient, background_color, background_opacity, height_type, 
            title_color, subtitle_color, breadcrumb_color, show_cta_button, 
            cta_button_text, cta_button_link, cta_button_color, cta_button_text_color, active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            page_title = VALUES(page_title),
            subtitle = VALUES(subtitle),
            updated_at = NOW()
    ");
    
    foreach ($test_pages as $page) {
        $stmt->execute([
            $page['page_name'],
            $page['page_title'],
            $page['subtitle'],
            $page['show_breadcrumbs'] ?? 1,
            $page['background_type'] ?? 'gradient',
            $page['background_gradient'] ?? 'linear-gradient(135deg, rgba(55, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)',
            $page['background_color'] ?? '#a99eff',
            $page['background_opacity'] ?? 0.60,
            $page['height_type'] ?? 'medium',
            $page['title_color'] ?? '#ffffff',
            $page['subtitle_color'] ?? '#ffffff',
            $page['breadcrumb_color'] ?? '#ffffff',
            $page['show_cta_button'] ?? 0,
            $page['cta_button_text'] ?? '',
            $page['cta_button_link'] ?? '',
            $page['cta_button_color'] ?? '#E67E22',
            $page['cta_button_text_color'] ?? '#ffffff',
            1
        ]);
        echo "  ✅ Created/updated hero header for: {$page['page_name']}\n";
    }
    
    // 3. Test the getHeroHeader function
    echo "\n=== STEP 3: TESTING getHeroHeader() FUNCTION ===\n";
    
    $test_page = 'contact';
    $hero_header = getHeroHeader($test_page);
    
    if ($hero_header) {
        echo "✅ Successfully retrieved hero header for '$test_page'\n";
        echo "   Title: {$hero_header['page_title']}\n";
        echo "   Subtitle: {$hero_header['subtitle']}\n";
        echo "   Background Type: {$hero_header['background_type']}\n";
        echo "   Height Type: {$hero_header['height_type']}\n";
        echo "   Show Breadcrumbs: " . ($hero_header['show_breadcrumbs'] ? 'Yes' : 'No') . "\n";
        echo "   Show CTA Button: " . ($hero_header['show_cta_button'] ? 'Yes' : 'No') . "\n";
        if ($hero_header['show_cta_button']) {
            echo "   CTA Button Text: {$hero_header['cta_button_text']}\n";
            echo "   CTA Button Link: {$hero_header['cta_button_link']}\n";
        }
    } else {
        echo "❌ Failed to retrieve hero header for '$test_page'\n";
    }
    
    // 4. Test non-existent page
    echo "\n=== STEP 4: TESTING NON-EXISTENT PAGE ===\n";
    
    $nonexistent_page = 'nonexistent';
    $hero_header = getHeroHeader($nonexistent_page);
    
    if ($hero_header === null) {
        echo "✅ Correctly returned null for non-existent page '$nonexistent_page'\n";
    } else {
        echo "❌ Unexpected result for non-existent page '$nonexistent_page'\n";
    }
    
    // 5. Show all hero headers
    echo "\n=== STEP 5: ALL HERO HEADERS ===\n";
    
    $stmt = $db->query("SELECT page_name, page_title, subtitle, background_type, height_type, active FROM hero_headers ORDER BY page_name");
    $all_headers = $stmt->fetchAll();
    
    printf("%-15s %-30s %-40s %-12s %-10s %-8s\n", "Page", "Title", "Subtitle", "Background", "Height", "Active");
    echo str_repeat("-", 110) . "\n";
    
    foreach ($all_headers as $header) {
        printf("%-15s %-30s %-40s %-12s %-10s %-8s\n", 
            $header['page_name'], 
            substr($header['page_title'], 0, 28),
            substr($header['subtitle'], 0, 38),
            $header['background_type'],
            $header['height_type'],
            $header['active'] ? 'Yes' : 'No'
        );
    }
    
    // 6. Template integration test
    echo "\n=== STEP 6: TEMPLATE INTEGRATION TEST ===\n";
    
    if (file_exists(__DIR__ . '/../templates/hero-header.php')) {
        echo "✅ Hero header template exists: templates/hero-header.php\n";
        
        // Test template loading (without output)
        ob_start();
        $hero_page_name = 'contact';
        include __DIR__ . '/../templates/hero-header.php';
        $template_output = ob_get_clean();
        
        if (!empty($template_output)) {
            echo "✅ Template generates output successfully\n";
            echo "   Output length: " . strlen($template_output) . " characters\n";
            
            // Check for key elements
            if (strpos($template_output, 'hero-header') !== false) {
                echo "   ✅ Contains hero-header class\n";
            }
            if (strpos($template_output, 'Contact Us') !== false) {
                echo "   ✅ Contains page title\n";
            }
            if (strpos($template_output, 'breadcrumb') !== false) {
                echo "   ✅ Contains breadcrumb navigation\n";
            }
        } else {
            echo "❌ Template did not generate output\n";
        }
    } else {
        echo "❌ Hero header template not found: templates/hero-header.php\n";
    }
    
    // 7. Admin interface check
    echo "\n=== STEP 7: ADMIN INTERFACE CHECK ===\n";
    
    if (file_exists(__DIR__ . '/../admin/hero-headers.php')) {
        echo "✅ Admin interface exists: admin/hero-headers.php\n";
        echo "   Access URL: http://localhost/monolith-design/admin/hero-headers.php\n";
    } else {
        echo "❌ Admin interface not found: admin/hero-headers.php\n";
    }
    
    echo "\n=== HERO HEADERS SYSTEM TEST COMPLETE ===\n";
    echo "✅ Hero Headers system is ready for use!\n";
    echo "\nNext steps:\n";
    echo "1. Access admin interface: /admin/hero-headers.php\n";
    echo "2. Add hero header template to pages: loadTemplate('hero-header', ['hero_page_name' => 'page_name'])\n";
    echo "3. Customize hero headers through admin panel\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>";
?>
