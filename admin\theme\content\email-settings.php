<?php
/**
 * Email Settings Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure settings variable is available
if (!isset($settings)) {
    $settings = [];
}
?>

<div class="admin-tabs">
    <!-- Tab Navigation -->
    <div class="tabs">
        <button class="tab active" onclick="showTab('smtp-settings')">
            <i class="fas fa-server me-2"></i>
            SMTP Settings
        </button>
        <button class="tab" onclick="showTab('email-config')">
            <i class="fas fa-envelope-open me-2"></i>
            Email Configuration
        </button>
        <button class="tab" onclick="showTab('test-email')">
            <i class="fas fa-paper-plane me-2"></i>
            Test Email
        </button>
    </div>

    <!-- SMTP Settings Tab -->
    <div id="smtp-settings" class="tab-content active">
        <form method="POST" class="form-section">
            <input type="hidden" name="action" value="update_email_settings">
            
            <h3 class="form-section-title">
                <i class="fas fa-server me-2"></i>
                SMTP Configuration
            </h3>
            
            <div class="form-group mb-3">
                <div class="form-check form-switch">
                    <input class="form-check-input" 
                           type="checkbox" 
                           id="smtp_enabled" 
                           name="smtp_enabled" 
                           value="1" 
                           <?php echo ($settings['smtp_enabled'] === '1') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="smtp_enabled">
                        <strong>Enable SMTP Email</strong>
                    </label>
                </div>
                <div class="form-text">
                    Enable this to use SMTP for sending emails instead of PHP's mail() function
                </div>
            </div>
            
            <div class="smtp-settings" <?php echo ($settings['smtp_enabled'] !== '1') ? 'style="opacity: 0.5;"' : ''; ?>>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="smtp_host" class="form-label">
                            <i class="fas fa-server me-1"></i>
                            SMTP Host <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="smtp_host" 
                               name="smtp_host" 
                               class="form-control" 
                               value="<?php echo htmlspecialchars($settings['smtp_host']); ?>"
                               placeholder="smtp.gmail.com">
                        <div class="form-text">Your SMTP server hostname</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_port" class="form-label">
                            <i class="fas fa-plug me-1"></i>
                            SMTP Port <span class="text-danger">*</span>
                        </label>
                        <input type="number" 
                               id="smtp_port" 
                               name="smtp_port" 
                               class="form-control" 
                               value="<?php echo htmlspecialchars($settings['smtp_port']); ?>"
                               placeholder="587">
                        <div class="form-text">Usually 587 for TLS or 465 for SSL</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_username" class="form-label">
                            <i class="fas fa-user me-1"></i>
                            SMTP Username <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="smtp_username" 
                               name="smtp_username" 
                               class="form-control" 
                               value="<?php echo htmlspecialchars($settings['smtp_username']); ?>"
                               placeholder="<EMAIL>">
                        <div class="form-text">Your SMTP username (usually your email)</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_password" class="form-label">
                            <i class="fas fa-lock me-1"></i>
                            SMTP Password <span class="text-danger">*</span>
                        </label>
                        <input type="password" 
                               id="smtp_password" 
                               name="smtp_password" 
                               class="form-control" 
                               value="<?php echo htmlspecialchars($settings['smtp_password']); ?>"
                               placeholder="Your SMTP password">
                        <div class="form-text">Your SMTP password or app-specific password</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="smtp_encryption" class="form-label">
                        <i class="fas fa-shield-alt me-1"></i>
                        Encryption Method
                    </label>
                    <select id="smtp_encryption" name="smtp_encryption" class="form-select">
                        <option value="tls" <?php echo ($settings['smtp_encryption'] === 'tls') ? 'selected' : ''; ?>>TLS (Recommended)</option>
                        <option value="ssl" <?php echo ($settings['smtp_encryption'] === 'ssl') ? 'selected' : ''; ?>>SSL</option>
                        <option value="none" <?php echo ($settings['smtp_encryption'] === 'none') ? 'selected' : ''; ?>>None</option>
                    </select>
                    <div class="form-text">TLS is recommended for most providers</div>
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save SMTP Settings
            </button>
        </form>
    </div>

    <!-- Email Configuration Tab -->
    <div id="email-config" class="tab-content">
        <form method="POST" class="form-section">
            <input type="hidden" name="action" value="update_email_settings">
            
            <h3 class="form-section-title">
                <i class="fas fa-envelope-open me-2"></i>
                Email Configuration
            </h3>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="from_email" class="form-label">
                        <i class="fas fa-at me-1"></i>
                        From Email Address <span class="text-danger">*</span>
                    </label>
                    <input type="email" 
                           id="from_email" 
                           name="from_email" 
                           class="form-control" 
                           value="<?php echo htmlspecialchars($settings['from_email']); ?>"
                           placeholder="<EMAIL>" 
                           required>
                    <div class="form-text">Email address that appears in the "From" field</div>
                </div>
                
                <div class="form-group">
                    <label for="from_name" class="form-label">
                        <i class="fas fa-signature me-1"></i>
                        From Name <span class="text-danger">*</span>
                    </label>
                    <input type="text" 
                           id="from_name" 
                           name="from_name" 
                           class="form-control" 
                           value="<?php echo htmlspecialchars($settings['from_name']); ?>"
                           placeholder="Monolith Design Co." 
                           required>
                    <div class="form-text">Name that appears in the "From" field</div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="admin_email" class="form-label">
                    <i class="fas fa-user-shield me-1"></i>
                    Admin Email Address <span class="text-danger">*</span>
                </label>
                <input type="email" 
                       id="admin_email" 
                       name="admin_email" 
                       class="form-control" 
                       value="<?php echo htmlspecialchars($settings['admin_email']); ?>"
                       placeholder="<EMAIL>" 
                       required>
                <div class="form-text">Email address where contact form submissions will be sent</div>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save Email Configuration
            </button>
        </form>
    </div>

    <!-- Test Email Tab -->
    <div id="test-email" class="tab-content">
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-paper-plane me-2"></i>
                Test Email Configuration
            </h3>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Test your email settings</strong><br>
                Send a test email to verify that your email configuration is working properly.
                Make sure to save your settings before testing.
            </div>
            
            <form method="POST" class="test-email-form">
                <input type="hidden" name="action" value="test_email">
                
                <div class="form-group">
                    <label for="test_email" class="form-label">
                        <i class="fas fa-envelope me-1"></i>
                        Test Email Address <span class="text-danger">*</span>
                    </label>
                    <input type="email" 
                           id="test_email" 
                           name="test_email" 
                           class="form-control" 
                           placeholder="Enter email address to send test to" 
                           required>
                    <div class="form-text">Enter the email address where you want to send the test email</div>
                </div>
                
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-paper-plane me-2"></i>
                    Send Test Email
                </button>
            </form>
            
            <div class="mt-4">
                <h4>Email Configuration Status</h4>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-label">SMTP Status:</div>
                        <div class="status-value">
                            <?php if ($settings['smtp_enabled'] === '1'): ?>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>
                                    Enabled
                                </span>
                            <?php else: ?>
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Using PHP mail()
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">From Email:</div>
                        <div class="status-value">
                            <code><?php echo htmlspecialchars($settings['from_email']); ?></code>
                        </div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">Admin Email:</div>
                        <div class="status-value">
                            <code><?php echo htmlspecialchars($settings['admin_email']); ?></code>
                        </div>
                    </div>
                    
                    <?php if ($settings['smtp_enabled'] === '1'): ?>
                    <div class="status-item">
                        <div class="status-label">SMTP Host:</div>
                        <div class="status-value">
                            <code><?php echo htmlspecialchars($settings['smtp_host']); ?>:<?php echo htmlspecialchars($settings['smtp_port']); ?></code>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.smtp-settings {
    transition: opacity 0.3s ease;
}

.status-grid {
    display: grid;
    gap: 1rem;
    margin-top: 1rem;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 0.375rem;
    border-left: 4px solid var(--bs-primary);
}

.status-label {
    font-weight: 600;
    color: #495057;
}

.status-value code {
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .status-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
</style>

<script>
// Toggle SMTP settings visibility
document.getElementById('smtp_enabled').addEventListener('change', function() {
    const smtpSettings = document.querySelector('.smtp-settings');
    if (this.checked) {
        smtpSettings.style.opacity = '1';
    } else {
        smtpSettings.style.opacity = '0.5';
    }
});
</script>
