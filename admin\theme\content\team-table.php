<?php
/**
 * Team Table Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure team_members variable is available
if (!isset($team_members)) {
    $team_members = [];
}
?>

<?php if (empty($team_members)): ?>
    <div class="no-data-state">
        <div class="no-data-icon">
            <i class="fas fa-users"></i>
        </div>
        <h3>No team members yet</h3>
        <p>Start building your team by adding your first team member with their photo and details.</p>
        <button type="button" class="btn btn-primary" onclick="toggleForm()">
            <i class="fas fa-plus me-2"></i>
            Add Your First Team Member
        </button>
    </div>
<?php else: ?>
    <div class="table-responsive">
        <table class="table table-hover table-striped">
            <thead>
                <tr>
                    <th width="50">#</th>
                    <th>Team Member</th>
                    <th>Position</th>
                    <th>Contact</th>
                    <th>Sort Order</th>
                    <th>Status</th>
                    <th>Date Added</th>
                    <th width="140">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($team_members as $index => $member): ?>
                <tr class="searchable-item" 
                    data-search="<?php echo htmlspecialchars(strtolower($member['name'] . ' ' . $member['position'] . ' ' . $member['email'] . ' ' . $member['bio'])); ?>">
                    <td class="text-center">
                        <span class="text-muted"><?php echo $index + 1; ?></span>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <?php if ($member['photo']): ?>
                                <div class="member-avatar me-3">
                                    <img src="<?php echo ensureAbsoluteUrl($member['photo']); ?>" 
                                         alt="<?php echo htmlspecialchars($member['name']); ?>" 
                                         class="rounded-circle">
                                </div>
                            <?php else: ?>
                                <div class="member-avatar me-3">
                                    <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center">
                                        <i class="fas fa-user text-muted"></i>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($member['name']); ?></h6>
                                <?php if ($member['bio']): ?>
                                    <small class="text-muted">
                                        <?php 
                                        $bio = htmlspecialchars($member['bio']);
                                        echo strlen($bio) > 50 ? substr($bio, 0, 50) . '...' : $bio;
                                        ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="fw-medium"><?php echo htmlspecialchars($member['position']); ?></span>
                    </td>
                    <td>
                        <div class="contact-info">
                            <?php if ($member['email']): ?>
                                <div class="mb-1">
                                    <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>" 
                                       class="text-decoration-none text-primary">
                                        <i class="fas fa-envelope me-1"></i>
                                        <small><?php echo htmlspecialchars($member['email']); ?></small>
                                    </a>
                                </div>
                            <?php endif; ?>
                            <?php if ($member['linkedin_url']): ?>
                                <div>
                                    <a href="<?php echo htmlspecialchars($member['linkedin_url']); ?>" 
                                       target="_blank" 
                                       class="text-decoration-none text-info">
                                        <i class="fab fa-linkedin me-1"></i>
                                        <small>LinkedIn</small>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-secondary"><?php echo $member['sort_order']; ?></span>
                    </td>
                    <td>
                        <?php if ($member['active']): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Active
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary">
                                <i class="fas fa-pause-circle me-1"></i>
                                Inactive
                            </span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <small class="text-muted">
                            <?php echo date('M j, Y', strtotime($member['created_at'])); ?>
                        </small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" 
                                    class="btn btn-outline-primary" 
                                    data-bs-toggle="modal" 
                                    data-bs-target="#memberModal<?php echo $member['id']; ?>"
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <a href="?edit=<?php echo $member['id']; ?>" 
                               class="btn btn-outline-secondary" 
                               title="Edit Member">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form method="POST" 
                                  style="display: inline;" 
                                  onsubmit="return confirm('Are you sure you want to delete this team member? This action cannot be undone.');">
                                <input type="hidden" name="action" value="delete_team_member">
                                <input type="hidden" name="member_id" value="<?php echo $member['id']; ?>">
                                <button type="submit" 
                                        class="btn btn-outline-danger" 
                                        title="Delete Member">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <?php 
    $total_items = count($team_members);
    $per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
    include __DIR__ . '/../components/pagination.php'; 
    ?>
    
    <!-- Team Member Details Modals -->
    <?php foreach ($team_members as $member): ?>
        <div class="modal fade" id="memberModal<?php echo $member['id']; ?>" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-user me-2"></i>
                            Team Member Details
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <?php if ($member['photo']): ?>
                                    <img src="<?php echo ensureAbsoluteUrl($member['photo']); ?>" 
                                         alt="<?php echo htmlspecialchars($member['name']); ?>" 
                                         class="img-fluid rounded-circle mb-3" 
                                         style="max-width: 200px;">
                                <?php else: ?>
                                    <div class="avatar-placeholder-large rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                                        <i class="fas fa-user fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                <h5><?php echo htmlspecialchars($member['name']); ?></h5>
                                <p class="text-muted mb-3"><?php echo htmlspecialchars($member['position']); ?></p>
                                
                                <div class="contact-links">
                                    <?php if ($member['email']): ?>
                                        <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>" 
                                           class="btn btn-outline-primary btn-sm me-2 mb-2">
                                            <i class="fas fa-envelope me-1"></i>
                                            Email
                                        </a>
                                    <?php endif; ?>
                                    <?php if ($member['linkedin_url']): ?>
                                        <a href="<?php echo htmlspecialchars($member['linkedin_url']); ?>" 
                                           target="_blank" 
                                           class="btn btn-outline-info btn-sm mb-2">
                                            <i class="fab fa-linkedin me-1"></i>
                                            LinkedIn
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <?php if ($member['bio']): ?>
                                <div class="mb-3">
                                    <h6>Biography</h6>
                                    <div class="border rounded p-3 bg-light">
                                        <?php echo nl2br(htmlspecialchars($member['bio'])); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Sort Order</label>
                                            <p><span class="badge bg-secondary"><?php echo $member['sort_order']; ?></span></p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Status</label>
                                            <p>
                                                <?php if ($member['active']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle me-1"></i>
                                                        Active
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-pause-circle me-1"></i>
                                                        Inactive
                                                    </span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Date Added</label>
                                    <p><?php echo date('F j, Y \a\t g:i A', strtotime($member['created_at'])); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="?edit=<?php echo $member['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>
                            Edit Member
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
<?php endif; ?>

<style>
.member-avatar img {
    width: 50px;
    height: 50px;
    object-fit: cover;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
}

.avatar-placeholder-large {
    width: 200px;
    height: 200px;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
}

.contact-info {
    font-size: 0.875rem;
}

.contact-links .btn {
    font-size: 0.8rem;
}

.no-data-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-data-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
