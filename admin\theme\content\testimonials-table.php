<?php
/**
 * Testimonials Table Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure testimonials variable is available
if (!isset($testimonials)) {
    $testimonials = [];
}
?>

<?php if (empty($testimonials)): ?>
    <div class="no-data-state">
        <div class="no-data-icon">
            <i class="fas fa-quote-right"></i>
        </div>
        <h3>No testimonials yet</h3>
        <p>Start collecting client testimonials to showcase your work and build trust.</p>
        <button type="button" class="btn btn-primary" onclick="toggleForm()">
            <i class="fas fa-plus me-2"></i>
            Add Your First Testimonial
        </button>
    </div>
<?php else: ?>
    <div class="table-responsive">
        <table class="table table-hover table-striped">
            <thead>
                <tr>
                    <th width="50">#</th>
                    <th>Client</th>
                    <th>Company</th>
                    <th>Position</th>
                    <th>Testimonial</th>
                    <th>Status</th>
                    <th>Date</th>
                    <th width="140">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($testimonials as $index => $testimonial): ?>
                <tr class="searchable-item" 
                    data-search="<?php echo htmlspecialchars(strtolower($testimonial['client_name'] . ' ' . $testimonial['client_company'] . ' ' . $testimonial['client_position'] . ' ' . $testimonial['quote'])); ?>">
                    <td class="text-center">
                        <span class="text-muted"><?php echo $index + 1; ?></span>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <?php if ($testimonial['client_image']): ?>
                                <div class="client-avatar me-3">
                                    <img src="<?php echo ensureAbsoluteUrl($testimonial['client_image']); ?>" 
                                         alt="<?php echo htmlspecialchars($testimonial['client_name']); ?>" 
                                         class="rounded-circle">
                                </div>
                            <?php endif; ?>
                            <div>
                                <h6 class="mb-0"><?php echo htmlspecialchars($testimonial['client_name']); ?></h6>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="fw-medium"><?php echo htmlspecialchars($testimonial['client_company']); ?></span>
                    </td>
                    <td>
                        <small class="text-muted"><?php echo htmlspecialchars($testimonial['client_position']); ?></small>
                    </td>
                    <td>
                        <div class="testimonial-preview" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            <?php 
                            $quote = htmlspecialchars($testimonial['quote']);
                            echo strlen($quote) > 80 ? substr($quote, 0, 80) . '...' : $quote;
                            ?>
                            <?php if (strlen($testimonial['quote']) > 80): ?>
                                <button type="button" 
                                        class="btn btn-link btn-sm p-0 ms-1" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#testimonialModal<?php echo $testimonial['id']; ?>"
                                        title="View full testimonial">
                                    <i class="fas fa-expand-alt"></i>
                                </button>
                            <?php endif; ?>
                        </div>
                    </td>
                    <td>
                        <?php if ($testimonial['active']): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Active
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary">
                                <i class="fas fa-pause-circle me-1"></i>
                                Inactive
                            </span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <small class="text-muted">
                            <?php echo date('M j, Y', strtotime($testimonial['created_at'])); ?>
                        </small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" 
                                    class="btn btn-outline-primary" 
                                    data-bs-toggle="modal" 
                                    data-bs-target="#testimonialModal<?php echo $testimonial['id']; ?>"
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <a href="?edit=<?php echo $testimonial['id']; ?>" 
                               class="btn btn-outline-secondary" 
                               title="Edit Testimonial">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form method="POST" 
                                  style="display: inline;" 
                                  onsubmit="return confirm('Are you sure you want to delete this testimonial? This action cannot be undone.');">
                                <input type="hidden" name="action" value="delete_testimonial">
                                <input type="hidden" name="id" value="<?php echo $testimonial['id']; ?>">
                                <button type="submit" 
                                        class="btn btn-outline-danger" 
                                        title="Delete Testimonial">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <?php 
    $total_items = count($testimonials);
    $per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
    include __DIR__ . '/../components/pagination.php'; 
    ?>
    
    <!-- Testimonial Details Modals -->
    <?php foreach ($testimonials as $testimonial): ?>
        <div class="modal fade" id="testimonialModal<?php echo $testimonial['id']; ?>" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-quote-right me-2"></i>
                            Testimonial Details
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <?php if ($testimonial['client_image']): ?>
                                    <img src="<?php echo ensureAbsoluteUrl($testimonial['client_image']); ?>" 
                                         alt="<?php echo htmlspecialchars($testimonial['client_name']); ?>" 
                                         class="img-fluid rounded-circle mb-3" 
                                         style="max-width: 150px;">
                                <?php endif; ?>
                                <h5><?php echo htmlspecialchars($testimonial['client_name']); ?></h5>
                                <p class="text-muted mb-1"><?php echo htmlspecialchars($testimonial['client_position']); ?></p>
                                <p class="fw-medium"><?php echo htmlspecialchars($testimonial['client_company']); ?></p>
                            </div>
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Testimonial</label>
                                    <div class="border rounded p-3 bg-light">
                                        <i class="fas fa-quote-left text-muted me-2"></i>
                                        <?php echo nl2br(htmlspecialchars($testimonial['quote'])); ?>
                                        <i class="fas fa-quote-right text-muted ms-2"></i>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Status</label>
                                            <p>
                                                <?php if ($testimonial['active']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle me-1"></i>
                                                        Active
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-pause-circle me-1"></i>
                                                        Inactive
                                                    </span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Date Added</label>
                                            <p><?php echo date('F j, Y \a\t g:i A', strtotime($testimonial['created_at'])); ?></p>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if ($testimonial['background_image']): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Background Image</label>
                                    <div>
                                        <img src="<?php echo ensureAbsoluteUrl($testimonial['background_image']); ?>" 
                                             alt="Background" 
                                             class="img-fluid rounded" 
                                             style="max-height: 200px;">
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="?edit=<?php echo $testimonial['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>
                            Edit Testimonial
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
<?php endif; ?>

<style>
.client-avatar img {
    width: 40px;
    height: 40px;
    object-fit: cover;
}

.testimonial-preview {
    font-style: italic;
    color: #6c757d;
}

.no-data-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-data-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
