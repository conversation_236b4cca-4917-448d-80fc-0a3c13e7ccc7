<?php
/**
 * Projects Form Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure edit_project variable is available
if (!isset($edit_project)) {
    $edit_project = null;
}
?>

<div class="admin-card">
    <div class="admin-card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas <?php echo $edit_project ? 'fa-edit' : 'fa-plus'; ?> me-2"></i>
            <?php echo $edit_project ? 'Edit Project' : 'Add New Project'; ?>
        </h5>
        <button type="button" 
                class="btn btn-outline-secondary btn-sm" 
                onclick="<?php echo $edit_project ? 'cancelForm()' : 'toggleForm()'; ?>">
            <i class="fas <?php echo $edit_project ? 'fa-times' : 'fa-plus'; ?> me-1"></i>
            <?php echo $edit_project ? 'Cancel Edit' : 'Add New Project'; ?>
        </button>
    </div>
    
    <div class="admin-card-body">
        <form method="POST" 
              enctype="multipart/form-data" 
              class="project-form show" 
              id="projectForm">
            <input type="hidden" name="MAX_FILE_SIZE" value="<?php echo MAX_FILE_SIZE; ?>">
            <input type="hidden" name="action" value="<?php echo $edit_project ? 'update_project' : 'add_project'; ?>">
            <?php if ($edit_project): ?>
                <input type="hidden" name="id" value="<?php echo $edit_project['id']; ?>">
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Main Content -->
                    <div class="form-group mb-3">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-1"></i>
                            Project Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               class="form-control" 
                               required 
                               value="<?php echo $edit_project ? htmlspecialchars($edit_project['title']) : ''; ?>"
                               placeholder="Enter project title">
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="slug" class="form-label">
                            <i class="fas fa-link me-1"></i>
                            URL Slug
                        </label>
                        <input type="text" 
                               id="slug" 
                               name="slug" 
                               class="form-control" 
                               value="<?php echo $edit_project ? htmlspecialchars($edit_project['slug']) : ''; ?>"
                               placeholder="project-url-slug (auto-generated if empty)">
                        <div class="form-text">
                            URL-friendly version of the title. Leave empty to auto-generate.
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-1"></i>
                            Short Description <span class="text-danger">*</span>
                        </label>
                        <textarea id="description" 
                                  name="description" 
                                  class="form-control" 
                                  required 
                                  rows="3"
                                  placeholder="Brief description of the project..."><?php echo $edit_project ? htmlspecialchars($edit_project['description']) : ''; ?></textarea>
                        <div class="form-text">
                            A brief summary that appears in project listings
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="content" class="form-label">
                            <i class="fas fa-file-alt me-1"></i>
                            Detailed Content
                        </label>
                        <textarea id="content" 
                                  name="content" 
                                  class="form-control" 
                                  rows="8"
                                  placeholder="Detailed project information, specifications, challenges, solutions, etc..."><?php echo $edit_project ? htmlspecialchars($edit_project['content']) : ''; ?></textarea>
                        <div class="form-text">
                            Detailed project information that appears on the project page
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="category" class="form-label">
                                    <i class="fas fa-tags me-1"></i>
                                    Category
                                </label>
                                <input type="text" 
                                       id="category" 
                                       name="category" 
                                       class="form-control" 
                                       value="<?php echo $edit_project ? htmlspecialchars($edit_project['category']) : ''; ?>"
                                       placeholder="e.g., Residential, Commercial, Industrial">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="client" class="form-label">
                                    <i class="fas fa-user-tie me-1"></i>
                                    Client Name
                                </label>
                                <input type="text" 
                                       id="client" 
                                       name="client" 
                                       class="form-control" 
                                       value="<?php echo $edit_project ? htmlspecialchars($edit_project['client']) : ''; ?>"
                                       placeholder="Client or company name">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="location" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    Location
                                </label>
                                <input type="text" 
                                       id="location" 
                                       name="location" 
                                       class="form-control" 
                                       value="<?php echo $edit_project ? htmlspecialchars($edit_project['location']) : ''; ?>"
                                       placeholder="City, State or Country">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="completion_date" class="form-label">
                                    <i class="fas fa-calendar-check me-1"></i>
                                    Completion Date
                                </label>
                                <input type="date" 
                                       id="completion_date" 
                                       name="completion_date" 
                                       class="form-control" 
                                       value="<?php echo $edit_project ? $edit_project['completion_date'] : ''; ?>">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- Sidebar Settings -->
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cog me-1"></i>
                                Settings
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="active" 
                                           name="active" 
                                           value="1" 
                                           <?php echo ($edit_project && $edit_project['active']) || !$edit_project ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="active">
                                        <strong>Active</strong>
                                    </label>
                                </div>
                                <div class="form-text">Show this project on the website</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-image me-1"></i>
                                Featured Image
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <?php if ($edit_project && $edit_project['featured_image']): ?>
                                <div class="current-image mb-3">
                                    <img src="<?php echo ensureAbsoluteUrl($edit_project['featured_image']); ?>" 
                                         alt="Current Featured Image" 
                                         class="img-fluid rounded">
                                    <small class="text-muted d-block mt-1">Current featured image</small>
                                </div>
                            <?php endif; ?>
                            <input type="file" 
                                   id="featured_image" 
                                   name="featured_image" 
                                   class="form-control" 
                                   accept="image/*">
                            <div class="form-text">
                                Main project image shown in listings<br>
                                Recommended: 800x600px or larger<br>
                                Formats: JPG, PNG, WebP
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-images me-1"></i>
                                Project Gallery
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <?php if ($edit_project && $edit_project['gallery']): ?>
                                <?php 
                                $gallery = json_decode($edit_project['gallery'], true);
                                if ($gallery && !empty($gallery)): 
                                ?>
                                <div class="current-gallery mb-3">
                                    <div class="row g-2">
                                        <?php foreach ($gallery as $image): ?>
                                        <div class="col-6">
                                            <img src="<?php echo ensureAbsoluteUrl($image); ?>" 
                                                 alt="Gallery Image" 
                                                 class="img-fluid rounded">
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <small class="text-muted d-block mt-1">Current gallery images</small>
                                </div>
                                <?php endif; ?>
                            <?php endif; ?>
                            <input type="file" 
                                   id="gallery_images" 
                                   name="gallery_images[]" 
                                   class="form-control" 
                                   accept="image/*" 
                                   multiple>
                            <div class="form-text">
                                Multiple images for project gallery<br>
                                Hold Ctrl/Cmd to select multiple files<br>
                                Recommended: 1200x800px or larger<br>
                                Formats: JPG, PNG, WebP
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2 mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas <?php echo $edit_project ? 'fa-save' : 'fa-plus'; ?> me-1"></i>
                    <?php echo $edit_project ? 'Update Project' : 'Add Project'; ?>
                </button>
                <button type="button" class="btn btn-secondary" onclick="cancelForm()">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.project-form {
    display: none;
}

.project-form.show {
    display: block;
}

.current-image img,
.current-gallery img {
    max-height: 150px;
    object-fit: cover;
}

.admin-card .admin-card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-text {
    font-size: 0.8rem;
}

.form-check-input:checked {
    background-color: var(--admin-accent-color);
    border-color: var(--admin-accent-color);
}
</style>
