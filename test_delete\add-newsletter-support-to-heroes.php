<?php
/**
 * Add Newsletter Support to Hero Sections
 * This adds email input support to hero sections for news pages
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>ADD NEWSLETTER SUPPORT TO HERO SECTIONS</h1><pre>";

try {
    $db = Database::getConnection();
    
    echo "=== ADDING NEWSLETTER FIELDS TO HERO SECTIONS TABLE ===\n\n";
    
    // Add newsletter fields to hero_sections table
    $newsletter_fields = [
        "ADD COLUMN show_newsletter_input TINYINT(1) DEFAULT 0 AFTER button_link",
        "ADD COLUMN newsletter_placeholder VARCHAR(255) DEFAULT 'Enter your email address' AFTER show_newsletter_input",
        "ADD COLUMN newsletter_button_text VARCHAR(100) DEFAULT 'Subscribe' AFTER newsletter_placeholder",
        "ADD COLUMN newsletter_success_message VARCHAR(255) DEFAULT 'Thank you for subscribing!' AFTER newsletter_button_text"
    ];
    
    foreach ($newsletter_fields as $field) {
        try {
            $sql = "ALTER TABLE hero_sections $field";
            $db->exec($sql);
            echo "✅ Added field: $field\n";
        } catch (Exception $e) {
            // Field might already exist
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "⚠️  Field already exists: $field\n";
            } else {
                echo "❌ Error adding field: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n=== UPDATING NEWS PAGE HEROES WITH NEWSLETTER SUPPORT ===\n\n";
    
    // Update news page hero to use newsletter
    $stmt = $db->prepare("
        UPDATE hero_sections 
        SET 
            caption = 'Stay Updated',
            title = 'Get the Latest News',
            description = 'Subscribe to receive the latest insights on architecture, design trends, and company updates delivered directly to your inbox.',
            button_text = '',
            button_link = '',
            show_newsletter_input = 1,
            newsletter_placeholder = 'Enter your email address',
            newsletter_button_text = 'Subscribe Now',
            newsletter_success_message = 'Thank you for subscribing! You will receive our latest updates.'
        WHERE page_name = 'news'
    ");
    
    if ($stmt->execute()) {
        echo "✅ Updated news page hero with newsletter support\n";
    } else {
        echo "❌ Failed to update news page hero\n";
    }
    
    // Update news-details page hero to use newsletter  
    $stmt = $db->prepare("
        INSERT INTO hero_sections (page_name, page_title, caption, title, description, button_text, button_link, show_newsletter_input, newsletter_placeholder, newsletter_button_text, newsletter_success_message, background_type, background_gradient) 
        VALUES ('news-details', 'News Details Page', 'Stay Updated', 'Get the Latest News', 'Subscribe to receive the latest insights on architecture, design trends, and company updates delivered directly to your inbox.', '', '', 1, 'Enter your email address', 'Subscribe Now', 'Thank you for subscribing! You will receive our latest updates.', 'gradient', 'linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)')
        ON DUPLICATE KEY UPDATE 
            caption = 'Stay Updated',
            title = 'Get the Latest News',
            description = 'Subscribe to receive the latest insights on architecture, design trends, and company updates delivered directly to your inbox.',
            button_text = '',
            button_link = '',
            show_newsletter_input = 1,
            newsletter_placeholder = 'Enter your email address',
            newsletter_button_text = 'Subscribe Now',
            newsletter_success_message = 'Thank you for subscribing! You will receive our latest updates.'
    ");
    
    if ($stmt->execute()) {
        echo "✅ Updated/created news-details page hero with newsletter support\n";
    } else {
        echo "❌ Failed to update news-details page hero\n";
    }
    
    echo "\n=== VERIFYING NEWSLETTER SUPPORT ===\n";
    
    $stmt = $db->query("SELECT page_name, title, show_newsletter_input, newsletter_button_text FROM hero_sections WHERE show_newsletter_input = 1");
    $newsletter_heroes = $stmt->fetchAll();
    
    foreach ($newsletter_heroes as $hero) {
        echo "✅ {$hero['page_name']}: Newsletter enabled - '{$hero['newsletter_button_text']}'\n";
    }
    
    echo "\n=== NEWSLETTER SUPPORT ADDED SUCCESSFULLY ===\n";
    echo "Newsletter-enabled heroes: " . count($newsletter_heroes) . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
