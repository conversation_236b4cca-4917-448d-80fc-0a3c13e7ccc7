<?php
/**
 * Newsletter Signup Handler
 * Handles newsletter subscriptions from hero CTA sections
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

$response = [
    'success' => false,
    'message' => 'Invalid request'
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $email = filter_var($_POST['email'] ?? '', FILTER_VALIDATE_EMAIL);
        $source = $_POST['source'] ?? 'unknown';
        $page = $_POST['page'] ?? 'unknown';
        
        if (!$email) {
            $response['message'] = 'Please enter a valid email address.';
        } else {
            $db = Database::getConnection();
            
            // Check if email already exists
            $stmt = $db->prepare("SELECT id FROM newsletter_subscribers WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->fetch()) {
                $response['message'] = 'This email is already subscribed to our newsletter.';
            } else {
                // Insert new subscriber
                $stmt = $db->prepare("
                    INSERT INTO newsletter_subscribers (email, source, page, subscribed_at, status) 
                    VALUES (?, ?, ?, NOW(), 'active')
                ");
                
                if ($stmt->execute([$email, $source, $page])) {
                    $response['success'] = true;
                    $response['message'] = 'Thank you for subscribing to our newsletter!';
                    
                    // Log the subscription
                    error_log("Newsletter subscription: $email from $source on $page");
                } else {
                    $response['message'] = 'Sorry, there was an error processing your subscription. Please try again.';
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("Newsletter signup error: " . $e->getMessage());
        $response['message'] = 'Sorry, there was an error processing your subscription. Please try again.';
    }
}

// If this is an AJAX request, return JSON
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    echo json_encode($response);
    exit;
}

// For regular form submissions, redirect back with message
$redirect_url = $_SERVER['HTTP_REFERER'] ?? siteUrl();
$message = urlencode($response['message']);
$status = $response['success'] ? 'success' : 'error';

header("Location: $redirect_url?newsletter_status=$status&newsletter_message=$message");
exit;
?>
