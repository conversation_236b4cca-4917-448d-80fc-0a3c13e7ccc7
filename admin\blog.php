<?php
/**
 * Blog/News Management - Admin Panel
 * Now using the new Admin Theme System
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Check admin authentication
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
            case 'add_post':
                $title = sanitizeInput($_POST['title']);
                $slug = createSlug($_POST['slug'] ?: $title);
                $excerpt = sanitizeInput($_POST['excerpt']);
                $content = $_POST['content']; // Keep HTML formatting
                $category = sanitizeInput($_POST['category']);
                $tags = sanitizeInput($_POST['tags']);
                $status = sanitizeInput($_POST['status']);
                $author = sanitizeInput($_POST['author'] ?: 'Monolith Design Team');
                $featured_image = '';
                
                // Handle featured image upload with debug output
                if (isset($_FILES['featured_image'])) {
                    if ($_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                        $upload = uploadFile($_FILES['featured_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload['success']) {
                            $featured_image = $upload['url'];
                        } else {
                            $error = 'Error uploading featured image: ' . $upload['message'] . '<br>Debug: ' . print_r($_FILES['featured_image'], true);
                        }
                    } else if ($_FILES['featured_image']['error'] !== UPLOAD_ERR_NO_FILE) {
                        $error = 'Featured image upload error: ' . $_FILES['featured_image']['error'] . '<br>Debug: ' . print_r($_FILES['featured_image'], true);
                    }
                }
                
                if (empty($error)) {
                    $stmt = $db->prepare("INSERT INTO blog_posts (title, slug, excerpt, content, category, tags, status, author, featured_image, published_at, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
                    $published_at = ($status === 'published') ? date('Y-m-d H:i:s') : null;

                    if ($stmt->execute([$title, $slug, $excerpt, $content, $category, $tags, $status, $author, $featured_image, $published_at])) {
                        $message = 'Blog post added successfully!';
                    } else {
                        $error = 'Error adding blog post to database.';
                    }
                }
                break;
                
            case 'update_post':
                $id = (int)$_POST['id'];
                $title = sanitizeInput($_POST['title']);
                $slug = createSlug($_POST['slug'] ?: $title);
                $excerpt = sanitizeInput($_POST['excerpt']);
                $content = $_POST['content'];
                $category = sanitizeInput($_POST['category']);
                $tags = sanitizeInput($_POST['tags']);
                $status = sanitizeInput($_POST['status']);
                $author = sanitizeInput($_POST['author']);
                
                // Initialize parameters for update query
                $featured_image_sql = '';
                $params = [$title, $slug, $excerpt, $content, $category, $tags, $status, $author];
                // Handle featured image upload with debug output
                if (isset($_FILES['featured_image'])) {
                    if ($_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                        $upload = uploadFile($_FILES['featured_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload['success']) {
                            $featured_image_sql = ', featured_image = ?';
                            $params[] = $upload['url'];
                        } else {
                            $error = 'Error uploading featured image: ' . $upload['message'] . '<br>Debug: ' . print_r($_FILES['featured_image'], true);
                            break;
                        }
                    } else if ($_FILES['featured_image']['error'] !== UPLOAD_ERR_NO_FILE) {
                        $error = 'Featured image upload error: ' . $_FILES['featured_image']['error'] . '<br>Debug: ' . print_r($_FILES['featured_image'], true);
                        break;
                    }
                }
                
                $published_at_sql = '';
                if ($status === 'published') {
                    // Get current post to check if it was draft
                    $stmt = $db->prepare("SELECT status, published_at FROM blog_posts WHERE id = ?");
                    $stmt->execute([$id]);
                    $current_post = $stmt->fetch();
                    
                    if ($current_post['status'] !== 'published' || !$current_post['published_at']) {
                        $published_at_sql = ', published_at = NOW()';
                    }
                }
                
                $params[] = $id;
                $stmt = $db->prepare("UPDATE blog_posts SET title = ?, slug = ?, excerpt = ?, content = ?, category = ?, tags = ?, status = ?, author = ?, updated_at = NOW(){$featured_image_sql}{$published_at_sql} WHERE id = ?");
                
                if ($stmt->execute($params)) {
                    $message = 'Blog post updated successfully!';
                } else {
                    $error = 'Error updating blog post.';
                }
                break;
                
            case 'delete_post':
                $id = (int)$_POST['id'];
                $stmt = $db->prepare("DELETE FROM blog_posts WHERE id = ?");
                if ($stmt->execute([$id])) {
                    $message = 'Blog post deleted successfully!';
                } else {
                    $error = 'Error deleting blog post.';
                }
                break;
            }
        } catch (Exception $e) {
            $error = 'Error processing request: ' . $e->getMessage();
        }
    }
}

// Get posts for listing
try {
    $posts = [];
    $stmt = $db->prepare("SELECT * FROM blog_posts ORDER BY created_at DESC");
    $stmt->execute();
    $posts = $stmt->fetchAll();

    // Get statistics
    $stats = [
        'total' => count($posts),
        'published' => count(array_filter($posts, fn($p) => $p['status'] === 'published')),
        'draft' => count(array_filter($posts, fn($p) => $p['status'] === 'draft')),
        'recent' => count(array_filter($posts, fn($p) => strtotime($p['created_at']) > strtotime('-30 days')))
    ];
} catch (Exception $e) {
    $error = 'Error loading blog posts: ' . $e->getMessage();
    $posts = [];
    $stats = ['total' => 0, 'published' => 0, 'draft' => 0, 'recent' => 0];
}

// Get post for editing if requested
$edit_post = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    try {
        $stmt = $db->prepare("SELECT * FROM blog_posts WHERE id = ?");
        $stmt->execute([$_GET['edit']]);
        $edit_post = $stmt->fetch();
    } catch (Exception $e) {
        $error = 'Error loading post for editing: ' . $e->getMessage();
    }
}

// Render the page using the new theme system
renderAdminPage('management', [
    'page_title' => 'Blog Management',
    'page_icon' => 'fas fa-blog',
    'page_description' => 'Create, edit, and manage blog posts and news articles for your website.',
    'management_title' => 'Blog & News Management',
    'management_description' => 'Create engaging content to share your expertise and company updates with website visitors.',
    'management_actions' => [
        [
            'url' => siteUrl('news'),
            'label' => 'View Blog',
            'class' => 'btn-outline-primary',
            'icon' => 'fas fa-external-link-alt',
            'target' => '_blank'
        ]
    ],
    'show_search' => true,
    'search_placeholder' => 'Search posts by title, category, or author...',
    'search_target' => '.searchable-item',
    'show_table' => true,
    'table_title' => 'All Blog Posts',
    'table_content_file' => __DIR__ . '/theme/content/blog-table.php',
    'show_stats' => true,
    'stats_cards' => [
        [
            'icon' => 'fas fa-blog',
            'number' => $stats['total'],
            'label' => 'Total Posts'
        ],
        [
            'icon' => 'fas fa-check-circle',
            'number' => $stats['published'],
            'label' => 'Published'
        ],
        [
            'icon' => 'fas fa-clock',
            'number' => $stats['draft'],
            'label' => 'Drafts'
        ],
        [
            'icon' => 'fas fa-calendar-alt',
            'number' => $stats['recent'],
            'label' => 'Recent (30d)'
        ]
    ],
    'custom_content_before_table' => function() use ($edit_post) {
        include __DIR__ . '/theme/content/blog-form.php';
    },
    'message' => $message,
    'error' => $error,
    'posts' => $posts,
    'edit_post' => $edit_post,
    'custom_js' => '
        function toggleForm() {
            const form = document.getElementById("postForm");
            if (form) {
                form.classList.toggle("show");
                if (form.classList.contains("show")) {
                    document.getElementById("title").focus();
                }
            }
        }

        function cancelForm() {
            window.location.href = "blog.php";
        }

        function generateSlug() {
            const title = document.getElementById("title").value;
            const slug = title.toLowerCase()
                .replace(/[^a-z0-9 -]/g, "")
                .replace(/\s+/g, "-")
                .replace(/-+/g, "-")
                .trim("-");
            document.getElementById("slug").value = slug;
        }

        // Auto-resize content textarea
        document.addEventListener("DOMContentLoaded", function() {
            const textarea = document.getElementById("content");
            if (textarea) {
                textarea.addEventListener("input", function() {
                    this.style.height = "auto";
                    this.style.height = this.scrollHeight + "px";
                });
            }
        });
    '
]);
?>
