<?php
/**
 * Test Newsletter Signup
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>NEWSLETTER SIGNUP TEST</h1><pre>";

try {
    // Simulate POST request
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_POST['email'] = '<EMAIL>';
    $_POST['source'] = 'hero_cta';
    $_POST['page'] = 'contact';
    
    echo "=== SIMULATING NEWSLETTER SIGNUP ===\n\n";
    echo "POST data:\n";
    print_r($_POST);
    
    // Include the newsletter signup script
    ob_start();
    include __DIR__ . '/../admin/newsletter-signup.php';
    $output = ob_get_clean();
    
    echo "\nScript output:\n";
    echo $output;
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
