<?php
/**
 * Add missing projects hero header
 */

require_once '../includes/config.php';
require_once '../includes/database.php';

try {
    $db = Database::getConnection();
    
    echo "<h2>Adding Projects Hero Header</h2>\n";
    
    // Check if projects hero header already exists
    $stmt = $db->prepare("SELECT id FROM hero_headers WHERE page_name = 'projects'");
    $stmt->execute();
    $existing = $stmt->fetch();
    
    if ($existing) {
        echo "<p>✅ Projects hero header already exists (ID: {$existing['id']})</p>\n";
    } else {
        // Insert projects hero header
        $stmt = $db->prepare("
            INSERT INTO hero_headers (
                page_name, page_title, subtitle, show_breadcrumbs,
                background_type, background_image, background_gradient, background_color, background_opacity,
                height_type, height_custom, title_color, subtitle_color, breadcrumb_color,
                show_cta_button, cta_button_text, cta_button_link, cta_button_color, cta_button_text_color,
                active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            'projects',                                                                                    // page_name
            'Our Projects',                                                                               // page_title
            'Explore our portfolio of innovative architectural solutions and completed projects',         // subtitle
            1,                                                                                           // show_breadcrumbs
            'gradient',                                                                                  // background_type
            '',                                                                                          // background_image
            'linear-gradient(135deg, rgba(55, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)',            // background_gradient
            '#a99eff',                                                                                   // background_color
            0.60,                                                                                        // background_opacity
            'medium',                                                                                    // height_type
            400,                                                                                         // height_custom
            '#ffffff',                                                                                   // title_color
            '#ffffff',                                                                                   // subtitle_color
            '#ffffff',                                                                                   // breadcrumb_color
            1,                                                                                           // show_cta_button
            'Start Your Project',                                                                        // cta_button_text
            'contact',                                                                                   // cta_button_link
            '#E67E22',                                                                                   // cta_button_color
            '#ffffff',                                                                                   // cta_button_text_color
            1                                                                                            // active
        ]);
        
        if ($result) {
            echo "<p>✅ Projects hero header created successfully!</p>\n";
        } else {
            echo "<p>❌ Failed to create projects hero header</p>\n";
        }
    }
    
    echo "<h3>Current Hero Headers:</h3>\n";
    $stmt = $db->query("SELECT page_name, page_title, active FROM hero_headers ORDER BY page_name");
    $headers = $stmt->fetchAll();
    
    echo "<ul>\n";
    foreach ($headers as $header) {
        $status = $header['active'] ? 'Active' : 'Inactive';
        echo "<li>{$header['page_name']}: {$header['page_title']} ({$status})</li>\n";
    }
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
}
?>
