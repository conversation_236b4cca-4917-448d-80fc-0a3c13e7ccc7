<?php
/**
 * Hero Data Flow Diagnostic Test
 * Traces the complete data flow from admin to frontend
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/hero-page-detection.php';

echo "<h1>Hero Data Flow Diagnostic</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 2rem; }
    .success { color: #27ae60; }
    .error { color: #e74c3c; }
    .info { color: #3498db; }
    .warning { color: #f39c12; }
    .section { margin: 2rem 0; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; }
    pre { background: #f8f9fa; padding: 1rem; border-radius: 4px; overflow-x: auto; }
    table { border-collapse: collapse; width: 100%; margin: 1rem 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

// Test 1: Database Connection and Table Structure
echo "<div class='section'>";
echo "<h2>🔍 Test 1: Database Connection & Table Structure</h2>";

try {
    $db = Database::getConnection();
    echo "<p class='success'>✅ Database connection successful</p>";
    
    // Check hero_sections table
    $stmt = $db->query('SHOW TABLES LIKE "hero_sections"');
    $hero_table_exists = $stmt->fetch();
    
    if ($hero_table_exists) {
        echo "<p class='success'>✅ hero_sections table exists</p>";
        
        // Get table structure
        $stmt = $db->query('DESCRIBE hero_sections');
        $columns = $stmt->fetchAll();
        $column_names = array_column($columns, 'Field');
        
        // Check for required columns
        $required_columns = ['page_name', 'title', 'description', 'active', 'background_type', 'background_gradient'];
        $missing_columns = array_diff($required_columns, $column_names);
        
        if (empty($missing_columns)) {
            echo "<p class='success'>✅ All required columns present</p>";
        } else {
            echo "<p class='error'>❌ Missing columns: " . implode(', ', $missing_columns) . "</p>";
        }
        
        // Check for enhanced columns
        $enhanced_columns = ['caption_color', 'title_color', 'description_color', 'button_bg_color', 'height_type'];
        $has_enhanced = array_intersect($enhanced_columns, $column_names);
        
        if (count($has_enhanced) > 0) {
            echo "<p class='info'>ℹ️ Enhanced columns found: " . implode(', ', $has_enhanced) . "</p>";
        } else {
            echo "<p class='warning'>⚠️ No enhanced columns found - table may need updating</p>";
        }
        
    } else {
        echo "<p class='error'>❌ hero_sections table does not exist!</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 2: Hero Data Retrieval
echo "<div class='section'>";
echo "<h2>🔍 Test 2: Hero Data Retrieval</h2>";

$test_pages = ['home', 'about', 'services', 'projects', 'contact', 'team'];

foreach ($test_pages as $page_name) {
    echo "<h3>Testing page: $page_name</h3>";
    
    // Test getHeroSection function
    $hero_data = getHeroSection($page_name);
    
    if ($hero_data) {
        echo "<p class='success'>✅ getHeroSection('$page_name') returned data</p>";
        echo "<pre>";
        echo "Title: " . htmlspecialchars($hero_data['title']) . "\n";
        echo "Caption: " . htmlspecialchars($hero_data['caption'] ?? 'N/A') . "\n";
        echo "Description: " . htmlspecialchars(substr($hero_data['description'] ?? 'N/A', 0, 100)) . "...\n";
        echo "Active: " . ($hero_data['active'] ? 'Yes' : 'No') . "\n";
        echo "Background Type: " . ($hero_data['background_type'] ?? 'N/A') . "\n";
        echo "Updated: " . ($hero_data['updated_at'] ?? 'N/A') . "\n";
        echo "</pre>";
    } else {
        echo "<p class='error'>❌ getHeroSection('$page_name') returned null</p>";
        
        // Check if record exists but is inactive
        try {
            $stmt = $db->prepare("SELECT * FROM hero_sections WHERE page_name = ?");
            $stmt->execute([$page_name]);
            $inactive_hero = $stmt->fetch();
            
            if ($inactive_hero) {
                echo "<p class='warning'>⚠️ Hero exists but is inactive (active = {$inactive_hero['active']})</p>";
            } else {
                echo "<p class='info'>ℹ️ No hero record found for page '$page_name'</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error checking inactive heroes: " . $e->getMessage() . "</p>";
        }
    }
}
echo "</div>";

// Test 3: Frontend Template Integration
echo "<div class='section'>";
echo "<h2>🔍 Test 3: Frontend Template Integration</h2>";

// Test hero-cta.php template logic
echo "<h3>Testing hero-cta.php template logic</h3>";

// Simulate different page contexts
$test_contexts = [
    ['hero_page_name' => 'home', 'description' => 'Homepage context'],
    ['hero_page_name' => 'about', 'description' => 'About page context'],
    ['hero_page_name' => 'services', 'description' => 'Services page context']
];

foreach ($test_contexts as $context) {
    echo "<h4>{$context['description']}</h4>";
    
    $hero_page_name = $context['hero_page_name'];
    $current_page = $hero_page_name;
    
    // Get hero section from database
    $db_hero = getHeroSection($current_page);
    
    // Simulate template logic
    $hero_caption = $db_hero ? $db_hero['caption'] : getThemeOption('hero_cta_caption', "Ready to Build?");
    $hero_title = $db_hero ? $db_hero['title'] : getThemeOption('hero_cta_title', 'Ready to Get Started?');
    $hero_description = $db_hero ? $db_hero['description'] : getThemeOption('hero_cta_description', "Let's transform your vision into reality...");
    
    echo "<pre>";
    echo "Page: $current_page\n";
    echo "DB Hero Found: " . ($db_hero ? 'Yes' : 'No') . "\n";
    echo "Final Caption: " . htmlspecialchars($hero_caption) . "\n";
    echo "Final Title: " . htmlspecialchars($hero_title) . "\n";
    echo "Final Description: " . htmlspecialchars(substr($hero_description, 0, 80)) . "...\n";
    echo "</pre>";
}
echo "</div>";

// Test 4: Admin Form Processing
echo "<div class='section'>";
echo "<h2>🔍 Test 4: Admin Form Processing</h2>";

// Check if admin functions exist
if (function_exists('createHeroSectionForPage')) {
    echo "<p class='success'>✅ createHeroSectionForPage() function exists</p>";
} else {
    echo "<p class='error'>❌ createHeroSectionForPage() function missing</p>";
}

if (function_exists('getPagesWithoutHeroSections')) {
    echo "<p class='success'>✅ getPagesWithoutHeroSections() function exists</p>";
    
    try {
        $pages_without_heroes = getPagesWithoutHeroSections();
        echo "<p class='info'>ℹ️ Pages without heroes: " . count($pages_without_heroes) . "</p>";
        if (!empty($pages_without_heroes)) {
            echo "<ul>";
            foreach ($pages_without_heroes as $page) {
                echo "<li>{$page['page_name']} - {$page['page_title']}</li>";
            }
            echo "</ul>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error getting pages without heroes: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class='error'>❌ getPagesWithoutHeroSections() function missing</p>";
}

echo "</div>";

// Test 5: Recent Changes Check
echo "<div class='section'>";
echo "<h2>🔍 Test 5: Recent Changes Check</h2>";

try {
    $stmt = $db->query("SELECT page_name, title, updated_at FROM hero_sections ORDER BY updated_at DESC LIMIT 5");
    $recent_updates = $stmt->fetchAll();
    
    if (!empty($recent_updates)) {
        echo "<p class='info'>ℹ️ Recent hero updates:</p>";
        echo "<table>";
        echo "<tr><th>Page</th><th>Title</th><th>Last Updated</th></tr>";
        foreach ($recent_updates as $update) {
            echo "<tr>";
            echo "<td>{$update['page_name']}</td>";
            echo "<td>" . htmlspecialchars($update['title']) . "</td>";
            echo "<td>{$update['updated_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ No recent updates found</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking recent updates: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>📋 Summary & Recommendations</h2>";
echo "<p>This diagnostic test helps identify where the hero system may be failing.</p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ul>";
echo "<li>Check the browser output above for any red error messages</li>";
echo "<li>Verify that hero_sections table has all required columns</li>";
echo "<li>Ensure getHeroSection() is returning data for your pages</li>";
echo "<li>Check that admin updates are actually saving to the database</li>";
echo "<li>Verify frontend templates are calling the correct functions</li>";
echo "</ul>";
echo "</div>";

?>
