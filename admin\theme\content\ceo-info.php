<?php
/**
 * CEO Information Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure ceo_info variable is available
if (!isset($ceo_info)) {
    $ceo_info = [];
}
?>

<div class="admin-card">
    <div class="admin-card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-crown me-2"></i>
                CEO Information
            </h5>
            <button type="button"
                    class="btn btn-outline-secondary btn-sm"
                    data-bs-toggle="collapse"
                    data-bs-target="#ceoInfoCollapse"
                    aria-expanded="false"
                    aria-controls="ceoInfoCollapse">
                <i class="fas fa-chevron-down me-1"></i>
                <span class="collapse-text">Expand</span>
            </button>
        </div>
    </div>

    <div class="collapse" id="ceoInfoCollapse">
        <div class="admin-card-body">
        <form method="POST" enctype="multipart/form-data">
            <input type="hidden" name="MAX_FILE_SIZE" value="<?php echo MAX_FILE_SIZE; ?>">
            <input type="hidden" name="action" value="update_ceo_info">
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Basic Information -->
                    <h6 class="mb-3">
                        <i class="fas fa-user-tie me-1"></i>
                        Basic Information
                    </h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="ceo_name" class="form-label">
                                    CEO Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       id="ceo_name" 
                                       name="ceo_name" 
                                       class="form-control" 
                                       required 
                                       value="<?php echo htmlspecialchars($ceo_info['name']); ?>"
                                       placeholder="Full name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="ceo_title" class="form-label">
                                    Title/Position <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       id="ceo_title" 
                                       name="ceo_title" 
                                       class="form-control" 
                                       required 
                                       value="<?php echo htmlspecialchars($ceo_info['title']); ?>"
                                       placeholder="CEO title and position">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-4">
                        <label for="ceo_bio" class="form-label">
                            Biography <span class="text-danger">*</span>
                        </label>
                        <textarea id="ceo_bio" 
                                  name="ceo_bio" 
                                  class="form-control" 
                                  required 
                                  rows="6"
                                  placeholder="Detailed biography including experience, background, and achievements..."><?php echo htmlspecialchars($ceo_info['bio']); ?></textarea>
                    </div>
                    
                    <!-- Professional Details -->
                    <h6 class="mb-3">
                        <i class="fas fa-award me-1"></i>
                        Professional Details
                    </h6>
                    
                    <div class="form-group mb-3">
                        <label for="ceo_achievements" class="form-label">
                            Key Achievements
                        </label>
                        <textarea id="ceo_achievements" 
                                  name="ceo_achievements" 
                                  class="form-control" 
                                  rows="4"
                                  placeholder="List key achievements, certifications, awards (one per line)..."><?php echo htmlspecialchars($ceo_info['achievements']); ?></textarea>
                        <div class="form-text">Enter each achievement on a new line</div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="ceo_education" class="form-label">
                            Education & Licenses
                        </label>
                        <textarea id="ceo_education" 
                                  name="ceo_education" 
                                  class="form-control" 
                                  rows="3"
                                  placeholder="Education and professional licenses (format: Degree|Institution|Year)"><?php echo htmlspecialchars($ceo_info['education']); ?></textarea>
                        <div class="form-text">Format: Degree|Institution|Year (one per line)</div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="ceo_projects" class="form-label">
                            Notable Projects
                        </label>
                        <textarea id="ceo_projects" 
                                  name="ceo_projects" 
                                  class="form-control" 
                                  rows="4"
                                  placeholder="Notable projects (format: Project Name|Description)"><?php echo htmlspecialchars($ceo_info['projects']); ?></textarea>
                        <div class="form-text">Format: Project Name|Description (one per line)</div>
                    </div>
                    
                    <!-- Philosophy -->
                    <h6 class="mb-3">
                        <i class="fas fa-lightbulb me-1"></i>
                        Leadership Philosophy
                    </h6>
                    
                    <div class="form-group mb-3">
                        <label for="ceo_philosophy_quote" class="form-label">
                            Philosophy Quote
                        </label>
                        <textarea id="ceo_philosophy_quote" 
                                  name="ceo_philosophy_quote" 
                                  class="form-control" 
                                  rows="3"
                                  placeholder="Inspirational quote or philosophy statement..."><?php echo htmlspecialchars($ceo_info['philosophy_quote']); ?></textarea>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="ceo_philosophy_description" class="form-label">
                            Philosophy Description
                        </label>
                        <textarea id="ceo_philosophy_description" 
                                  name="ceo_philosophy_description" 
                                  class="form-control" 
                                  rows="3"
                                  placeholder="Detailed description of leadership approach and philosophy..."><?php echo htmlspecialchars($ceo_info['philosophy_description']); ?></textarea>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- CEO Photo -->
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-camera me-1"></i>
                                CEO Photo
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <?php if ($ceo_info['photo']): ?>
                                <div class="current-photo mb-3 text-center">
                                    <img src="<?php echo ensureAbsoluteUrl($ceo_info['photo']); ?>" 
                                         alt="Current CEO Photo" 
                                         class="img-fluid rounded" 
                                         style="max-width: 200px;">
                                    <small class="text-muted d-block mt-1">Current CEO photo</small>
                                </div>
                            <?php endif; ?>
                            <input type="file" 
                                   id="ceo_photo" 
                                   name="ceo_photo" 
                                   class="form-control" 
                                   accept="image/*">
                            <div class="form-text">
                                Professional CEO portrait<br>
                                Recommended: 600x800px or larger<br>
                                Formats: JPG, PNG, WebP
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Preview -->
                    <div class="admin-card">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-eye me-1"></i>
                                Quick Preview
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="ceo-preview">
                                <?php if ($ceo_info['photo']): ?>
                                    <img src="<?php echo ensureAbsoluteUrl($ceo_info['photo']); ?>" 
                                         alt="CEO" 
                                         class="img-fluid rounded mb-2" 
                                         style="max-width: 100px;">
                                <?php endif; ?>
                                <h6 class="mb-1"><?php echo htmlspecialchars($ceo_info['name']); ?></h6>
                                <small class="text-muted d-block mb-2"><?php echo htmlspecialchars($ceo_info['title']); ?></small>
                                <?php if ($ceo_info['philosophy_quote']): ?>
                                    <blockquote class="blockquote-sm">
                                        <small class="text-muted fst-italic">
                                            "<?php echo htmlspecialchars(substr($ceo_info['philosophy_quote'], 0, 100)); ?>..."
                                        </small>
                                    </blockquote>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2 mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    Update CEO Information
                </button>
            </div>
        </form>
        </div>
    </div>
</div>

<style>
.current-photo img {
    max-height: 200px;
    object-fit: cover;
}

.ceo-preview {
    text-align: center;
}

.blockquote-sm {
    font-size: 0.8rem;
    margin: 0;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 0.25rem;
}

.admin-card .admin-card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-text {
    font-size: 0.8rem;
}
</style>

<script>
// Handle collapse toggle
document.addEventListener('DOMContentLoaded', function() {
    const collapseElement = document.getElementById('ceoInfoCollapse');
    const toggleButton = document.querySelector('[data-bs-target="#ceoInfoCollapse"]');
    const collapseText = toggleButton.querySelector('.collapse-text');
    const chevronIcon = toggleButton.querySelector('.fas');

    collapseElement.addEventListener('show.bs.collapse', function() {
        collapseText.textContent = 'Collapse';
        chevronIcon.classList.remove('fa-chevron-down');
        chevronIcon.classList.add('fa-chevron-up');
    });

    collapseElement.addEventListener('hide.bs.collapse', function() {
        collapseText.textContent = 'Expand';
        chevronIcon.classList.remove('fa-chevron-up');
        chevronIcon.classList.add('fa-chevron-down');
    });
});
</script>
