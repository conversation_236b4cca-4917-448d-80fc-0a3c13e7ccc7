<?php
/**
 * ✨ PAGE TEMPLATE - Copy this for ALL new pages
 * Ensures consistent header & footer usage
 */

// 🔒 Security Check
define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// 📄 Page-specific settings
$pageTitle = 'Your Page Title Here';
$pageDescription = 'Your page description for SEO';
$pageKeywords = 'your, page, keywords';

// 🎨 Load page-specific CSS (optional)
$additionalCSS = [
    // 'assets/css/your-page-specific.css',
];

// 📱 Load page-specific JS (optional)  
$additionalJS = [
    // 'assets/js/your-page-specific.js',
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- 🔧 Standard head includes -->
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription,
        'keywords' => $pageKeywords,
        'additionalCSS' => $additionalCSS ?? []
    ]); ?>
</head>
<body>
    <!-- 🎯 ALWAYS include header -->
    <?php loadTemplate('header'); ?>

    <!-- 🎨 Your page content goes here -->
    <main class="page-content">
        <!-- Hero Section (optional) -->
        <section class="page-hero">
            <div class="container">
                <h1><?php echo htmlspecialchars($pageTitle); ?></h1>
                <p>Your page hero content...</p>
            </div>
        </section>

        <!-- Main Content -->
        <section class="main-section">
            <div class="container">
                <!-- Your main content here -->
                <h2>Your Content</h2>
                <p>Add your page content here...</p>
            </div>
        </section>

        <!-- Additional sections as needed -->
    </main>

    <!-- 🎯 ALWAYS include footer (NEW SYSTEM) -->
    <?php loadFooter(); ?>

    <!-- 📱 Load JavaScript -->
    <?php 
    if (!empty($additionalJS)) {
        foreach ($additionalJS as $jsFile) {
            echo '<script src="' . siteUrl($jsFile) . '"></script>' . "\n";
        }
    }
    ?>
</body>
</html>
