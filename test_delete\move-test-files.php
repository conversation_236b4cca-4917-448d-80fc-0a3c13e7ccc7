<?php
/**
 * Move Test Files to test_delete Folder
 */

echo "<h1>MOVING TEST FILES</h1><pre>";

// List of test/debug files to move
$test_files = [
    'check_footer_options.php',
    'debug_db.php', 
    'debug_upload.php',
    'image-test.html',
    'page-generator.php',
    'remove-old-footer.php',
    'update_footer_options.php',
    'news-details-clean.php'
];

$moved_count = 0;
$failed_count = 0;

foreach ($test_files as $file) {
    $source = __DIR__ . '/../' . $file;
    $destination = __DIR__ . '/' . $file;
    
    if (file_exists($source)) {
        if (rename($source, $destination)) {
            echo "✅ Moved: $file\n";
            $moved_count++;
        } else {
            echo "❌ Failed to move: $file\n";
            $failed_count++;
        }
    } else {
        echo "ℹ️ File not found: $file\n";
    }
}

echo "\n=== SUMMARY ===\n";
echo "Files moved: $moved_count\n";
echo "Files failed: $failed_count\n";

echo "\n=== CURRENT TEST_DELETE CONTENTS ===\n";
$test_files_in_folder = scandir(__DIR__);
foreach ($test_files_in_folder as $file) {
    if ($file !== '.' && $file !== '..') {
        echo "- $file\n";
    }
}

echo "</pre>";
?>
