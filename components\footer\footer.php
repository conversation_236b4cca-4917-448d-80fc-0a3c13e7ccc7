<?php
/**
 * Modular Footer Component
 * Clean, organized footer structure
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}
?>

<!-- Modern Footer Component -->
<footer class="footer-component">
    <div class="footer-background-pattern"></div>
    <div class="container">
        <div class="footer-content">
            <!-- Main Footer Content -->
            <div class="footer-main-grid">
                <!-- Brand Section -->
                <div class="footer-brand-section">
                    <?php include __DIR__ . '/sections/brand-section.php'; ?>
                </div>

                <!-- Navigation Links -->
                <div class="footer-nav-section">
                    <?php include __DIR__ . '/sections/navigation-section.php'; ?>
                </div>

                <!-- Contact Information -->
                <div class="footer-contact-section">
                    <?php include __DIR__ . '/sections/contact-section.php'; ?>
                </div>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <?php include __DIR__ . '/sections/footer-bottom.php'; ?>
            </div>
        </div>
    </div>

    <!-- Mobile App Tab Bar (Mobile Only) -->
    <div class="mobile-tab-bar">
        <?php
        $current_page = basename($_SERVER['PHP_SELF'], '.php');
        $tab_items = [
            ['url' => '', 'icon' => 'home', 'label' => 'Home', 'page' => 'index'],
            ['url' => 'services', 'icon' => 'briefcase', 'label' => 'Services', 'page' => 'services'],
            ['url' => 'projects', 'icon' => 'grid', 'label' => 'Projects', 'page' => 'projects'],
            ['url' => 'team', 'icon' => 'users', 'label' => 'Team', 'page' => 'team'],
            ['url' => 'contact', 'icon' => 'mail', 'label' => 'Contact', 'page' => 'contact']
        ];

        foreach ($tab_items as $item):
            $is_active = ($current_page === $item['page']) ? 'active' : '';
            $tab_url = empty($item['url']) ? siteUrl() : siteUrl($item['url']);
        ?>
            <a href="<?php echo $tab_url; ?>" class="mobile-tab-item <?php echo $is_active; ?>">
                <?php if ($item['icon'] === 'home'): ?>
                    <svg class="mobile-tab-icon" viewBox="0 0 24 24">
                        <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                <?php elseif ($item['icon'] === 'briefcase'): ?>
                    <svg class="mobile-tab-icon" viewBox="0 0 24 24">
                        <rect x="2" y="7" width="20" height="14" rx="2" ry="2"/>
                        <path d="M16 21V5a2 2 0 00-2-2h-4a2 2 0 00-2 2v16"/>
                    </svg>
                <?php elseif ($item['icon'] === 'grid'): ?>
                    <svg class="mobile-tab-icon" viewBox="0 0 24 24">
                        <rect x="3" y="3" width="7" height="7"/>
                        <rect x="14" y="3" width="7" height="7"/>
                        <rect x="14" y="14" width="7" height="7"/>
                        <rect x="3" y="14" width="7" height="7"/>
                    </svg>
                <?php elseif ($item['icon'] === 'users'): ?>
                    <svg class="mobile-tab-icon" viewBox="0 0 24 24">
                        <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                        <circle cx="9" cy="7" r="4"/>
                        <path d="M23 21v-2a4 4 0 00-3-3.87"/>
                        <path d="M16 3.13a4 4 0 010 7.75"/>
                    </svg>
                <?php elseif ($item['icon'] === 'mail'): ?>
                    <svg class="mobile-tab-icon" viewBox="0 0 24 24">
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                        <polyline points="22,6 12,13 2,6"/>
                    </svg>
                <?php endif; ?>
                <span class="mobile-tab-label"><?php echo $item['label']; ?></span>
            </a>
        <?php endforeach; ?>
    </div>
</footer>
