<?php
/**
 * Page Hero Template - Database-Driven
 * Reusable hero section for all pages except home
 * Now connects to admin hero management system
 *
 * Variables expected:
 * $hero_title - Main page title (fallback if no database record)
 * $hero_subtitle - Optional subtitle (fallback if no database record)
 * $hero_background - Background image URL (fallback if no database record)
 * $breadcrumb_items - Array of breadcrumb items
 * $hero_page_name - Page name for database lookup (optional)
 */

// Determine page name for database lookup
if (!isset($hero_page_name)) {
    $current_file = basename($_SERVER['PHP_SELF'], '.php');
    $hero_page_name = ($current_file === 'index') ? 'home' : $current_file;
}

// Get hero section from database
$db_hero = getHeroSection($hero_page_name);

// Use database data if available, otherwise fall back to provided variables or defaults
if ($db_hero) {
    // Use database values
    $hero_title = $db_hero['title'];
    $hero_subtitle = $db_hero['caption'] ?? '';
    $hero_description = $db_hero['description'] ?? '';

    // Handle background - prefer database image, then gradient, then fallback
    if ($db_hero['background_type'] === 'image' && !empty($db_hero['background_image'])) {
        $hero_background = $db_hero['background_image'];
        $hero_background_style = "background-image: url('" . htmlspecialchars($hero_background) . "');";
    } elseif ($db_hero['background_type'] === 'gradient' && !empty($db_hero['background_gradient'])) {
        $hero_background_style = "background: " . htmlspecialchars($db_hero['background_gradient']) . ";";
    } else {
        // Fallback to provided background or default
        $hero_background = $hero_background ?? 'assets/images/demo-image/demo-images/imgi_42_678b4fa8626623b854fc160e_project-main01-p-800.jpg';
        $hero_background_style = "background-image: url('" . htmlspecialchars($hero_background) . "');";
    }

    // Get colors from database
    $title_color = $db_hero['title_color'] ?? '#ffffff';
    $caption_color = $db_hero['caption_color'] ?? '#ffffff';
    $description_color = $db_hero['description_color'] ?? '#ffffff';

} else {
    // Use fallback values (backward compatibility)
    $hero_title = $hero_title ?? 'Page Title';
    $hero_subtitle = $hero_subtitle ?? '';
    $hero_description = '';
    $hero_background = $hero_background ?? 'assets/images/demo-image/demo-images/imgi_42_678b4fa8626623b854fc160e_project-main01-p-800.jpg';
    $hero_background_style = "background-image: url('" . htmlspecialchars($hero_background) . "');";

    // Default colors
    $title_color = '#ffffff';
    $caption_color = '#ffffff';
    $description_color = '#ffffff';
}

// Breadcrumb items (always use provided or default)
$breadcrumb_items = $breadcrumb_items ?? [
    ['title' => 'Home', 'url' => 'index.php'],
    ['title' => 'Page', 'url' => '']
];
?>

<!-- Page Hero Section - Database-Driven -->
<section class="page-hero<?php echo isset($hero_overlay_class) ? ' ' . $hero_overlay_class : ''; ?>"
         style="<?php echo $hero_background_style; ?>">
    <div class="hero-content">
        <div class="breadcrumb">
            <?php foreach ($breadcrumb_items as $index => $item): ?>
                <?php if ($index > 0): ?>
                    <span>/</span>
                <?php endif; ?>

                <?php if (!empty($item['url'])): ?>
                    <a href="<?php echo $item['url']; ?>"><?php echo htmlspecialchars($item['title']); ?></a>
                <?php else: ?>
                    <span><?php echo htmlspecialchars($item['title']); ?></span>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>

        <?php if ($hero_subtitle): ?>
            <p class="hero-caption" style="color: <?php echo $caption_color; ?>;">
                <?php echo htmlspecialchars($hero_subtitle); ?>
            </p>
        <?php endif; ?>

        <h1 class="hero-title" style="color: <?php echo $title_color; ?>;">
            <?php echo htmlspecialchars($hero_title); ?>
        </h1>

        <?php if ($hero_description): ?>
            <p class="hero-description" style="color: <?php echo $description_color; ?>;">
                <?php echo htmlspecialchars($hero_description); ?>
            </p>
        <?php endif; ?>

        <?php if ($db_hero && !empty($db_hero['button_text'])): ?>
            <div class="hero-actions">
                <a href="<?php echo siteUrl($db_hero['button_link']); ?>"
                   class="btn btn-primary hero-button"
                   style="background: <?php echo $db_hero['button_bg_color'] ?? '#E67E22'; ?>;
                          color: <?php echo $db_hero['button_text_color'] ?? '#ffffff'; ?>;">
                    <?php echo htmlspecialchars($db_hero['button_text']); ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php if ($db_hero): ?>
<!-- Database-driven hero styling -->
<style>
.page-hero {
    <?php if (isset($db_hero['height_type'])): ?>
        <?php if ($db_hero['height_type'] === 'small'): ?>
            min-height: 300px;
        <?php elseif ($db_hero['height_type'] === 'medium'): ?>
            min-height: 400px;
        <?php elseif ($db_hero['height_type'] === 'large'): ?>
            min-height: 600px;
        <?php elseif ($db_hero['height_type'] === 'custom' && !empty($db_hero['height_custom'])): ?>
            min-height: <?php echo intval($db_hero['height_custom']); ?>px;
        <?php endif; ?>
    <?php endif; ?>

    <?php if (!empty($db_hero['background_color']) && !empty($db_hero['background_opacity'])): ?>
        position: relative;
    <?php endif; ?>
}

<?php if (!empty($db_hero['background_color']) && !empty($db_hero['background_opacity'])): ?>
.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: <?php echo $db_hero['background_color']; ?>;
    opacity: <?php echo $db_hero['background_opacity']; ?>;
    z-index: 1;
}

.page-hero .hero-content {
    position: relative;
    z-index: 2;
}
<?php endif; ?>
</style>
<?php endif; ?>
