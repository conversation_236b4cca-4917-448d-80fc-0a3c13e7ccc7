<?php
/**
 * Services Form Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure edit_service variable is available
if (!isset($edit_service)) {
    $edit_service = null;
}
?>

<div class="admin-card">
    <div class="admin-card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas <?php echo $edit_service ? 'fa-edit' : 'fa-plus'; ?> me-2"></i>
            <?php echo $edit_service ? 'Edit Service' : 'Add New Service'; ?>
        </h5>
        <button type="button" 
                class="btn btn-outline-secondary btn-sm" 
                onclick="<?php echo $edit_service ? 'cancelForm()' : 'toggleForm()'; ?>">
            <i class="fas <?php echo $edit_service ? 'fa-times' : 'fa-plus'; ?> me-1"></i>
            <?php echo $edit_service ? 'Cancel Edit' : 'Add New Service'; ?>
        </button>
    </div>
    
    <div class="admin-card-body">
        <form method="POST"
              enctype="multipart/form-data"
              class="service-form <?php echo $edit_service ? 'show' : ''; ?>"
              id="serviceForm">
            <input type="hidden" name="MAX_FILE_SIZE" value="<?php echo MAX_FILE_SIZE; ?>">
            <input type="hidden" name="action" value="<?php echo $edit_service ? 'update_service' : 'add_service'; ?>">
            <?php if ($edit_service): ?>
                <input type="hidden" name="id" value="<?php echo $edit_service['id']; ?>">
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Main Content -->
                    <div class="form-group mb-3">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-1"></i>
                            Service Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               class="form-control" 
                               required 
                               value="<?php echo $edit_service ? htmlspecialchars($edit_service['title']) : ''; ?>"
                               placeholder="Enter service title">
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="slug" class="form-label">
                            <i class="fas fa-link me-1"></i>
                            URL Slug <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="slug" 
                               name="slug" 
                               class="form-control" 
                               required 
                               value="<?php echo $edit_service ? htmlspecialchars($edit_service['slug']) : ''; ?>"
                               placeholder="service-url-slug">
                        <div class="form-text">
                            URL-friendly version of the title (auto-generated from title)
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-1"></i>
                            Short Description <span class="text-danger">*</span>
                        </label>
                        <textarea id="description" 
                                  name="description" 
                                  class="form-control" 
                                  required 
                                  rows="3"
                                  placeholder="Brief description of the service..."><?php echo $edit_service ? htmlspecialchars($edit_service['description']) : ''; ?></textarea>
                        <div class="form-text">
                            A brief summary that appears in service listings
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="content" class="form-label">
                            <i class="fas fa-file-alt me-1"></i>
                            Detailed Content
                        </label>
                        <textarea id="content" 
                                  name="content" 
                                  class="form-control" 
                                  rows="8"
                                  placeholder="Detailed service information, process, benefits, etc..."><?php echo $edit_service ? htmlspecialchars($edit_service['content']) : ''; ?></textarea>
                        <div class="form-text">
                            Detailed service information that appears on the service page (HTML allowed)
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- Sidebar Settings -->
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cog me-1"></i>
                                Settings
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group mb-3">
                                <label for="sort_order" class="form-label">
                                    <i class="fas fa-sort-numeric-up me-1"></i>
                                    Sort Order
                                </label>
                                <input type="number" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       class="form-control" 
                                       min="0" 
                                       value="<?php echo $edit_service ? $edit_service['sort_order'] : '0'; ?>"
                                       placeholder="0">
                                <div class="form-text">Lower numbers appear first</div>
                            </div>
                            
                            <?php if ($edit_service): ?>
                            <div class="form-group mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="active" 
                                           name="active" 
                                           value="1" 
                                           <?php echo ($edit_service && $edit_service['active']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="active">
                                        <strong>Active</strong>
                                    </label>
                                </div>
                                <div class="form-text">Show this service on the website</div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-icons me-1"></i>
                                Service Icon
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group mb-3">
                                <label for="icon" class="form-label">
                                    FontAwesome Icon Class
                                </label>
                                <input type="text" 
                                       id="icon" 
                                       name="icon" 
                                       class="form-control" 
                                       value="<?php echo $edit_service ? htmlspecialchars($edit_service['icon']) : ''; ?>"
                                       placeholder="fa-cogs">
                                <div class="form-text">
                                    FontAwesome icon class (e.g., fa-cogs, fa-home, fa-users)
                                </div>
                            </div>
                            
                            <div class="text-center mb-3">
                                <strong>OR</strong>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="icon_upload" class="form-label">
                                    Upload Custom Icon
                                </label>
                                <?php if ($edit_service && $edit_service['icon'] && strpos($edit_service['icon'], 'uploads/') !== false): ?>
                                    <div class="current-icon mb-3 text-center">
                                        <img src="<?php echo ensureAbsoluteUrl($edit_service['icon']); ?>" 
                                             alt="Current Icon" 
                                             class="img-fluid" 
                                             style="max-width: 60px;">
                                        <small class="text-muted d-block mt-1">Current custom icon</small>
                                    </div>
                                <?php endif; ?>
                                <input type="file" 
                                       id="icon_upload" 
                                       name="icon_upload" 
                                       class="form-control" 
                                       accept="image/svg+xml,image/png,image/jpeg">
                                <div class="form-text">
                                    Upload SVG, PNG, or JPG icon<br>
                                    Recommended: 64x64px or larger<br>
                                    SVG preferred for scalability
                                </div>
                            </div>
                            
                            <!-- Icon Preview -->
                            <div class="icon-preview text-center">
                                <div class="preview-container p-3 border rounded bg-light">
                                    <div id="iconPreview">
                                        <?php if ($edit_service && $edit_service['icon']): ?>
                                            <?php if (strpos($edit_service['icon'], 'fa-') !== false): ?>
                                                <i class="fas <?php echo htmlspecialchars($edit_service['icon']); ?> fa-2x text-primary"></i>
                                            <?php else: ?>
                                                <img src="<?php echo ensureAbsoluteUrl($edit_service['icon']); ?>" 
                                                     alt="Icon Preview" 
                                                     style="max-width: 40px;">
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <i class="fas fa-cogs fa-2x text-muted"></i>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted d-block mt-2">Icon Preview</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2 mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas <?php echo $edit_service ? 'fa-save' : 'fa-plus'; ?> me-1"></i>
                    <?php echo $edit_service ? 'Update Service' : 'Add Service'; ?>
                </button>
                <button type="button" class="btn btn-secondary" onclick="cancelForm()">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.service-form {
    display: none;
}

.service-form.show {
    display: block;
}

.current-icon img {
    max-height: 60px;
    object-fit: contain;
}

.admin-card .admin-card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-text {
    font-size: 0.8rem;
}

.form-check-input:checked {
    background-color: var(--admin-accent-color);
    border-color: var(--admin-accent-color);
}

.preview-container {
    min-height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
</style>

<script>
// Auto-generate slug from title
document.getElementById('title').addEventListener('input', function() {
    const slugField = document.getElementById('slug');
    if (!slugField.dataset.manuallyEdited) {
        const slug = this.value
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
        slugField.value = slug;
    }
});

document.getElementById('slug').addEventListener('input', function() {
    this.dataset.manuallyEdited = 'true';
});

// Icon preview
document.getElementById('icon').addEventListener('input', function() {
    const preview = document.getElementById('iconPreview');
    const iconClass = this.value.trim();
    
    if (iconClass && iconClass.startsWith('fa-')) {
        preview.innerHTML = '<i class="fas ' + iconClass + ' fa-2x text-primary"></i>';
    } else {
        preview.innerHTML = '<i class="fas fa-cogs fa-2x text-muted"></i>';
    }
});

// File upload preview
document.getElementById('icon_upload').addEventListener('change', function() {
    const file = this.files[0];
    const preview = document.getElementById('iconPreview');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = '<img src="' + e.target.result + '" alt="Icon Preview" style="max-width: 40px;">';
        };
        reader.readAsDataURL(file);
        
        // Clear the FontAwesome icon field
        document.getElementById('icon').value = '';
    }
});
</script>
