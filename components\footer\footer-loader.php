<?php
/**
 * Footer Component Loader
 * Clean way to load the modular footer
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Load the modular footer component
 * This replaces the old loadTemplate('footer') calls
 */
function loadFooterComponent() {
    // Load footer CSS
    echo '<link rel="stylesheet" href="' . siteUrl('components/footer/css/footer.css') . '">' . "\n";

    // Load mobile app CSS for mobile footer and header functionality
    echo '<link rel="stylesheet" href="' . siteUrl('assets/css/mobile-app.css') . '">' . "\n";

    // Load mobile app JavaScript for mobile menu functionality
    echo '<script src="' . siteUrl('assets/js/mobile-app.js') . '" defer></script>' . "\n";

    // Load footer HTML
    include __DIR__ . '/footer.php';
}

/**
 * Load only footer CSS (for pages that need just the styles)
 */
function loadFooterCSS() {
    echo '<link rel="stylesheet" href="' . siteUrl('components/footer/css/footer.css') . '">' . "\n";
}

/**
 * Get footer component path
 */
function getFooterComponentPath($file = '') {
    return __DIR__ . '/' . ltrim($file, '/');
}

/**
 * Load a specific footer section
 */
function loadFooterSection($section) {
    $sectionPath = __DIR__ . '/sections/' . $section . '.php';
    if (file_exists($sectionPath)) {
        include $sectionPath;
    } else {
        echo "<!-- Footer section not found: {$section} -->";
    }
}
?>
