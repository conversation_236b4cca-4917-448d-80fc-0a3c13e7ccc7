<?php
/**
 * Team Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Check admin authentication
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_team_member':
                $name = sanitizeInput($_POST['name']);
                $position = sanitizeInput($_POST['position']);
                $bio = sanitizeInput($_POST['bio']);
                $email = sanitizeInput($_POST['email']);
                $linkedin_url = sanitizeInput($_POST['linkedin_url']);
                $sort_order = intval($_POST['sort_order']);
                $active = isset($_POST['active']) ? 1 : 0;
                $photo = '';

                // Handle photo upload
                if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['photo'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload_result['success']) {
                        $photo = $upload_result['url'];
                    } else {
                        $error = 'Photo upload failed: ' . $upload_result['message'];
                        break;
                    }
                }

                if (!$error) {
                    try {
                        $stmt = $db->prepare("INSERT INTO team_members (name, position, bio, email, photo, linkedin_url, sort_order, active) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                        $result = $stmt->execute([$name, $position, $bio, $email, $photo, $linkedin_url, $sort_order, $active]);
                        
                        if ($result) {
                            $message = 'Team member added successfully!';
                        } else {
                            $error = 'Failed to add team member.';
                        }
                    } catch (Exception $e) {
                        $error = 'Error: ' . $e->getMessage();
                    }
                }
                break;

            case 'update_team_member':
                $id = intval($_POST['member_id']);
                $name = sanitizeInput($_POST['name']);
                $position = sanitizeInput($_POST['position']);
                $bio = sanitizeInput($_POST['bio']);
                $email = sanitizeInput($_POST['email']);
                $linkedin_url = sanitizeInput($_POST['linkedin_url']);
                $sort_order = intval($_POST['sort_order']);
                $active = isset($_POST['active']) ? 1 : 0;
                $photo = $_POST['current_photo'] ?? '';

                // Handle photo upload
                if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['photo'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload_result['success']) {
                        $photo = $upload_result['url'];
                    } else {
                        $error = 'Photo upload failed: ' . $upload_result['message'];
                        break;
                    }
                }

                if (!$error) {
                    try {
                        $stmt = $db->prepare("UPDATE team_members SET name = ?, position = ?, bio = ?, email = ?, photo = ?, linkedin_url = ?, sort_order = ?, active = ? WHERE id = ?");
                        $result = $stmt->execute([$name, $position, $bio, $email, $photo, $linkedin_url, $sort_order, $active, $id]);
                        
                        if ($result) {
                            $message = 'Team member updated successfully!';
                        } else {
                            $error = 'Failed to update team member.';
                        }
                    } catch (Exception $e) {
                        $error = 'Error: ' . $e->getMessage();
                    }
                }
                break;

            case 'delete_team_member':
                try {
                    $id = intval($_POST['member_id']);
                    $stmt = $db->prepare("DELETE FROM team_members WHERE id = ?");
                    $result = $stmt->execute([$id]);
                    
                    if ($result) {
                        $message = 'Team member deleted successfully!';
                    } else {
                        $error = 'Failed to delete team member.';
                    }
                } catch (Exception $e) {
                    $error = 'Error: ' . $e->getMessage();
                }
                break;

            case 'update_ceo_info':
                try {
                    // Handle CEO photo upload
                    if (isset($_FILES['ceo_photo']) && $_FILES['ceo_photo']['error'] === UPLOAD_ERR_OK) {
                        $upload_result = uploadFile($_FILES['ceo_photo'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload_result['success']) {
                            updateThemeOption('ceo_photo', $upload_result['url']);
                        }
                    }
                    
                    // Store CEO information
                    updateThemeOption('ceo_name', sanitizeInput($_POST['ceo_name']));
                    updateThemeOption('ceo_title', sanitizeInput($_POST['ceo_title']));
                    updateThemeOption('ceo_bio', sanitizeInput($_POST['ceo_bio']));
                    updateThemeOption('ceo_achievements', sanitizeInput($_POST['ceo_achievements']));
                    updateThemeOption('ceo_education', sanitizeInput($_POST['ceo_education']));
                    updateThemeOption('ceo_philosophy_quote', sanitizeInput($_POST['ceo_philosophy_quote']));
                    updateThemeOption('ceo_philosophy_description', sanitizeInput($_POST['ceo_philosophy_description']));
                    updateThemeOption('ceo_projects', sanitizeInput($_POST['ceo_projects']));
                    
                    $message = 'CEO information updated successfully!';
                } catch (Exception $e) {
                    $error = 'Error updating CEO information: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get all team members
try {
    $stmt = $db->query("SELECT * FROM team_members ORDER BY sort_order ASC, name ASC");
    $team_members = $stmt->fetchAll();
} catch (Exception $e) {
    $error = 'Error loading team members: ' . $e->getMessage();
    $team_members = [];
}

// Get CEO information
$ceo_info = [
    'name' => getThemeOption('ceo_name', 'Alexander Thompson'),
    'title' => getThemeOption('ceo_title', 'Chief Executive Officer & Principal Architect'),
    'bio' => getThemeOption('ceo_bio', 'With over 25 years of experience in architectural design and construction management, Alexander founded Monolith Design with a vision to create spaces that inspire and endure.'),
    'photo' => getThemeOption('ceo_photo', ''),
    'achievements' => getThemeOption('ceo_achievements', 'Licensed Architect in 12 states\nLEED AP BD+C Certified Professional\nAIA Gold Medal Recipient (2023)'),
    'education' => getThemeOption('ceo_education', 'Master of Architecture|Massachusetts Institute of Technology (MIT)|1998\nBachelor of Architecture|University of California, Berkeley|1996'),
    'philosophy_quote' => getThemeOption('ceo_philosophy_quote', 'Architecture is not just about creating buildings; it\'s about crafting experiences that inspire, spaces that endure, and environments that enhance human life.'),
    'philosophy_description' => getThemeOption('ceo_philosophy_description', 'Alexander\'s leadership approach combines visionary thinking with practical execution, ensuring that every project delivered by Monolith Design exceeds client expectations.'),
    'projects' => getThemeOption('ceo_projects', 'Metropolitan Arts Center|$150M cultural complex featuring sustainable design\nSkyline Corporate Headquarters|LEED Platinum certified office tower')
];

// Get member for editing
$edit_member = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    try {
        $stmt = $db->prepare("SELECT * FROM team_members WHERE id = ?");
        $stmt->execute([intval($_GET['edit'])]);
        $edit_member = $stmt->fetch();
    } catch (Exception $e) {
        $error = 'Error loading team member: ' . $e->getMessage();
    }
}

// Get statistics
$stats = [
    'total' => count($team_members),
    'active' => count(array_filter($team_members, fn($m) => $m['active'] == 1)),
    'inactive' => count(array_filter($team_members, fn($m) => $m['active'] == 0)),
    'recent' => count(array_filter($team_members, fn($m) => strtotime($m['created_at']) > strtotime('-30 days')))
];

// Render the page using the new theme system
renderAdminPage('management', [
    'page_title' => 'Team Management',
    'page_icon' => 'fas fa-users',
    'page_description' => 'Manage your team members and CEO information to showcase your professional team.',
    'management_title' => 'Team & Leadership',
    'management_description' => 'Build trust and credibility by showcasing your professional team and leadership.',
    'management_actions' => [
        [
            'url' => siteUrl('team'),
            'label' => 'View Team Page',
            'class' => 'btn-outline-primary',
            'icon' => 'fas fa-external-link-alt',
            'target' => '_blank'
        ]
    ],
    'show_search' => true,
    'search_placeholder' => 'Search team members by name, position, or bio...',
    'search_target' => '.searchable-item',
    'show_table' => true,
    'table_title' => 'Team Members',
    'table_content_file' => __DIR__ . '/theme/content/team-table.php',
    'show_stats' => true,
    'stats_cards' => [
        [
            'icon' => 'fas fa-users',
            'number' => $stats['total'],
            'label' => 'Total Members'
        ],
        [
            'icon' => 'fas fa-user-check',
            'number' => $stats['active'],
            'label' => 'Active'
        ],
        [
            'icon' => 'fas fa-user-times',
            'number' => $stats['inactive'],
            'label' => 'Inactive'
        ],
        [
            'icon' => 'fas fa-user-plus',
            'number' => $stats['recent'],
            'label' => 'Recent (30d)'
        ]
    ],
    'custom_content_before_table' => function() use ($edit_member, $ceo_info) {
        include __DIR__ . '/theme/content/team-form.php';
        echo '<div class="mt-4"></div>';
        include __DIR__ . '/theme/content/ceo-info.php';
    },
    'message' => $message,
    'error' => $error,
    'team_members' => $team_members,
    'edit_member' => $edit_member,
    'ceo_info' => $ceo_info,
    'custom_js' => '
        function toggleForm() {
            const form = document.getElementById("teamForm");
            if (form) {
                form.classList.toggle("show");
                if (form.classList.contains("show")) {
                    document.getElementById("name").focus();
                }
            }
        }

        function cancelForm() {
            window.location.href = "team.php";
        }
    '
]);
?>
