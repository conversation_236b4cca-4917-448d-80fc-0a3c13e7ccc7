<?php
/**
 * Hero Headers Form Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

$is_edit = isset($edit_hero_header) && $edit_hero_header;
$form_title = $is_edit ? 'Edit Hero Header' : 'Add New Hero Header';
$form_action = $is_edit ? 'update' : 'create';
$button_text = $is_edit ? 'Update Hero Header' : 'Create Hero Header';
$button_icon = $is_edit ? 'fas fa-save' : 'fas fa-plus';

// Default values
$defaults = [
    'page_name' => '',
    'page_title' => '',
    'subtitle' => '',
    'show_breadcrumbs' => true,
    'background_type' => 'gradient',
    'background_image' => '',
    'background_gradient' => 'linear-gradient(135deg, rgba(55, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)',
    'background_color' => '#a99eff',
    'background_opacity' => 0.60,
    'height_type' => 'medium',
    'height_custom' => 400,
    'padding_top' => '4rem',
    'padding_bottom' => '4rem',
    'title_color' => '#ffffff',
    'subtitle_color' => '#ffffff',
    'breadcrumb_color' => '#ffffff',
    'show_cta_button' => false,
    'cta_button_text' => '',
    'cta_button_link' => '',
    'cta_button_color' => '#E67E22',
    'cta_button_text_color' => '#ffffff',
    'active' => true
];

// Merge with edit data if available
$data = $is_edit ? array_merge($defaults, $edit_hero_header) : $defaults;
?>

<div class="admin-card" id="heroHeaderForm" style="<?php echo $is_edit ? '' : 'display: none;'; ?>">
    <div class="admin-card-header">
        <h5 class="admin-card-title">
            <i class="<?php echo $button_icon; ?> me-2"></i>
            <?php echo $form_title; ?>
        </h5>
        <?php if (!$is_edit): ?>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleAddForm()">
                <i class="fas fa-times"></i>
            </button>
        <?php endif; ?>
    </div>
    <div class="admin-card-body">
        <form method="POST" class="hero-header-form">
            <input type="hidden" name="action" value="<?php echo $form_action; ?>">
            <?php if ($is_edit): ?>
                <input type="hidden" name="id" value="<?php echo $data['id']; ?>">
            <?php endif; ?>
            
            <!-- Basic Information -->
            <div class="form-section">
                <h6 class="form-section-title">
                    <i class="fas fa-info-circle me-2"></i>Basic Information
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="page_name" class="form-label">Page Name *</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="page_name" 
                                   name="page_name" 
                                   value="<?php echo htmlspecialchars($data['page_name']); ?>"
                                   <?php echo $is_edit ? 'readonly' : 'required'; ?>
                                   placeholder="e.g., about, services, contact">
                            <div class="form-text">Unique identifier for the page (lowercase, no spaces)</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="page_title" class="form-label">Page Title *</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="page_title" 
                                   name="page_title" 
                                   value="<?php echo htmlspecialchars($data['page_title']); ?>"
                                   required
                                   placeholder="e.g., About Us, Our Services">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="subtitle" 
                                   name="subtitle" 
                                   value="<?php echo htmlspecialchars($data['subtitle']); ?>"
                                   placeholder="Optional subtitle or description">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Background Settings -->
            <div class="form-section">
                <h6 class="form-section-title">
                    <i class="fas fa-palette me-2"></i>Background Settings
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="background_type" class="form-label">Background Type</label>
                            <select class="form-control" id="background_type" name="background_type" onchange="toggleBackgroundFields()">
                                <option value="gradient" <?php echo $data['background_type'] === 'gradient' ? 'selected' : ''; ?>>Gradient</option>
                                <option value="image" <?php echo $data['background_type'] === 'image' ? 'selected' : ''; ?>>Image</option>
                                <option value="color" <?php echo $data['background_type'] === 'color' ? 'selected' : ''; ?>>Solid Color</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="background_color" class="form-label">Overlay Color</label>
                            <input type="color" 
                                   class="form-control form-control-color" 
                                   id="background_color" 
                                   name="background_color" 
                                   value="<?php echo $data['background_color']; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="background_opacity" class="form-label">Overlay Opacity</label>
                            <input type="range" 
                                   class="form-range" 
                                   id="background_opacity" 
                                   name="background_opacity" 
                                   min="0" max="1" step="0.1" 
                                   value="<?php echo $data['background_opacity']; ?>"
                                   oninput="updateOpacityValue(this.value)">
                            <div class="form-text">Opacity: <span id="opacityValue"><?php echo $data['background_opacity']; ?></span></div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6" id="imageField">
                        <div class="form-group">
                            <label for="background_image" class="form-label">Background Image URL</label>
                            <input type="url"
                                   class="form-control"
                                   id="background_image"
                                   name="background_image"
                                   value="<?php echo htmlspecialchars($data['background_image'] ?? ''); ?>"
                                   placeholder="https://example.com/image.jpg">
                        </div>
                    </div>
                    <div class="col-md-6" id="gradientField">
                        <div class="form-group">
                            <label for="background_gradient" class="form-label">CSS Gradient</label>
                            <textarea class="form-control" 
                                      id="background_gradient" 
                                      name="background_gradient" 
                                      rows="2"
                                      placeholder="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"><?php echo htmlspecialchars($data['background_gradient']); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Layout & Colors -->
            <div class="form-section">
                <h6 class="form-section-title">
                    <i class="fas fa-arrows-alt-v me-2"></i>Layout & Colors
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="height_type" class="form-label">Height Type</label>
                            <select class="form-control" id="height_type" name="height_type" onchange="toggleCustomHeight()">
                                <option value="small" <?php echo $data['height_type'] === 'small' ? 'selected' : ''; ?>>Small (300px)</option>
                                <option value="medium" <?php echo $data['height_type'] === 'medium' ? 'selected' : ''; ?>>Medium (400px)</option>
                                <option value="large" <?php echo $data['height_type'] === 'large' ? 'selected' : ''; ?>>Large (600px)</option>
                                <option value="custom" <?php echo $data['height_type'] === 'custom' ? 'selected' : ''; ?>>Custom</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4" id="customHeightField">
                        <div class="form-group">
                            <label for="height_custom" class="form-label">Custom Height (px)</label>
                            <input type="number"
                                   class="form-control"
                                   id="height_custom"
                                   name="height_custom"
                                   value="<?php echo $data['height_custom']; ?>"
                                   min="200" max="1000">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="padding_top" class="form-label">Top Padding</label>
                            <input type="text"
                                   class="form-control"
                                   id="padding_top"
                                   name="padding_top"
                                   value="<?php echo htmlspecialchars($data['padding_top'] ?? '4rem'); ?>"
                                   placeholder="4rem">
                            <div class="form-text">e.g., 4rem, 60px, 5vh</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="padding_bottom" class="form-label">Bottom Padding</label>
                            <input type="text"
                                   class="form-control"
                                   id="padding_bottom"
                                   name="padding_bottom"
                                   value="<?php echo htmlspecialchars($data['padding_bottom'] ?? '4rem'); ?>"
                                   placeholder="4rem">
                            <div class="form-text">e.g., 4rem, 60px, 5vh</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="title_color" class="form-label">Title Color</label>
                            <input type="color"
                                   class="form-control form-control-color"
                                   id="title_color"
                                   name="title_color"
                                   value="<?php echo $data['title_color']; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="subtitle_color" class="form-label">Subtitle Color</label>
                            <input type="color"
                                   class="form-control form-control-color"
                                   id="subtitle_color"
                                   name="subtitle_color"
                                   value="<?php echo $data['subtitle_color']; ?>">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="breadcrumb_color" class="form-label">Breadcrumb Color</label>
                            <input type="color"
                                   class="form-control form-control-color"
                                   id="breadcrumb_color"
                                   name="breadcrumb_color"
                                   value="<?php echo $data['breadcrumb_color']; ?>">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features -->
            <div class="form-section">
                <h6 class="form-section-title">
                    <i class="fas fa-cog me-2"></i>Features
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input type="checkbox" 
                                   class="form-check-input" 
                                   id="show_breadcrumbs" 
                                   name="show_breadcrumbs" 
                                   <?php echo $data['show_breadcrumbs'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="show_breadcrumbs">
                                Show Breadcrumbs
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input type="checkbox" 
                                   class="form-check-input" 
                                   id="show_cta_button" 
                                   name="show_cta_button" 
                                   <?php echo $data['show_cta_button'] ? 'checked' : ''; ?>
                                   onchange="toggleCTAFields()">
                            <label class="form-check-label" for="show_cta_button">
                                Show CTA Button
                            </label>
                        </div>
                    </div>
                </div>
                
                <div id="ctaFields" style="<?php echo $data['show_cta_button'] ? '' : 'display: none;'; ?>">
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="cta_button_text" class="form-label">Button Text</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="cta_button_text" 
                                       name="cta_button_text" 
                                       value="<?php echo htmlspecialchars($data['cta_button_text']); ?>"
                                       placeholder="e.g., Get Started">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="cta_button_link" class="form-label">Button Link</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="cta_button_link" 
                                       name="cta_button_link" 
                                       value="<?php echo htmlspecialchars($data['cta_button_link']); ?>"
                                       placeholder="/contact or https://example.com">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="cta_button_color" class="form-label">Button Color</label>
                                <input type="color" 
                                       class="form-control form-control-color" 
                                       id="cta_button_color" 
                                       name="cta_button_color" 
                                       value="<?php echo $data['cta_button_color']; ?>">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="cta_button_text_color" class="form-label">Text Color</label>
                                <input type="color" 
                                       class="form-control form-control-color" 
                                       id="cta_button_text_color" 
                                       name="cta_button_text_color" 
                                       value="<?php echo $data['cta_button_text_color']; ?>">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input type="checkbox" 
                                   class="form-check-input" 
                                   id="active" 
                                   name="active" 
                                   <?php echo $data['active'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="active">
                                Active (Show on website)
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <?php if ($is_edit): ?>
                    <a href="hero-headers.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Cancel
                    </a>
                <?php else: ?>
                    <button type="button" class="btn btn-secondary" onclick="toggleAddForm()">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                <?php endif; ?>
                <button type="submit" class="btn btn-primary">
                    <i class="<?php echo $button_icon; ?> me-2"></i><?php echo $button_text; ?>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function updateOpacityValue(value) {
    document.getElementById('opacityValue').textContent = value;
}

function toggleBackgroundFields() {
    const type = document.getElementById('background_type').value;
    const imageField = document.getElementById('imageField');
    const gradientField = document.getElementById('gradientField');
    
    if (type === 'image') {
        imageField.style.display = 'block';
        gradientField.style.display = 'none';
    } else if (type === 'gradient') {
        imageField.style.display = 'none';
        gradientField.style.display = 'block';
    } else {
        imageField.style.display = 'none';
        gradientField.style.display = 'none';
    }
}

function toggleCustomHeight() {
    const type = document.getElementById('height_type').value;
    const customField = document.getElementById('customHeightField');
    customField.style.display = type === 'custom' ? 'block' : 'none';
}

function toggleCTAFields() {
    const showCTA = document.getElementById('show_cta_button').checked;
    const ctaFields = document.getElementById('ctaFields');
    ctaFields.style.display = showCTA ? 'block' : 'none';
}

// Initialize field visibility on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleBackgroundFields();
    toggleCustomHeight();
    toggleCTAFields();
});
</script>
