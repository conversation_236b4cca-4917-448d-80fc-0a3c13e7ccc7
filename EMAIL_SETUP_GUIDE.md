# Email Setup Guide - Monolith Design Co.

## 🎯 Current Status

✅ **Contact Form**: Fully functional - saves to database  
⚠️ **Email Notifications**: Requires SMTP configuration for production  
✅ **Admin Interface**: Complete email settings panel available  

## 📧 Email Configuration Options

### Option 1: Gmail SMTP (Recommended)

1. **Go to Admin Panel**: `/admin/email-settings.php`
2. **Enable SMTP**: Check "Enable SMTP" checkbox
3. **Configure Gmail Settings**:
   - **SMTP Host**: `smtp.gmail.com`
   - **SMTP Port**: `587`
   - **SMTP Username**: `<EMAIL>`
   - **SMTP Password**: `your-app-password` (see below)
   - **Encryption**: `TLS`
   - **From Email**: `<EMAIL>`
   - **Admin Email**: `<EMAIL>`

4. **Create Gmail App Password**:
   - Go to Google Account settings
   - Enable 2-Factor Authentication
   - Generate App Password for "Mail"
   - Use this password in SMTP settings

### Option 2: Other Email Providers

#### **Outlook/Hotmail**
- **SMTP Host**: `smtp-mail.outlook.com`
- **SMTP Port**: `587`
- **Encryption**: `TLS`

#### **Yahoo Mail**
- **SMTP Host**: `smtp.mail.yahoo.com`
- **SMTP Port**: `587` or `465`
- **Encryption**: `TLS` or `SSL`

#### **Custom Domain Email**
- Contact your hosting provider for SMTP settings
- Usually: `mail.yourdomain.com` or `smtp.yourdomain.com`

### Option 3: Server Mail (Basic)

If your server has mail configured:
1. **Disable SMTP** in admin panel
2. **Uses PHP mail()** function
3. **May require server configuration**

## 🧪 Testing Email

1. **Go to**: `/admin/email-settings.php`
2. **Scroll to**: "Test Email Configuration"
3. **Enter test email**: Your email address
4. **Click**: "Send Test Email"
5. **Check**: Email inbox and spam folder

## 🔧 Production Setup Steps

### Step 1: Configure Email
```
1. Visit: /admin/email-settings.php
2. Configure SMTP settings
3. Test email functionality
4. Verify emails are received
```

### Step 2: Update Configuration
```
1. Edit: config.php
2. Update: ADMIN_EMAIL constant
3. Set: Production domain URLs
4. Change: Admin password
```

### Step 3: Test Contact Form
```
1. Visit: /contact page
2. Submit test message
3. Check: Admin receives notification
4. Verify: Submission appears in admin panel
```

## 📋 Email Features

### ✅ What's Working Now
- **Database Storage**: All contact submissions saved
- **Form Validation**: Complete validation system
- **Admin Interface**: View and manage submissions
- **Email Templates**: Professional HTML templates ready
- **Error Handling**: Proper error logging

### 📧 Email Templates Included
1. **Admin Notification**: Sent when contact form submitted
2. **User Confirmation**: Sent to user confirming receipt
3. **Professional Design**: HTML templates with branding

### 🎛️ Admin Features
- **Email Settings Panel**: Easy SMTP configuration
- **Contact Submissions**: View all form submissions
- **Status Management**: Mark as read/replied
- **Email Testing**: Built-in test functionality

## 🚨 Common Issues & Solutions

### Issue: "Failed to connect to mailserver"
**Solution**: Configure SMTP settings in admin panel

### Issue: Emails going to spam
**Solutions**:
- Use proper "From" email address
- Configure SPF/DKIM records for domain
- Use reputable SMTP service (Gmail, etc.)

### Issue: SMTP authentication failed
**Solutions**:
- Check username/password
- Use app password for Gmail
- Verify 2FA is enabled for Gmail

### Issue: Port connection refused
**Solutions**:
- Try different ports (587, 465, 25)
- Check firewall settings
- Contact hosting provider

## 📞 Support

### Development Environment
- **Email**: Uses PHP mail() (may not work locally)
- **Database**: All submissions saved and viewable
- **Testing**: Use admin panel test feature

### Production Environment
- **SMTP Required**: For reliable email delivery
- **SSL Recommended**: For secure email transmission
- **Monitoring**: Check admin panel for submissions

## 🎉 Quick Start

**For immediate use**:
1. Contact form saves to database ✅
2. Admin can view submissions ✅
3. Email setup optional for notifications

**For full functionality**:
1. Configure SMTP via admin panel (15 minutes)
2. Test email delivery
3. Update production settings

---

**The contact system is production-ready with or without email configuration!**
