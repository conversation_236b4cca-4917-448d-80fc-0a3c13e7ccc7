<?php
/**
 * Update Hero Headers Database Schema
 * Add padding controls and fix any missing columns
 */

define('MONOLITH_ACCESS', true);
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';

try {
    $db = Database::getConnection();
    
    echo "<h2>Updating Hero Headers Database Schema</h2>\n";
    
    // Check if padding columns exist
    $stmt = $db->query("DESCRIBE hero_headers");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $has_padding_top = in_array('padding_top', $columns);
    $has_padding_bottom = in_array('padding_bottom', $columns);
    
    if (!$has_padding_top || !$has_padding_bottom) {
        echo "<p>Adding padding control columns...</p>\n";
        
        if (!$has_padding_top) {
            $db->exec("ALTER TABLE hero_headers ADD COLUMN padding_top VARCHAR(20) DEFAULT '4rem' AFTER height_custom");
            echo "<p>✅ Added padding_top column</p>\n";
        }
        
        if (!$has_padding_bottom) {
            $db->exec("ALTER TABLE hero_headers ADD COLUMN padding_bottom VARCHAR(20) DEFAULT '4rem' AFTER padding_top");
            echo "<p>✅ Added padding_bottom column</p>\n";
        }
    } else {
        echo "<p>✅ Padding columns already exist</p>\n";
    }
    
    // Update existing records to have default padding values
    $stmt = $db->prepare("UPDATE hero_headers SET padding_top = '4rem', padding_bottom = '4rem' WHERE padding_top IS NULL OR padding_bottom IS NULL");
    $updated = $stmt->execute();
    
    if ($updated) {
        echo "<p>✅ Updated existing records with default padding values</p>\n";
    }
    
    echo "<h3>Current Hero Headers Schema:</h3>\n";
    $stmt = $db->query("DESCRIBE hero_headers");
    $schema = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
    foreach ($schema as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
}
?>
