MONOLITH DESIGN CO. - INSTALLATION GUIDE
=========================================

Welcome to Monolith Design Co.! This guide will walk you through the complete installation process.

📋 PRE-INSTALLATION CHECKLIST
==============================

Before you begin, ensure you have:
✅ Web hosting with PHP 7.4+ and MySQL 5.7+
✅ FTP/SFTP access to your server
✅ Database access (cPanel, phpMyAdmin, or similar)
✅ Text editor for configuration

🚀 QUICK INSTALLATION (5 MINUTES)
==================================

Step 1: Download & Extract
--------------------------
1. Download the theme package from ThemeForest
2. Extract the ZIP file to your computer
3. You'll see these files:
   - monolith-design/ (main theme folder)
   - documentation.html
   - License.txt
   - Changelog.txt

Step 2: Upload Files
--------------------
1. Using FTP/SFTP, upload the entire 'monolith-design' folder to your web root
   (usually public_html, www, or htdocs)
2. Your structure should look like:
   yoursite.com/
   ├── index.php
   ├── config.php
   ├── admin/
   ├── assets/
   └── ... (other files)

Step 3: Create Database
-----------------------
1. Login to your hosting control panel (cPanel, etc.)
2. Go to MySQL Databases or Database section
3. Create a new database called 'monolith_design'
4. Create a database user and assign it to the database
5. Note down: database name, username, password

Step 4: Run Auto-Installer
---------------------------
1. Visit: yoursite.com/install.php
2. Enter your database details:
   - Database Host: localhost (usually)
   - Database Name: monolith_design
   - Username: your_db_user
   - Password: your_db_password
3. Click "Install Database"
4. ✅ Success! Delete install.php file when prompted

Step 5: Configure Site
----------------------
1. Edit config.php file:
   - Update SITE_URL to your domain
   - Verify database credentials
   - Change admin password hash
2. Set file permissions:
   - Folders: 755
   - Files: 644
   - /assets/images/uploads/: 755 (writable)

🎯 YOU'RE DONE!
===============
Visit yoursite.com to see your new website!
Visit yoursite.com/admin to access the admin panel

Default admin login:
Username: admin
Password: admin123
⚠️ CHANGE THIS IMMEDIATELY!

🔧 MANUAL INSTALLATION (ALTERNATIVE)
====================================

If the auto-installer doesn't work:

1. Create MySQL Database
------------------------
- Create database: monolith_design
- Import database.sql file through phpMyAdmin
- Create database user and assign permissions

2. Configure Connection
-----------------------
Edit config.php:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('SITE_URL', 'https://yourwebsite.com');
```

3. Set Permissions
------------------
SSH or FTP commands:
```bash
chmod 755 assets/images/uploads/
chmod 644 config.php
chmod 755 admin/
```

📁 FILE PERMISSIONS GUIDE
==========================

Correct permissions for security:

Directories (755):
- assets/
- admin/
- includes/
- templates/
- components/
- assets/images/uploads/ (must be writable)

Files (644):
- *.php files
- *.css files
- *.js files
- .htaccess

Secure files (600):
- config.php (optional, for extra security)

🔐 SECURITY CONFIGURATION
==========================

ESSENTIAL SECURITY STEPS:

1. Change Admin Password
------------------------
Admin Panel → Theme Options → Change password immediately

2. Update config.php
--------------------
```php
// Change this line:
define('ADMIN_PASSWORD_HASH', password_hash('YOUR_NEW_PASSWORD', PASSWORD_DEFAULT));

// Update admin email:
define('ADMIN_EMAIL', '<EMAIL>');
```

3. Remove Install File
----------------------
Delete install.php after successful installation

4. Secure File Permissions
---------------------------
Ensure .htaccess file is properly uploaded (contains security rules)

🌐 HOSTING COMPATIBILITY
=========================

✅ TESTED HOSTING PROVIDERS:
- cPanel hosting
- Shared hosting
- VPS/Dedicated servers
- Cloud hosting (AWS, DigitalOcean)

✅ REQUIREMENTS MET BY MOST HOSTS:
- PHP 7.4+ (most have 8.0+)
- MySQL 5.7+ (most have 8.0+)
- Apache with mod_rewrite
- 128MB+ memory (most have 256MB+)

⚠️ HOSTING REQUIREMENTS:
- mod_rewrite must be enabled
- PHP extensions: PDO, GD, JSON
- File upload permissions

🔧 CUSTOMIZATION QUICK START
=============================

After installation, customize your site:

1. Admin Dashboard
------------------
yoursite.com/admin
- Theme Options: Colors, logos, contact info
- Sliders: Homepage hero content
- Services: Add your services
- Projects: Upload portfolio
- Team: Add team members

2. Replace Demo Content
-----------------------
- Upload your logo (SVG recommended)
- Replace demo images in /assets/images/
- Update contact information
- Add your Google Maps embed code

3. Content Updates
------------------
- About page: Edit about.php
- Services: Add through admin panel
- Projects: Upload through admin panel
- Contact info: Update in admin panel

📞 SUPPORT & TROUBLESHOOTING
============================

Common Issues & Solutions:

🔴 "Database connection failed"
→ Check config.php database credentials
→ Ensure database exists and user has permissions

🔴 "Page not found" errors
→ Check .htaccess file uploaded correctly
→ Ensure mod_rewrite is enabled on server

🔴 "Permission denied" on uploads
→ Set /assets/images/uploads/ to 755 permissions
→ Check if server allows file uploads

🔴 Admin panel not accessible
→ Clear browser cache
→ Check admin/ folder uploaded correctly
→ Verify .htaccess rules

🔴 CSS/JS not loading
→ Check SITE_URL in config.php
→ Ensure /assets/ folder uploaded completely

📊 PERFORMANCE OPTIMIZATION
============================

After installation, optimize for speed:

1. Enable HTTPS
---------------
Update config.php when SSL is active:
```php
// Force HTTPS
$protocol = 'https';
```

2. Image Optimization
---------------------
- Compress images before upload
- Use WebP format when possible
- Optimize hero images (1920x1080 max)

3. Caching
----------
- Enable server-side caching if available
- Use CDN for static assets
- .htaccess already includes browser caching rules

🎉 CONGRATULATIONS!
===================

Your Monolith Design Co. website is now ready!

Next Steps:
1. ✅ Change admin password
2. ✅ Update contact information
3. ✅ Replace demo images
4. ✅ Add your content
5. ✅ Test all functionality
6. ✅ Go live!

Need help? Check documentation.html or contact support through ThemeForest.

Happy building! 🏗️
