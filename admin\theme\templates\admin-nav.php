<?php
/**
 * Admin Navigation Template
 * Navigation component for admin dashboard
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Get current page for active state
$current_page = basename($_SERVER['PHP_SELF']);

// Navigation items with icons
$nav_items = [
    [
        'title' => 'Theme',
        'url' => 'index.php',
        'icon' => 'fas fa-palette',
        'active' => $current_page === 'index.php'
    ],
    [
        'title' => 'Sliders',
        'url' => 'sliders.php',
        'icon' => 'fas fa-images',
        'active' => $current_page === 'sliders.php'
    ],
    [
        'title' => 'Hero',
        'url' => 'hero-sections.php',
        'icon' => 'fas fa-star',
        'active' => $current_page === 'hero-sections.php'
    ],
    [
        'title' => 'Hero Headers',
        'url' => 'hero-headers.php',
        'icon' => 'fas fa-image',
        'active' => $current_page === 'hero-headers.php'
    ],
    [
        'title' => 'Services',
        'url' => 'services.php',
        'icon' => 'fas fa-tools',
        'active' => $current_page === 'services.php'
    ],
    [
        'title' => 'Projects',
        'url' => 'projects.php',
        'icon' => 'fas fa-building',
        'active' => $current_page === 'projects.php'
    ],
    [
        'title' => 'Team',
        'url' => 'team.php',
        'icon' => 'fas fa-users',
        'active' => $current_page === 'team.php'
    ],
    [
        'title' => 'Reviews',
        'url' => 'testimonials.php',
        'icon' => 'fas fa-quote-right',
        'active' => $current_page === 'testimonials.php'
    ],
    [
        'title' => 'Blog',
        'url' => 'blog.php',
        'icon' => 'fas fa-blog',
        'active' => $current_page === 'blog.php'
    ],
    [
        'title' => 'Contact',
        'url' => 'contacts.php',
        'icon' => 'fas fa-envelope',
        'active' => $current_page === 'contacts.php'
    ],
    [
        'title' => 'Email',
        'url' => 'email-settings.php',
        'icon' => 'fas fa-cog',
        'active' => $current_page === 'email-settings.php'
    ]
];
?>

<nav class="admin-nav">
    <div class="admin-nav-container">

        <!-- Navigation Items -->
        <div class="admin-nav-content">
            <ul class="admin-nav-list">
                <?php foreach ($nav_items as $item): ?>
                    <li class="nav-item">
                        <a href="<?php echo htmlspecialchars($item['url']); ?>" 
                           class="nav-link <?php echo $item['active'] ? 'active' : ''; ?>"
                           <?php if ($item['active']): ?>aria-current="page"<?php endif; ?>>
                            <i class="<?php echo htmlspecialchars($item['icon']); ?> nav-icon"></i>
                            <span class="nav-text"><?php echo htmlspecialchars($item['title']); ?></span>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <!-- Navigation Actions -->
        <div class="nav-actions d-none d-lg-flex">
            <div class="nav-status">
                <span class="status-indicator online" title="System Online"></span>
                <span class="status-text">Online</span>
            </div>
        </div>
        
    </div>
</nav>
