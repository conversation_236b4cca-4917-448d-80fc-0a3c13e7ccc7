<?php
/**
 * Database Schema Update Script
 * Run this once to update the sliders and testimonials tables
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Simple security check
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
$client_ip = $_SERVER['REMOTE_ADDR'] ?? $_SERVER['HTTP_CLIENT_IP'] ?? $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 'unknown';

if (!in_array($client_ip, $allowed_ips) && !isset($_GET['force'])) {
    die('Access denied. Run this script locally only.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Schema Update</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .info { color: #3498db; }
        .warning { color: #f39c12; }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
        }
        .btn {
            background: #E67E22;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 1rem 0;
        }
        .btn:hover {
            background: #d35400;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Schema Update</h1>
        <p>This script will update the sliders and testimonials tables with new columns for enhanced functionality.</p>
        
        <?php if (isset($_POST['run_update'])): ?>
            <h2>Running Database Update...</h2>
            <pre>
<?php
try {
    $db = Database::getConnection();
    
    echo "=== UPDATING SLIDERS TABLE SCHEMA ===\n\n";
    
    // Check if new columns already exist
    $stmt = $db->query("DESCRIBE sliders");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $new_columns = [
        'text_color' => "VARCHAR(7) DEFAULT '#ffffff' AFTER button_link",
        'overlay_color' => "VARCHAR(7) DEFAULT '#000000' AFTER text_color", 
        'overlay_opacity' => "DECIMAL(3,2) DEFAULT 0.50 AFTER overlay_color",
        'title_char_limit' => "INT DEFAULT 50 AFTER overlay_opacity",
        'description_char_limit' => "INT DEFAULT 100 AFTER title_char_limit"
    ];
    
    foreach ($new_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $columns)) {
            $sql = "ALTER TABLE sliders ADD COLUMN $column_name $column_definition";
            $db->exec($sql);
            echo "✅ Added column: $column_name\n";
        } else {
            echo "ℹ️  Column already exists: $column_name\n";
        }
    }
    
    echo "\n=== UPDATING EXISTING SLIDERS WITH DEFAULT VALUES ===\n\n";
    
    // Update existing sliders with default values
    $update_sql = "
        UPDATE sliders SET 
            text_color = COALESCE(text_color, '#ffffff'),
            overlay_color = COALESCE(overlay_color, '#000000'),
            overlay_opacity = COALESCE(overlay_opacity, 0.50),
            title_char_limit = COALESCE(title_char_limit, 50),
            description_char_limit = COALESCE(description_char_limit, 100)
        WHERE text_color IS NULL OR overlay_color IS NULL OR overlay_opacity IS NULL 
           OR title_char_limit IS NULL OR description_char_limit IS NULL
    ";
    
    $result = $db->exec($update_sql);
    echo "✅ Updated $result existing sliders with default styling values\n";
    
    echo "\n=== ADDING TESTIMONIAL BACKGROUND IMAGE COLUMN ===\n\n";
    
    // Check if testimonials table needs background_image column
    $stmt = $db->query("DESCRIBE testimonials");
    $testimonial_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('background_image', $testimonial_columns)) {
        $sql = "ALTER TABLE testimonials ADD COLUMN background_image VARCHAR(500) DEFAULT NULL AFTER client_image";
        $db->exec($sql);
        echo "✅ Added background_image column to testimonials table\n";
    } else {
        echo "ℹ️  background_image column already exists in testimonials table\n";
    }
    
    echo "\n=== SCHEMA UPDATE COMPLETE ===\n\n";
    echo "The sliders table now supports:\n";
    echo "- Custom text colors\n";
    echo "- Custom overlay colors\n"; 
    echo "- Adjustable overlay opacity\n";
    echo "- Title character limits\n";
    echo "- Description character limits\n";
    echo "\nThe testimonials table now supports:\n";
    echo "- Background images for each testimonial\n";
    echo "\nYou can now use the admin slider management page to customize these settings.\n";
    
} catch (Exception $e) {
    echo "❌ Error updating schema: " . $e->getMessage() . "\n";
}
?>
            </pre>
            
            <p class="success"><strong>Database update completed!</strong></p>
            <p>You can now:</p>
            <ul>
                <li>Use the <a href="admin/sliders.php">Admin Slider Management</a> page</li>
                <li>Customize text colors, overlay colors, and opacity</li>
                <li>Set character limits for titles and descriptions</li>
                <li>Manage testimonial background images</li>
            </ul>
            
            <p class="warning"><strong>Important:</strong> Delete this file (update-database.php) after running the update for security.</p>
            
        <?php else: ?>
            <h2>Before You Begin</h2>
            <p>This update will add the following columns to your database:</p>
            
            <h3>Sliders Table:</h3>
            <ul>
                <li><code>text_color</code> - Custom text colors for each slide</li>
                <li><code>overlay_color</code> - Custom overlay colors</li>
                <li><code>overlay_opacity</code> - Adjustable overlay transparency</li>
                <li><code>title_char_limit</code> - Character limits for titles</li>
                <li><code>description_char_limit</code> - Character limits for descriptions</li>
            </ul>
            
            <h3>Testimonials Table:</h3>
            <ul>
                <li><code>background_image</code> - Background images for testimonial slides</li>
            </ul>
            
            <p class="info"><strong>Note:</strong> This update is safe and will not affect existing data. All new columns have default values.</p>
            
            <form method="POST">
                <button type="submit" name="run_update" class="btn">Run Database Update</button>
            </form>
        <?php endif; ?>
        
        <hr style="margin: 2rem 0;">
        <p><small>After running this update, you can delete this file for security.</small></p>
    </div>
</body>
</html>
