<?php
/**
 * Create Upload Directories for Hero Headers
 */

define('MONOLITH_ACCESS', true);
require_once dirname(__DIR__) . '/config.php';

try {
    echo "<h2>Creating Upload Directories</h2>\n";
    
    // Create hero headers upload directory
    $upload_dir = dirname(__DIR__) . '/assets/images/hero-headers';
    
    if (!file_exists($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<p>✅ Created directory: assets/images/hero-headers/</p>\n";
        } else {
            echo "<p>❌ Failed to create directory: assets/images/hero-headers/</p>\n";
        }
    } else {
        echo "<p>✅ Directory already exists: assets/images/hero-headers/</p>\n";
    }
    
    // Create .htaccess for security
    $htaccess_content = "# Prevent PHP execution in upload directory\n";
    $htaccess_content .= "php_flag engine off\n";
    $htaccess_content .= "\n# Allow only image files\n";
    $htaccess_content .= "<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\n";
    $htaccess_content .= "    Order Allow,Deny\n";
    $htaccess_content .= "    Allow from all\n";
    $htaccess_content .= "</FilesMatch>\n";
    
    $htaccess_file = $upload_dir . '/.htaccess';
    if (file_put_contents($htaccess_file, $htaccess_content)) {
        echo "<p>✅ Created security .htaccess file</p>\n";
    } else {
        echo "<p>❌ Failed to create .htaccess file</p>\n";
    }
    
    // Check directory permissions
    if (is_writable($upload_dir)) {
        echo "<p>✅ Upload directory is writable</p>\n";
    } else {
        echo "<p>❌ Upload directory is not writable - check permissions</p>\n";
    }
    
    echo "<h3>✅ Upload Directory Setup Complete!</h3>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
}
?>
