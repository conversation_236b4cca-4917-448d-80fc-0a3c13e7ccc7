#!/bin/bash

# Monolith Design Co. - Production Package Preparation Script
# This script prepares the theme for ThemeForest submission

echo "🏗️ Monolith Design Co. - Preparing Production Package..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -f "config.php" ]; then
    echo -e "${RED}❌ Error: Please run this script from the monolith-design directory${NC}"
    exit 1
fi

echo -e "${BLUE}📁 Cleaning up development files...${NC}"

# Remove development files
rm -f install.php 2>/dev/null
rm -f .DS_Store 2>/dev/null
rm -f Thumbs.db 2>/dev/null
find . -name "*.log" -delete 2>/dev/null
find . -name ".git*" -delete 2>/dev/null

echo -e "${GREEN}✅ Development files cleaned${NC}"

echo -e "${BLUE}🔒 Securing file permissions...${NC}"

# Set proper file permissions
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;
chmod 755 assets/images/uploads/

echo -e "${GREEN}✅ File permissions set${NC}"

echo -e "${BLUE}🗜️ Optimizing assets...${NC}"

# Remove any empty upload directories
find assets/images/uploads/ -type d -empty -delete 2>/dev/null

echo -e "${GREEN}✅ Assets optimized${NC}"

echo -e "${BLUE}📋 Validating required files...${NC}"

# Check for required files
required_files=(
    "config.php"
    "database.sql"
    ".htaccess"
    "index.php"
    "documentation.html"
    "License.txt"
    "Changelog.txt"
    "Credits.txt"
    "INSTALLATION.txt"
    "README.md"
)

missing_files=()

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo -e "${GREEN}✅ All required files present${NC}"
else
    echo -e "${RED}❌ Missing required files:${NC}"
    for file in "${missing_files[@]}"; do
        echo -e "${RED}   - $file${NC}"
    done
    exit 1
fi

echo -e "${BLUE}🔍 Checking configuration...${NC}"

# Check if config.php has production settings
if grep -q "error_reporting(E_ALL)" config.php; then
    echo -e "${YELLOW}⚠️  Warning: config.php may still have development error reporting${NC}"
    echo -e "${YELLOW}   Make sure to set production error handling${NC}"
fi

if grep -q "localhost" config.php; then
    echo -e "${YELLOW}⚠️  Warning: config.php contains localhost references${NC}"
    echo -e "${YELLOW}   Users will need to update for their domain${NC}"
fi

echo -e "${GREEN}✅ Configuration check complete${NC}"

echo -e "${BLUE}📦 Preparing package structure...${NC}"

# Create package info
package_date=$(date +"%Y-%m-%d")
echo "Package prepared: $package_date" > PACKAGE_INFO.txt
echo "Version: 1.0.0" >> PACKAGE_INFO.txt
echo "For ThemeForest submission" >> PACKAGE_INFO.txt

echo -e "${GREEN}✅ Package structure ready${NC}"

echo -e "${BLUE}🧹 Final cleanup...${NC}"

# Remove any remaining temp files
find . -name "*~" -delete 2>/dev/null
find . -name "*.tmp" -delete 2>/dev/null
find . -name "*.bak" -delete 2>/dev/null

echo -e "${GREEN}✅ Final cleanup complete${NC}"

echo ""
echo -e "${GREEN}🎉 PRODUCTION PACKAGE READY!${NC}"
echo "=================================================="
echo -e "${BLUE}📋 Next Steps:${NC}"
echo -e "   1. Review all files one final time"
echo -e "   2. Test the package on a clean server"
echo -e "   3. Create preview images for ThemeForest"
echo -e "   4. Zip the entire directory for submission"
echo -e "   5. Upload to ThemeForest"
echo ""
echo -e "${YELLOW}⚠️  Remember to:${NC}"
echo -e "   - Update config.php with production settings"
echo -e "   - Test installation process thoroughly"
echo -e "   - Ensure all demo images are properly licensed"
echo -e "   - Verify all links and functionality work"
echo ""
echo -e "${GREEN}🏗️ Monolith Design Co. - Ready for Launch!${NC}"
