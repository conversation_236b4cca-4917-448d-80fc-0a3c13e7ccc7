<?php
/**
 * Hero Headers Admin Interface
 * Manages page header hero sections across the site
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = Database::getConnection();

        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create':
                    $stmt = $db->prepare("
                        INSERT INTO hero_headers (
                            page_name, page_title, subtitle, show_breadcrumbs,
                            background_type, background_image, background_gradient, background_color, background_opacity,
                            height_type, height_custom, title_color, subtitle_color, breadcrumb_color,
                            show_cta_button, cta_button_text, cta_button_link, cta_button_color, cta_button_text_color,
                            active
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");

                    $stmt->execute([
                        $_POST['page_name'], $_POST['page_title'], $_POST['subtitle'],
                        isset($_POST['show_breadcrumbs']) ? 1 : 0,
                        $_POST['background_type'], $_POST['background_image'], $_POST['background_gradient'],
                        $_POST['background_color'], $_POST['background_opacity'],
                        $_POST['height_type'], $_POST['height_custom'],
                        $_POST['title_color'], $_POST['subtitle_color'], $_POST['breadcrumb_color'],
                        isset($_POST['show_cta_button']) ? 1 : 0, $_POST['cta_button_text'], $_POST['cta_button_link'],
                        $_POST['cta_button_color'], $_POST['cta_button_text_color'],
                        isset($_POST['active']) ? 1 : 0
                    ]);

                    $message = 'Hero header created successfully!';
                    break;
                    
                case 'update':
                    $stmt = $db->prepare("
                        UPDATE hero_headers SET
                            page_title = ?, subtitle = ?, show_breadcrumbs = ?,
                            background_type = ?, background_image = ?, background_gradient = ?,
                            background_color = ?, background_opacity = ?,
                            height_type = ?, height_custom = ?,
                            title_color = ?, subtitle_color = ?, breadcrumb_color = ?,
                            show_cta_button = ?, cta_button_text = ?, cta_button_link = ?,
                            cta_button_color = ?, cta_button_text_color = ?,
                            active = ?, updated_at = NOW()
                        WHERE id = ?
                    ");

                    $stmt->execute([
                        $_POST['page_title'], $_POST['subtitle'],
                        isset($_POST['show_breadcrumbs']) ? 1 : 0,
                        $_POST['background_type'], $_POST['background_image'], $_POST['background_gradient'],
                        $_POST['background_color'], $_POST['background_opacity'],
                        $_POST['height_type'], $_POST['height_custom'],
                        $_POST['title_color'], $_POST['subtitle_color'], $_POST['breadcrumb_color'],
                        isset($_POST['show_cta_button']) ? 1 : 0, $_POST['cta_button_text'], $_POST['cta_button_link'],
                        $_POST['cta_button_color'], $_POST['cta_button_text_color'],
                        isset($_POST['active']) ? 1 : 0,
                        $_POST['id']
                    ]);

                    $message = 'Hero header updated successfully!';
                    break;

                case 'delete':
                    $stmt = $db->prepare("DELETE FROM hero_headers WHERE id = ?");
                    $stmt->execute([$_POST['id']]);

                    $message = 'Hero header deleted successfully!';
                    break;
            }
        }
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get all hero headers
$db = Database::getConnection();
$stmt = $db->query("SELECT * FROM hero_headers ORDER BY page_name ASC");
$hero_headers = $stmt->fetchAll();

// Get specific hero header for editing
$edit_hero_header = null;
if (isset($_GET['edit'])) {
    $stmt = $db->prepare("SELECT * FROM hero_headers WHERE id = ?");
    $stmt->execute([$_GET['edit']]);
    $edit_hero_header = $stmt->fetch();
}

// Calculate statistics
$total_headers = count($hero_headers);
$active_headers = count(array_filter($hero_headers, function($h) { return $h['active']; }));
$inactive_headers = $total_headers - $active_headers;
$headers_with_cta = count(array_filter($hero_headers, function($h) { return $h['show_cta_button']; }));

// Render the page using the admin theme system
renderAdminPage('management', [
    'page_title' => 'Hero Headers Management',
    'page_icon' => 'fas fa-image',
    'page_description' => 'Manage page header hero sections with titles, breadcrumbs, backgrounds, and call-to-action buttons.',
    'management_title' => 'Hero Headers',
    'management_description' => 'Create and customize page header sections that appear at the top of pages with professional styling and flexible content options.',
    'management_actions' => [
        [
            'url' => '#',
            'label' => 'Add New Hero Header',
            'class' => 'btn-primary',
            'icon' => 'fas fa-plus',
            'onclick' => 'toggleAddForm()'
        ]
    ],
    'show_search' => true,
    'search_placeholder' => 'Search hero headers by page name, title, or subtitle...',
    'search_target' => '.searchable-item',
    'show_stats' => true,
    'stats_cards' => [
        [
            'icon' => 'fas fa-image',
            'number' => $total_headers,
            'label' => 'Total Headers'
        ],
        [
            'icon' => 'fas fa-check-circle',
            'number' => $active_headers,
            'label' => 'Active Headers'
        ],
        [
            'icon' => 'fas fa-times-circle',
            'number' => $inactive_headers,
            'label' => 'Inactive Headers'
        ],
        [
            'icon' => 'fas fa-mouse-pointer',
            'number' => $headers_with_cta,
            'label' => 'With CTA Button'
        ]
    ],
    'show_table' => true,
    'table_title' => 'All Hero Headers',
    'table_content_file' => __DIR__ . '/theme/content/hero-headers-table.php',
    'custom_content_before_table' => function() use ($edit_hero_header) {
        include __DIR__ . '/theme/content/hero-headers-form.php';
    },
    'message' => $message,
    'error' => $error,
    'hero_headers' => $hero_headers,
    'edit_hero_header' => $edit_hero_header,
    'custom_js' => '
        function toggleAddForm() {
            const form = document.getElementById("heroHeaderForm");
            if (form) {
                form.style.display = form.style.display === "none" ? "block" : "none";
                if (form.style.display === "block") {
                    document.getElementById("page_name").focus();
                }
            }
        }
    '
]);
?>
<body>
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col">
                    <h1><i class="fas fa-image me-2"></i>Hero Headers Management</h1>
                    <p class="mb-0">Manage page header hero sections across the website</p>
                </div>
                <div class="col-auto">
                    <a href="index.php" class="btn btn-light">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Hero Headers List -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Existing Hero Headers</h5>
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#createModal">
                    <i class="fas fa-plus me-1"></i>Add New Hero Header
                </button>
            </div>
            <div class="card-body">
                <?php if (empty($hero_headers)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-image fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No hero headers found</h5>
                        <p class="text-muted">Create your first hero header to get started.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Page</th>
                                    <th>Title</th>
                                    <th>Subtitle</th>
                                    <th>Background</th>
                                    <th>Height</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($hero_headers as $header): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($header['page_name']); ?></strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($header['page_title']); ?></td>
                                        <td><?php echo htmlspecialchars(substr($header['subtitle'], 0, 50)) . (strlen($header['subtitle']) > 50 ? '...' : ''); ?></td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo ucfirst($header['background_type']); ?></span>
                                            <span class="color-preview" style="background-color: <?php echo $header['background_color']; ?>"></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo ucfirst($header['height_type']); ?></span>
                                            <?php if ($header['height_type'] === 'custom'): ?>
                                                <small>(<?php echo $header['height_custom']; ?>px)</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge status-badge <?php echo $header['active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                <?php echo $header['active'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                        </td>
                                        <td class="table-actions">
                                            <a href="?edit=<?php echo $header['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteHeader(<?php echo $header['id']; ?>, '<?php echo htmlspecialchars($header['page_name']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Edit Form -->
        <?php if ($edit_header): ?>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Hero Header: <?php echo htmlspecialchars($edit_header['page_name']); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="editForm">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="id" value="<?php echo $edit_header['id']; ?>">
                        
                        <!-- Content Section -->
                        <div class="form-section">
                            <h5><i class="fas fa-text-width me-2"></i>Content Settings</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Page Title</label>
                                        <input type="text" class="form-control" name="page_title" value="<?php echo htmlspecialchars($edit_header['page_title']); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Subtitle</label>
                                        <input type="text" class="form-control" name="subtitle" value="<?php echo htmlspecialchars($edit_header['subtitle']); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" name="show_breadcrumbs" id="show_breadcrumbs" <?php echo $edit_header['show_breadcrumbs'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="show_breadcrumbs">Show Breadcrumbs</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" name="active" id="active" <?php echo $edit_header['active'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="active">Active</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Background Section -->
                        <div class="form-section">
                            <h5><i class="fas fa-palette me-2"></i>Background Settings</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Background Type</label>
                                        <select class="form-select" name="background_type" id="background_type" onchange="toggleBackgroundFields()">
                                            <option value="gradient" <?php echo $edit_header['background_type'] === 'gradient' ? 'selected' : ''; ?>>Gradient</option>
                                            <option value="image" <?php echo $edit_header['background_type'] === 'image' ? 'selected' : ''; ?>>Image</option>
                                            <option value="color" <?php echo $edit_header['background_type'] === 'color' ? 'selected' : ''; ?>>Solid Color</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Overlay Color</label>
                                        <input type="color" class="form-control form-control-color" name="background_color" value="<?php echo $edit_header['background_color']; ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Overlay Opacity</label>
                                        <input type="range" class="form-range" name="background_opacity" min="0" max="1" step="0.1" value="<?php echo $edit_header['background_opacity']; ?>" oninput="updateOpacityValue(this.value)">
                                        <small class="text-muted">Opacity: <span id="opacityValue"><?php echo $edit_header['background_opacity']; ?></span></small>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6" id="imageField">
                                    <div class="mb-3">
                                        <label class="form-label">Background Image URL</label>
                                        <input type="url" class="form-control" name="background_image" value="<?php echo htmlspecialchars($edit_header['background_image']); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6" id="gradientField">
                                    <div class="mb-3">
                                        <label class="form-label">CSS Gradient</label>
                                        <textarea class="form-control" name="background_gradient" rows="2"><?php echo htmlspecialchars($edit_header['background_gradient']); ?></textarea>
                                        <small class="text-muted">Example: linear-gradient(135deg, #667eea 0%, #764ba2 100%)</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Height & Colors Section -->
                        <div class="form-section">
                            <h5><i class="fas fa-arrows-alt-v me-2"></i>Height & Colors</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Height Type</label>
                                        <select class="form-select" name="height_type" id="height_type" onchange="toggleCustomHeight()">
                                            <option value="small" <?php echo $edit_header['height_type'] === 'small' ? 'selected' : ''; ?>>Small (300px)</option>
                                            <option value="medium" <?php echo $edit_header['height_type'] === 'medium' ? 'selected' : ''; ?>>Medium (400px)</option>
                                            <option value="large" <?php echo $edit_header['height_type'] === 'large' ? 'selected' : ''; ?>>Large (600px)</option>
                                            <option value="custom" <?php echo $edit_header['height_type'] === 'custom' ? 'selected' : ''; ?>>Custom</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3" id="customHeightField">
                                    <div class="mb-3">
                                        <label class="form-label">Custom Height (px)</label>
                                        <input type="number" class="form-control" name="height_custom" value="<?php echo $edit_header['height_custom']; ?>" min="200" max="1000">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label class="form-label">Title Color</label>
                                        <input type="color" class="form-control form-control-color" name="title_color" value="<?php echo $edit_header['title_color']; ?>">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label class="form-label">Subtitle Color</label>
                                        <input type="color" class="form-control form-control-color" name="subtitle_color" value="<?php echo $edit_header['subtitle_color']; ?>">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label class="form-label">Breadcrumb Color</label>
                                        <input type="color" class="form-control form-control-color" name="breadcrumb_color" value="<?php echo $edit_header['breadcrumb_color']; ?>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- CTA Button Section -->
                        <div class="form-section">
                            <h5><i class="fas fa-mouse-pointer me-2"></i>Call-to-Action Button</h5>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-check mb-3">
                                        <input type="checkbox" class="form-check-input" name="show_cta_button" id="show_cta_button" <?php echo $edit_header['show_cta_button'] ? 'checked' : ''; ?> onchange="toggleCTAFields()">
                                        <label class="form-check-label" for="show_cta_button">Show CTA Button</label>
                                    </div>
                                </div>
                            </div>
                            <div id="ctaFields">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Button Text</label>
                                            <input type="text" class="form-control" name="cta_button_text" value="<?php echo htmlspecialchars($edit_header['cta_button_text']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Button Link</label>
                                            <input type="url" class="form-control" name="cta_button_link" value="<?php echo htmlspecialchars($edit_header['cta_button_link']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Button Color</label>
                                            <input type="color" class="form-control form-control-color" name="cta_button_color" value="<?php echo $edit_header['cta_button_color']; ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">Text Color</label>
                                            <input type="color" class="form-control form-control-color" name="cta_button_text_color" value="<?php echo $edit_header['cta_button_text_color']; ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="hero-headers.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Hero Header
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Create Modal -->
    <div class="modal fade" id="createModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Hero Header</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="create">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Page Name *</label>
                                    <input type="text" class="form-control" name="page_name" required>
                                    <small class="text-muted">Unique identifier (e.g., 'about', 'services')</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Page Title *</label>
                                    <input type="text" class="form-control" name="page_title" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Subtitle</label>
                            <input type="text" class="form-control" name="subtitle">
                        </div>
                        
                        <!-- Default values for quick creation -->
                        <input type="hidden" name="background_type" value="gradient">
                        <input type="hidden" name="background_gradient" value="linear-gradient(135deg, rgba(55, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)">
                        <input type="hidden" name="background_color" value="#a99eff">
                        <input type="hidden" name="background_opacity" value="0.60">
                        <input type="hidden" name="height_type" value="medium">
                        <input type="hidden" name="height_custom" value="400">
                        <input type="hidden" name="title_color" value="#ffffff">
                        <input type="hidden" name="subtitle_color" value="#ffffff">
                        <input type="hidden" name="breadcrumb_color" value="#ffffff">
                        <input type="hidden" name="cta_button_color" value="#E67E22">
                        <input type="hidden" name="cta_button_text_color" value="#ffffff">
                        
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="show_breadcrumbs" id="create_show_breadcrumbs" checked>
                            <label class="form-check-label" for="create_show_breadcrumbs">Show Breadcrumbs</label>
                        </div>
                        
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="active" id="create_active" checked>
                            <label class="form-check-label" for="create_active">Active</label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Hero Header</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Form -->
    <form method="POST" id="deleteForm" style="display: none;">
        <input type="hidden" name="action" value="delete">
        <input type="hidden" name="id" id="deleteId">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteHeader(id, pageName) {
            if (confirm(`Are you sure you want to delete the hero header for "${pageName}"?`)) {
                document.getElementById('deleteId').value = id;
                document.getElementById('deleteForm').submit();
            }
        }

        function updateOpacityValue(value) {
            document.getElementById('opacityValue').textContent = value;
        }

        function toggleBackgroundFields() {
            const type = document.getElementById('background_type').value;
            const imageField = document.getElementById('imageField');
            const gradientField = document.getElementById('gradientField');
            
            if (type === 'image') {
                imageField.style.display = 'block';
                gradientField.style.display = 'none';
            } else if (type === 'gradient') {
                imageField.style.display = 'none';
                gradientField.style.display = 'block';
            } else {
                imageField.style.display = 'none';
                gradientField.style.display = 'none';
            }
        }

        function toggleCustomHeight() {
            const type = document.getElementById('height_type').value;
            const customField = document.getElementById('customHeightField');
            customField.style.display = type === 'custom' ? 'block' : 'none';
        }

        function toggleCTAFields() {
            const showCTA = document.getElementById('show_cta_button').checked;
            const ctaFields = document.getElementById('ctaFields');
            ctaFields.style.display = showCTA ? 'block' : 'none';
        }

        // Initialize field visibility on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('background_type')) {
                toggleBackgroundFields();
                toggleCustomHeight();
                toggleCTAFields();
            }
        });
    </script>
</body>
</html>
