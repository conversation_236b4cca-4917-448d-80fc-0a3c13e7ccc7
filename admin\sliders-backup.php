<?php
/**
 * Slider Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';
require_once 'theme/init.php';

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_slider':
                try {
                    $db = Database::getConnection();

                    // Validate required fields
                    if (empty($_POST['title'])) {
                        $error = 'Title is required';
                        break;
                    }

                    // Handle background image upload
                    $background_image = '';
                    if (isset($_FILES['background_image']) && $_FILES['background_image']['error'] === UPLOAD_ERR_OK) {
                        $upload_result = uploadFile($_FILES['background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload_result['success']) {
                            $background_image = $upload_result['url'];
                        } else {
                            $error = 'Image upload failed: ' . $upload_result['message'];
                            break;
                        }
                    } elseif (isset($_FILES['background_image']) && $_FILES['background_image']['error'] !== UPLOAD_ERR_NO_FILE) {
                        $error = 'Image upload error: ' . $_FILES['background_image']['error'];
                        break;
                    } else {
                        $error = 'Background image is required';
                        break;
                    }

                    // Handle button link (custom URL or predefined page)
                    $button_link = $_POST['button_link'] ?? '#';
                    if ($button_link === 'custom' && !empty($_POST['custom_url'])) {
                        $button_link = $_POST['custom_url'];
                    }

                    // Handle custom height values
                    $height_desktop = $_POST['height_desktop'] ?? 500;
                    if ($height_desktop === 'custom') {
                        $height_desktop = $_POST['height_desktop_custom'] ?? 500;
                    }

                    $height_tablet = $_POST['height_tablet'] ?? 400;
                    if ($height_tablet === 'custom') {
                        $height_tablet = $_POST['height_tablet_custom'] ?? 400;
                    }

                    $height_mobile = $_POST['height_mobile'] ?? 300;
                    if ($height_mobile === 'custom') {
                        $height_mobile = $_POST['height_mobile_custom'] ?? 300;
                    }

                    $stmt = $db->prepare("
                        INSERT INTO sliders (title, subtitle, background_image, button_text, button_link,
                                           text_color, overlay_color, overlay_opacity, title_char_limit,
                                           description_char_limit, sort_order, active, animation_type,
                                           animation_duration, auto_play, pause_on_hover, show_navigation_dots,
                                           title_font_size, title_font_family, title_font_weight, title_text_transform,
                                           title_line_height, title_break_type, title_break_limit,
                                           description_break_type, description_break_limit, description_font_size,
                                           description_font_family, description_font_weight, description_text_transform,
                                           description_line_height, height_desktop, height_tablet, height_mobile,
                                           content_alignment, vertical_alignment, padding_top, padding_bottom)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");

                    $stmt->execute([
                        $_POST['title'],
                        $_POST['subtitle'] ?? '',
                        $background_image,
                        $_POST['button_text'] ?? 'Learn More',
                        $button_link,
                        $_POST['text_color'] ?? '#ffffff',
                        $_POST['overlay_color'] ?? '#000000',
                        $_POST['overlay_opacity'] ?? 0.5,
                        $_POST['title_char_limit'] ?? 50,
                        $_POST['description_char_limit'] ?? 100,
                        $_POST['sort_order'] ?? 0,
                        isset($_POST['active']) ? 1 : 0,
                        $_POST['animation_type'] ?? 'fade',
                        $_POST['animation_duration'] ?? 5.0,
                        isset($_POST['auto_play']) ? 1 : 0,
                        isset($_POST['pause_on_hover']) ? 1 : 0,
                        isset($_POST['show_navigation_dots']) ? 1 : 0,
                        $_POST['title_font_size'] ?? 3.5,
                        $_POST['title_font_family'] ?? 'System Default',
                        $_POST['title_font_weight'] ?? 700,
                        $_POST['title_text_transform'] ?? 'none',
                        $_POST['title_line_height'] ?? 1.2,
                        $_POST['title_break_type'] ?? 'none',
                        $_POST['title_break_limit'] ?? 50,
                        $_POST['description_break_type'] ?? 'none',
                        $_POST['description_break_limit'] ?? 100,
                        $_POST['description_font_size'] ?? 1.25,
                        $_POST['description_font_family'] ?? 'System Default',
                        $_POST['description_font_weight'] ?? 400,
                        $_POST['description_text_transform'] ?? 'none',
                        $_POST['description_line_height'] ?? 1.6,
                        $height_desktop,
                        $height_tablet,
                        $height_mobile,
                        $_POST['content_alignment'] ?? 'center',
                        $_POST['vertical_alignment'] ?? 'center',
                        $_POST['padding_top'] ?? 90,
                        $_POST['padding_bottom'] ?? 40
                    ]);

                    $message = 'Slider added successfully!';
                } catch (Exception $e) {
                    $error = 'Error adding slider: ' . $e->getMessage();
                }
                break;
                
            case 'update_slider':
                try {
                    $db = Database::getConnection();

                    // Validate required fields
                    if (empty($_POST['title'])) {
                        $error = 'Title is required';
                        break;
                    }

                    // Handle background image upload
                    $background_image = $_POST['current_background_image'] ?? '';
                    if (isset($_FILES['background_image']) && $_FILES['background_image']['error'] === UPLOAD_ERR_OK) {
                        $upload_result = uploadFile($_FILES['background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload_result['success']) {
                            $background_image = $upload_result['url'];
                        } else {
                            $error = 'Image upload failed: ' . $upload_result['message'];
                            break;
                        }
                    }

                    // Handle button link (custom URL or predefined page)
                    $button_link = $_POST['button_link'] ?? '#';
                    if ($button_link === 'custom' && !empty($_POST['custom_url'])) {
                        $button_link = $_POST['custom_url'];
                    }

                    // Handle custom height values
                    $height_desktop = $_POST['height_desktop'] ?? 500;
                    if ($height_desktop === 'custom') {
                        $height_desktop = $_POST['height_desktop_custom'] ?? 500;
                    }

                    $height_tablet = $_POST['height_tablet'] ?? 400;
                    if ($height_tablet === 'custom') {
                        $height_tablet = $_POST['height_tablet_custom'] ?? 400;
                    }

                    $height_mobile = $_POST['height_mobile'] ?? 300;
                    if ($height_mobile === 'custom') {
                        $height_mobile = $_POST['height_mobile_custom'] ?? 300;
                    }

                    $stmt = $db->prepare("
                        UPDATE sliders SET title = ?, subtitle = ?, background_image = ?, button_text = ?,
                                         button_link = ?, text_color = ?, overlay_color = ?, overlay_opacity = ?,
                                         title_char_limit = ?, description_char_limit = ?, sort_order = ?, active = ?,
                                         animation_type = ?, animation_duration = ?, auto_play = ?, pause_on_hover = ?,
                                         show_navigation_dots = ?, title_font_size = ?, title_font_family = ?, title_font_weight = ?,
                                         title_text_transform = ?, title_line_height = ?, title_break_type = ?, title_break_limit = ?,
                                         description_break_type = ?, description_break_limit = ?, description_font_size = ?,
                                         description_font_family = ?, description_font_weight = ?, description_text_transform = ?,
                                         description_line_height = ?, height_desktop = ?, height_tablet = ?, height_mobile = ?,
                                         content_alignment = ?, vertical_alignment = ?, padding_top = ?, padding_bottom = ?
                        WHERE id = ?
                    ");

                    $stmt->execute([
                        $_POST['title'],
                        $_POST['subtitle'] ?? '',
                        $background_image,
                        $_POST['button_text'] ?? 'Learn More',
                        $button_link,
                        $_POST['text_color'] ?? '#ffffff',
                        $_POST['overlay_color'] ?? '#000000',
                        $_POST['overlay_opacity'] ?? 0.5,
                        $_POST['title_char_limit'] ?? 50,
                        $_POST['description_char_limit'] ?? 100,
                        $_POST['sort_order'] ?? 0,
                        isset($_POST['active']) ? 1 : 0,
                        $_POST['animation_type'] ?? 'fade',
                        $_POST['animation_duration'] ?? 5.0,
                        isset($_POST['auto_play']) ? 1 : 0,
                        isset($_POST['pause_on_hover']) ? 1 : 0,
                        isset($_POST['show_navigation_dots']) ? 1 : 0,
                        $_POST['title_font_size'] ?? 3.5,
                        $_POST['title_font_family'] ?? 'System Default',
                        $_POST['title_font_weight'] ?? 700,
                        $_POST['title_text_transform'] ?? 'none',
                        $_POST['title_line_height'] ?? 1.2,
                        $_POST['title_break_type'] ?? 'none',
                        $_POST['title_break_limit'] ?? 50,
                        $_POST['description_break_type'] ?? 'none',
                        $_POST['description_break_limit'] ?? 100,
                        $_POST['description_font_size'] ?? 1.25,
                        $_POST['description_font_family'] ?? 'System Default',
                        $_POST['description_font_weight'] ?? 400,
                        $_POST['description_text_transform'] ?? 'none',
                        $_POST['description_line_height'] ?? 1.6,
                        $height_desktop,
                        $height_tablet,
                        $height_mobile,
                        $_POST['content_alignment'] ?? 'center',
                        $_POST['vertical_alignment'] ?? 'center',
                        $_POST['padding_top'] ?? 90,
                        $_POST['padding_bottom'] ?? 40,
                        $_POST['slider_id']
                    ]);

                    $message = 'Slider updated successfully!';
                } catch (Exception $e) {
                    $error = 'Error updating slider: ' . $e->getMessage();
                }
                break;
                
            case 'delete_slider':
                try {
                    $db = Database::getConnection();
                    $stmt = $db->prepare("DELETE FROM sliders WHERE id = ?");
                    $stmt->execute([$_POST['slider_id']]);
                    $message = 'Slider deleted successfully!';
                } catch (Exception $e) {
                    $error = 'Error deleting slider: ' . $e->getMessage();
                }
                break;
                
            case 'update_universal_height':
                try {
                    $use_universal = isset($_POST['use_universal_height']) ? '1' : '0';
                    $height_desktop = $_POST['universal_height_desktop'] ?? '600';
                    $height_tablet = $_POST['universal_height_tablet'] ?? '450';
                    $height_mobile = $_POST['universal_height_mobile'] ?? '350';
                    
                    updateThemeOption('use_universal_slider_height', $use_universal);
                    updateThemeOption('universal_slider_height_desktop', $height_desktop);
                    updateThemeOption('universal_slider_height_tablet', $height_tablet);
                    updateThemeOption('universal_slider_height_mobile', $height_mobile);
                    
                    $message = 'Universal height settings updated successfully!';
                } catch (Exception $e) {
                    $error = 'Error updating universal height settings: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get all sliders
try {
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM sliders ORDER BY sort_order ASC, id ASC");
    $sliders = $stmt->fetchAll();
} catch (Exception $e) {
    $sliders = [];
    $error = 'Error fetching sliders: ' . $e->getMessage();
}

// Get available pages for dropdown
function getAvailablePages() {
    $pages = [
        '' => 'Select a page...',
        'index' => 'Home',
        'about' => 'About',
        'services' => 'Services',
        'projects' => 'Projects',
        'team' => 'Team',
        'news' => 'News',
        'contact' => 'Contact'
    ];

    // Check for additional pages
    $additional_pages = [];
    $files = glob('*.php');
    foreach ($files as $file) {
        $page_name = basename($file, '.php');
        if (!in_array($page_name, ['index', 'about', 'services', 'projects', 'team', 'news', 'contact'])
            && !in_array($page_name, ['config', 'database', 'update-database'])
            && !str_starts_with($page_name, 'admin')
            && !str_starts_with($page_name, 'test_')) {
            $additional_pages[$page_name] = ucfirst(str_replace(['-', '_'], ' ', $page_name));
        }
    }

    if (!empty($additional_pages)) {
        $pages = array_merge($pages, $additional_pages);
    }

    $pages['custom'] = 'Custom URL...';

    return $pages;
}

$available_pages = getAvailablePages();

// Get font families
function getFontFamilies() {
    try {
        $db = Database::getConnection();
        $stmt = $db->query("SELECT * FROM font_families ORDER BY name ASC");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [
            ['name' => 'System Default', 'css_name' => 'system-ui, -apple-system, sans-serif'],
            ['name' => 'Arial', 'css_name' => 'Arial, sans-serif'],
            ['name' => 'Helvetica', 'css_name' => 'Helvetica, sans-serif'],
            ['name' => 'Georgia', 'css_name' => 'Georgia, serif'],
            ['name' => 'Times New Roman', 'css_name' => 'Times New Roman, serif']
        ];
    }
}

// Get animation presets
function getAnimationPresets() {
    try {
        $db = Database::getConnection();
        $stmt = $db->query("SELECT * FROM animation_presets ORDER BY name ASC");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [
            ['value' => 'fade', 'name' => 'Fade In/Out'],
            ['value' => 'slide', 'name' => 'Slide Left/Right'],
            ['value' => 'zoom', 'name' => 'Zoom In/Out'],
            ['value' => 'none', 'name' => 'No Animation']
        ];
    }
}

$font_families = getFontFamilies();
$animation_presets = getAnimationPresets();

// Prepare management page data
$management_actions = [
    [
        'url' => '#',
        'label' => 'Add New Slider',
        'type' => 'primary',
        'icon' => 'fas fa-plus',
        'onclick' => 'openModal(\'addSliderModal\')'
    ]
];

// Render the management page using the theme system
renderAdminPage('management', [
    'page_title' => 'Slider Management',
    'page_icon' => 'fas fa-images',
    'management_title' => 'Slider Management',
    'management_description' => 'Create and manage sliders for your website homepage and other sections.',
    'management_actions' => $management_actions,
    'show_search' => true,
    'search_placeholder' => 'Search sliders...',
    'show_table' => true,
    'table_title' => 'All Sliders',
    'table_content_file' => __DIR__ . '/theme/content/sliders-table.php',
    'message' => $message,
    'error' => $error,
    'sliders' => $sliders,
    'font_families' => $font_families,
    'animation_presets' => $animation_presets
]);
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slider Management - Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .admin-header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-nav {
            background: #34495e;
            padding: 0;
            display: flex;
            overflow-x: auto;
        }
        
        .admin-nav a {
            color: #bdc3c7;
            text-decoration: none;
            padding: 1rem 1.5rem;
            white-space: nowrap;
            transition: all 0.3s;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #E67E22;
            color: white;
        }
        
        .admin-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #f5c6cb;
        }
        
        .admin-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-card-body {
            padding: 1.5rem;
        }
        
        .btn {
            background: #E67E22;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #d35400;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .slider-item {
            border: 1px solid #eee;
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }
        
        .slider-header {
            background: #f8f9fa;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .slider-preview {
            padding: 1rem;
            display: grid;
            grid-template-columns: 200px 1fr auto;
            gap: 1rem;
            align-items: center;
        }
        
        .slider-image {
            width: 200px;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .slider-info h4 {
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }
        
        .slider-info p {
            color: #7f8c8d;
            margin-bottom: 0.25rem;
        }
        
        .slider-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .color-input {
            width: 60px !important;
            height: 40px;
            padding: 0;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .range-input {
            width: 100%;
        }

        .toggle-form {
            display: none;
            padding: 1rem;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }

        .toggle-form.active {
            display: block;
        }

        /* Advanced Controls Styling */
        .control-section {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .control-section-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #e0e0e0;
            font-weight: 600;
            color: #2c3e50;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-section-header:hover {
            background: #e9ecef;
        }

        .control-section-body {
            padding: 1.5rem;
            display: none !important;
        }

        .control-section-body.active {
            display: block !important;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .form-grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
        }

        .form-group-full {
            grid-column: 1 / -1;
        }

        .range-display {
            display: inline-block;
            margin-left: 0.5rem;
            font-weight: 600;
            color: #E67E22;
        }

        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }

        .reset-btn {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.75rem;
            cursor: pointer;
            margin-left: 0.5rem;
        }

        .reset-btn:hover {
            background: #7f8c8d;
        }
        
        @media (max-width: 768px) {
            .admin-content {
                padding: 1rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .slider-preview {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .slider-actions {
                justify-content: center;
            }
        }

        /* Number input styling for padding controls */
        .number-input {
            width: 120px;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-right: 0.5rem;
        }

        .input-suffix {
            font-weight: bold;
            color: #333;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>Slider Management</h1>
        <div>
            <a href="<?php echo siteUrl(); ?>" target="_blank" style="color: #E67E22; text-decoration: none;">View Website</a>
            <span style="margin: 0 1rem;">|</span>
            <a href="logout.php" style="color: #e74c3c; text-decoration: none;">Logout</a>
        </div>
    </div>
    
    <nav class="admin-nav">
        <a href="index.php">Theme Options</a>
        <a href="sliders.php" class="active">Sliders</a>
        <a href="hero-sections.php">Hero Sections</a>
        <a href="services.php">Services</a>
        <a href="projects.php">Projects</a>
        <a href="team.php">Team</a>
        <a href="testimonials.php">Testimonials</a>
        <a href="blog.php">Blog</a>
        <a href="contacts.php">Contact Submissions</a>
    </nav>
    
    <div class="admin-content">
        <?php if ($message): ?>
            <div class="message"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Universal Height Controls -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h2>Universal Height Settings</h2>
                <small>Control the height of all sliders at once</small>
            </div>
            
            <div class="admin-card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_universal_height">
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="use_universal_height" <?php echo getThemeOption('use_universal_slider_height', '1') === '1' ? 'checked' : ''; ?>>
                            Use universal height for all sliders
                        </label>
                        <small>When enabled, all sliders will use the same height regardless of individual settings</small>
                    </div>
                    
                    <div class="form-grid-3">
                        <div class="form-group">
                            <label for="universal_height_desktop">Desktop Height (px)</label>
                            <input type="number" id="universal_height_desktop" name="universal_height_desktop" 
                                   value="<?php echo getThemeOption('universal_slider_height_desktop', '600'); ?>" 
                                   min="300" max="1200">
                        </div>
                        <div class="form-group">
                            <label for="universal_height_tablet">Tablet Height (px)</label>
                            <input type="number" id="universal_height_tablet" name="universal_height_tablet" 
                                   value="<?php echo getThemeOption('universal_slider_height_tablet', '450'); ?>" 
                                   min="300" max="800">
                        </div>
                        <div class="form-group">
                            <label for="universal_height_mobile">Mobile Height (px)</label>
                            <input type="number" id="universal_height_mobile" name="universal_height_mobile" 
                                   value="<?php echo getThemeOption('universal_slider_height_mobile', '350'); ?>" 
                                   min="250" max="600">
                        </div>
                    </div>
                    
                    <button type="submit" class="btn">Update Universal Height</button>
                </form>
            </div>
        </div>

        <!-- Add New Slider -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h2>Add New Slider</h2>
                <button type="button" class="btn" onclick="toggleForm('add-slider-form')">Add Slider</button>
            </div>
            
            <div id="add-slider-form" class="toggle-form">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="add_slider">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="title">Slide Title</label>
                            <input type="text" id="title" name="title" required>
                        </div>
                        <div class="form-group">
                            <label for="sort_order">Sort Order</label>
                            <input type="number" id="sort_order" name="sort_order" value="0">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="subtitle">Slide Description</label>
                        <textarea id="subtitle" name="subtitle" placeholder="Enter slide description/subtitle"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="background_image">Background Image</label>
                        <input type="file" id="background_image" name="background_image" accept="image/*" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="button_text">Button Text</label>
                            <input type="text" id="button_text" name="button_text" value="Learn More">
                        </div>
                        <div class="form-group">
                            <label for="button_link">Button Link</label>
                            <select id="button_link" name="button_link" onchange="toggleCustomUrl(this)">
                                <?php foreach ($available_pages as $value => $label): ?>
                                    <option value="<?php echo htmlspecialchars($value); ?>"><?php echo htmlspecialchars($label); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <input type="text" id="custom_url" name="custom_url" placeholder="Enter custom URL..." style="display: none; margin-top: 0.5rem;">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="title_char_limit">Title Character Limit</label>
                            <input type="number" id="title_char_limit" name="title_char_limit" value="50" min="20" max="100">
                            <small>Characters before line break</small>
                        </div>
                        <div class="form-group">
                            <label for="description_char_limit">Description Character Limit</label>
                            <input type="number" id="description_char_limit" name="description_char_limit" value="100" min="50" max="200">
                            <small>Characters per line before break</small>
                        </div>
                    </div>
                    
                    <!-- Animation Controls -->
                    <div class="control-section">
                        <div class="control-section-header" onclick="toggleSection(this)">
                            <span>🎬 Animation Controls</span>
                            <span>▼</span>
                        </div>
                        <div class="control-section-body">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="animation_type" class="tooltip" data-tooltip="Choose how slides transition">Animation Type</label>
                                    <select id="animation_type" name="animation_type">
                                        <?php foreach ($animation_presets as $preset): ?>
                                            <option value="<?php echo htmlspecialchars($preset['value']); ?>"><?php echo htmlspecialchars($preset['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="animation_duration" class="tooltip" data-tooltip="How long each slide displays (3-10 seconds)">Duration (seconds)</label>
                                    <input type="range" id="animation_duration" name="animation_duration" min="3" max="10" step="0.5" value="5" class="range-input">
                                    <span class="range-display">5s</span>
                                    <button type="button" class="reset-btn" onclick="resetField('animation_duration', 5)">Reset</button>
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" name="auto_play" checked> Auto-play slides
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" name="pause_on_hover" checked> Pause on hover
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" name="show_navigation_dots" checked> Show navigation dots
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Typography Controls -->
                    <div class="control-section">
                        <div class="control-section-header" onclick="toggleSection(this)">
                            <span>🔤 Typography Controls</span>
                            <span>▼</span>
                        </div>
                        <div class="control-section-body">
                            <h4>Title Typography</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="title_font_size" class="tooltip" data-tooltip="Font size for slide titles (1-5rem)">Font Size</label>
                                    <input type="range" id="title_font_size" name="title_font_size" min="1" max="5" step="0.1" value="3.5" class="range-input">
                                    <span class="range-display">3.5rem</span>
                                    <button type="button" class="reset-btn" onclick="resetField('title_font_size', 3.5)">Reset</button>
                                </div>
                                <div class="form-group">
                                    <label for="title_font_family">Font Family</label>
                                    <select id="title_font_family" name="title_font_family">
                                        <?php foreach ($font_families as $font): ?>
                                            <option value="<?php echo htmlspecialchars($font['name']); ?>"><?php echo htmlspecialchars($font['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="title_font_weight">Font Weight</label>
                                    <select id="title_font_weight" name="title_font_weight">
                                        <option value="100">100 - Thin</option>
                                        <option value="300">300 - Light</option>
                                        <option value="400">400 - Normal</option>
                                        <option value="500">500 - Medium</option>
                                        <option value="600">600 - Semi Bold</option>
                                        <option value="700" selected>700 - Bold</option>
                                        <option value="900">900 - Black</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="title_text_transform">Text Transform</label>
                                    <select id="title_text_transform" name="title_text_transform">
                                        <option value="none">None</option>
                                        <option value="uppercase">UPPERCASE</option>
                                        <option value="lowercase">lowercase</option>
                                        <option value="capitalize">Capitalize</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="title_line_height" class="tooltip" data-tooltip="Line spacing for titles">Line Height</label>
                                    <input type="range" id="title_line_height" name="title_line_height" min="0.8" max="2" step="0.1" value="1.2" class="range-input">
                                    <span class="range-display">1.2</span>
                                    <button type="button" class="reset-btn" onclick="resetField('title_line_height', 1.2)">Reset</button>
                                </div>
                                <div class="form-group">
                                    <label for="title_break_type" class="tooltip" data-tooltip="How to break long titles into lines">Line Breaking</label>
                                    <select id="title_break_type" name="title_break_type" onchange="toggleBreakLimitField('title')">
                                        <option value="none">No Breaking</option>
                                        <option value="character">Character Based</option>
                                        <option value="word">Word Based</option>
                                        <option value="manual">Manual (use | for line breaks)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group" id="title_break_limit_group">
                                <label for="title_break_limit" class="tooltip" data-tooltip="Characters or words per line before breaking (not used for manual breaks)">Break Limit</label>
                                <input type="number" id="title_break_limit" name="title_break_limit" value="50" min="10" max="100">
                                <small style="color: #666; font-size: 0.85rem; margin-top: 0.25rem; display: block;">
                                    For manual breaks, use | (pipe) character in your title where you want line breaks
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Description Typography -->
                    <div class="control-section">
                        <div class="control-section-header" onclick="toggleSection(this)">
                            <span>📝 Description Typography</span>
                            <span>▼</span>
                        </div>
                        <div class="control-section-body">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="description_font_size" class="tooltip" data-tooltip="Font size for descriptions (0.8-2rem)">Font Size</label>
                                    <input type="range" id="description_font_size" name="description_font_size" min="0.8" max="2" step="0.1" value="1.25" class="range-input">
                                    <span class="range-display">1.25rem</span>
                                    <button type="button" class="reset-btn" onclick="resetField('description_font_size', 1.25)">Reset</button>
                                </div>
                                <div class="form-group">
                                    <label for="description_font_family">Font Family</label>
                                    <select id="description_font_family" name="description_font_family">
                                        <?php foreach ($font_families as $font): ?>
                                            <option value="<?php echo htmlspecialchars($font['name']); ?>"><?php echo htmlspecialchars($font['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="description_font_weight">Font Weight</label>
                                    <select id="description_font_weight" name="description_font_weight">
                                        <option value="100">100 - Thin</option>
                                        <option value="300">300 - Light</option>
                                        <option value="400" selected>400 - Normal</option>
                                        <option value="500">500 - Medium</option>
                                        <option value="600">600 - Semi Bold</option>
                                        <option value="700">700 - Bold</option>
                                        <option value="900">900 - Black</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="description_text_transform">Text Transform</label>
                                    <select id="description_text_transform" name="description_text_transform">
                                        <option value="none">None</option>
                                        <option value="uppercase">UPPERCASE</option>
                                        <option value="lowercase">lowercase</option>
                                        <option value="capitalize">Capitalize</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="description_line_height" class="tooltip" data-tooltip="Line spacing for descriptions">Line Height</label>
                                    <input type="range" id="description_line_height" name="description_line_height" min="1" max="2.5" step="0.1" value="1.6" class="range-input">
                                    <span class="range-display">1.6</span>
                                    <button type="button" class="reset-btn" onclick="resetField('description_line_height', 1.6)">Reset</button>
                                </div>
                                <div class="form-group">
                                    <label for="description_break_type" class="tooltip" data-tooltip="How to break long descriptions into lines">Line Breaking</label>
                                    <select id="description_break_type" name="description_break_type" onchange="toggleBreakLimitField('description')">
                                        <option value="none">No Breaking</option>
                                        <option value="character">Character Based</option>
                                        <option value="word">Word Based</option>
                                        <option value="manual">Manual (use | for line breaks)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group" id="description_break_limit_group">
                                <label for="description_break_limit" class="tooltip" data-tooltip="Characters or words per line before breaking (not used for manual breaks)">Break Limit</label>
                                <input type="number" id="description_break_limit" name="description_break_limit" value="100" min="50" max="200">
                                <small style="color: #666; font-size: 0.85rem; margin-top: 0.25rem; display: block;">
                                    For manual breaks, use | (pipe) character in your description where you want line breaks
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Layout & Height Controls -->
                    <div class="control-section">
                        <div class="control-section-header" onclick="toggleSection(this)">
                            <span>📐 Layout & Height Controls</span>
                            <span>▼</span>
                        </div>
                        <div class="control-section-body">
                            <div class="form-grid-3">
                                <div class="form-group">
                                    <label for="height_desktop" class="tooltip" data-tooltip="Slider height on desktop (200-1200px)">Desktop Height</label>
                                    <select id="height_desktop" name="height_desktop" onchange="updateHeightInput(this, 'height_desktop_custom')">
                                        <option value="300">300px</option>
                                        <option value="400">400px</option>
                                        <option value="500" selected>500px</option>
                                        <option value="600">600px</option>
                                        <option value="700">700px</option>
                                        <option value="800">800px</option>
                                        <option value="custom">Custom</option>
                                    </select>
                                    <input type="number" id="height_desktop_custom" min="200" max="1200" style="display: none; margin-top: 0.5rem;" placeholder="Custom height">
                                </div>
                                <div class="form-group">
                                    <label for="height_tablet" class="tooltip" data-tooltip="Slider height on tablet">Tablet Height</label>
                                    <select id="height_tablet" name="height_tablet" onchange="updateHeightInput(this, 'height_tablet_custom')">
                                        <option value="300">300px</option>
                                        <option value="400" selected>400px</option>
                                        <option value="500">500px</option>
                                        <option value="600">600px</option>
                                        <option value="custom">Custom</option>
                                    </select>
                                    <input type="number" id="height_tablet_custom" min="200" max="800" style="display: none; margin-top: 0.5rem;" placeholder="Custom height">
                                </div>
                                <div class="form-group">
                                    <label for="height_mobile" class="tooltip" data-tooltip="Slider height on mobile">Mobile Height</label>
                                    <select id="height_mobile" name="height_mobile" onchange="updateHeightInput(this, 'height_mobile_custom')">
                                        <option value="250">250px</option>
                                        <option value="300" selected>300px</option>
                                        <option value="350">350px</option>
                                        <option value="400">400px</option>
                                        <option value="custom">Custom</option>
                                    </select>
                                    <input type="number" id="height_mobile_custom" min="200" max="600" style="display: none; margin-top: 0.5rem;" placeholder="Custom height">
                                </div>
                            </div>

                            <h4>Padding Controls</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="padding_top" class="tooltip" data-tooltip="Space from top of slider to title (0px and up)">Top Padding</label>
                                    <input type="number" id="padding_top" name="padding_top" min="0" step="5" value="90" class="number-input" placeholder="Enter padding in pixels">
                                    <span class="input-suffix">px</span>
                                    <button type="button" class="reset-btn" onclick="resetField('padding_top', 90)">Reset</button>
                                </div>
                                <div class="form-group">
                                    <label for="padding_bottom" class="tooltip" data-tooltip="Space from bottom content to bottom of slider (0px and up)">Bottom Padding</label>
                                    <input type="number" id="padding_bottom" name="padding_bottom" min="0" step="5" value="40" class="number-input" placeholder="Enter padding in pixels">
                                    <span class="input-suffix">px</span>
                                    <button type="button" class="reset-btn" onclick="resetField('padding_bottom', 40)">Reset</button>
                                </div>
                            </div>

                            <h4>Content Alignment</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="content_alignment" class="tooltip" data-tooltip="Horizontal alignment of content">Content Alignment</label>
                                    <select id="content_alignment" name="content_alignment">
                                        <option value="left">Left</option>
                                        <option value="center" selected>Center</option>
                                        <option value="right">Right</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="vertical_alignment" class="tooltip" data-tooltip="Vertical alignment of content">Vertical Alignment</label>
                                    <select id="vertical_alignment" name="vertical_alignment">
                                        <option value="top">Top</option>
                                        <option value="center" selected>Center</option>
                                        <option value="bottom">Bottom</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Basic Settings -->
                    <div class="control-section">
                        <div class="control-section-header" onclick="toggleSection(this)">
                            <span>⚙️ Basic Settings</span>
                            <span>▼</span>
                        </div>
                        <div class="control-section-body active">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="text_color">Text Color</label>
                                    <input type="color" id="text_color" name="text_color" value="#ffffff" class="color-input">
                                </div>
                                <div class="form-group">
                                    <label for="overlay_color">Overlay Color</label>
                                    <input type="color" id="overlay_color" name="overlay_color" value="#000000" class="color-input">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="overlay_opacity">Overlay Opacity</label>
                                <input type="range" id="overlay_opacity" name="overlay_opacity" min="0" max="1" step="0.1" value="0.5" class="range-input">
                                <span class="range-display">0.5</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="active" checked> Active
                                </label>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn">Add Slider</button>
                </form>
            </div>
        </div>

        <!-- Existing Sliders -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h2>Manage Sliders (<?php echo count($sliders); ?>)</h2>
            </div>

            <div class="admin-card-body">
                <?php if (empty($sliders)): ?>
                    <p>No sliders found. Add your first slider above.</p>
                <?php else: ?>
                    <?php foreach ($sliders as $slider): ?>
                        <div class="slider-item">
                            <div class="slider-preview">
                                <img src="<?php echo ensureAbsoluteUrl($slider['background_image']); ?>"
                                     alt="<?php echo htmlspecialchars($slider['title']); ?>"
                                     class="slider-image">

                                <div class="slider-info">
                                    <h4><?php echo htmlspecialchars($slider['title']); ?></h4>
                                    <p><strong>Description:</strong> <?php echo htmlspecialchars(substr($slider['subtitle'], 0, 100)) . (strlen($slider['subtitle']) > 100 ? '...' : ''); ?></p>
                                    <p><strong>Button:</strong> <?php echo htmlspecialchars($slider['button_text']); ?> → <?php echo htmlspecialchars($slider['button_link']); ?></p>
                                    <p><strong>Order:</strong> <?php echo $slider['sort_order']; ?> | <strong>Status:</strong> <?php echo $slider['active'] ? 'Active' : 'Inactive'; ?></p>
                                </div>

                                <div class="slider-actions">
                                    <button type="button" class="btn" onclick="toggleForm('edit-slider-<?php echo $slider['id']; ?>')">Edit</button>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this slider?')">
                                        <input type="hidden" name="action" value="delete_slider">
                                        <input type="hidden" name="slider_id" value="<?php echo $slider['id']; ?>">
                                        <button type="submit" class="btn btn-danger">Delete</button>
                                    </form>
                                </div>
                            </div>

                            <!-- Edit Form -->
                            <div id="edit-slider-<?php echo $slider['id']; ?>" class="toggle-form">
                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="action" value="update_slider">
                                    <input type="hidden" name="slider_id" value="<?php echo $slider['id']; ?>">
                                    <input type="hidden" name="current_background_image" value="<?php echo htmlspecialchars($slider['background_image']); ?>">

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="edit_title_<?php echo $slider['id']; ?>">Slide Title</label>
                                            <input type="text" id="edit_title_<?php echo $slider['id']; ?>" name="title" value="<?php echo htmlspecialchars($slider['title']); ?>" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="edit_sort_order_<?php echo $slider['id']; ?>">Sort Order</label>
                                            <input type="number" id="edit_sort_order_<?php echo $slider['id']; ?>" name="sort_order" value="<?php echo $slider['sort_order']; ?>">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="edit_subtitle_<?php echo $slider['id']; ?>">Slide Description</label>
                                        <textarea id="edit_subtitle_<?php echo $slider['id']; ?>" name="subtitle"><?php echo htmlspecialchars($slider['subtitle']); ?></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label for="edit_background_image_<?php echo $slider['id']; ?>">Background Image (leave empty to keep current)</label>
                                        <input type="file" id="edit_background_image_<?php echo $slider['id']; ?>" name="background_image" accept="image/*">
                                        <small>Current: <?php echo basename($slider['background_image']); ?></small>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="edit_button_text_<?php echo $slider['id']; ?>">Button Text</label>
                                            <input type="text" id="edit_button_text_<?php echo $slider['id']; ?>" name="button_text" value="<?php echo htmlspecialchars($slider['button_text']); ?>">
                                        </div>
                                        <div class="form-group">
                                            <label for="edit_button_link_<?php echo $slider['id']; ?>">Button Link</label>
                                            <select id="edit_button_link_<?php echo $slider['id']; ?>" name="button_link" onchange="toggleCustomUrl(this)">
                                                <?php
                                                $current_link = $slider['button_link'];
                                                $is_custom = !array_key_exists($current_link, $available_pages);
                                                foreach ($available_pages as $value => $label):
                                                    $selected = ($value === $current_link) ? 'selected' : '';
                                                    if ($is_custom && $value === 'custom') {
                                                        $selected = 'selected';
                                                    }
                                                ?>
                                                    <option value="<?php echo htmlspecialchars($value); ?>" <?php echo $selected; ?>><?php echo htmlspecialchars($label); ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                            <input type="text" name="custom_url" placeholder="Enter custom URL..."
                                                   value="<?php echo $is_custom ? htmlspecialchars($current_link) : ''; ?>"
                                                   style="<?php echo $is_custom ? 'display: block;' : 'display: none;'; ?> margin-top: 0.5rem;">
                                        </div>
                                    </div>

                                    <!-- Animation Controls -->
                                    <div class="control-section">
                                        <div class="control-section-header" onclick="toggleSection(this)">
                                            <span>🎬 Animation Controls</span>
                                            <span>▼</span>
                                        </div>
                                        <div class="control-section-body">
                                            <div class="form-grid">
                                                <div class="form-group">
                                                    <label for="edit_animation_type_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Choose how slides transition">Animation Type</label>
                                                    <select id="edit_animation_type_<?php echo $slider['id']; ?>" name="animation_type">
                                                        <?php foreach ($animation_presets as $preset): ?>
                                                            <option value="<?php echo htmlspecialchars($preset['value']); ?>" <?php echo ($slider['animation_type'] ?? 'fade') === $preset['value'] ? 'selected' : ''; ?>><?php echo htmlspecialchars($preset['name']); ?></option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_animation_duration_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="How long each slide displays (3-10 seconds)">Duration (seconds)</label>
                                                    <input type="range" id="edit_animation_duration_<?php echo $slider['id']; ?>" name="animation_duration" min="3" max="10" step="0.5" value="<?php echo $slider['animation_duration'] ?? 5; ?>" class="range-input">
                                                    <span class="range-display"><?php echo $slider['animation_duration'] ?? 5; ?>s</span>
                                                    <button type="button" class="reset-btn" onclick="resetField('edit_animation_duration_<?php echo $slider['id']; ?>', 5)">Reset</button>
                                                </div>
                                            </div>
                                            <div class="form-grid">
                                                <div class="form-group">
                                                    <label>
                                                        <input type="checkbox" name="auto_play" <?php echo ($slider['auto_play'] ?? 1) ? 'checked' : ''; ?>> Auto-play slides
                                                    </label>
                                                </div>
                                                <div class="form-group">
                                                    <label>
                                                        <input type="checkbox" name="pause_on_hover" <?php echo ($slider['pause_on_hover'] ?? 1) ? 'checked' : ''; ?>> Pause on hover
                                                    </label>
                                                </div>
                                                <div class="form-group">
                                                    <label>
                                                        <input type="checkbox" name="show_navigation_dots" <?php echo ($slider['show_navigation_dots'] ?? 1) ? 'checked' : ''; ?>> Show navigation dots
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Typography Controls -->
                                    <div class="control-section">
                                        <div class="control-section-header" onclick="toggleSection(this)">
                                            <span>🔤 Typography Controls</span>
                                            <span>▼</span>
                                        </div>
                                        <div class="control-section-body">
                                            <h4>Title Typography</h4>
                                            <div class="form-grid">
                                                <div class="form-group">
                                                    <label for="edit_title_font_size_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Font size for slide titles (1-5rem)">Font Size</label>
                                                    <input type="range" id="edit_title_font_size_<?php echo $slider['id']; ?>" name="title_font_size" min="1" max="5" step="0.1" value="<?php echo $slider['title_font_size'] ?? 3.5; ?>" class="range-input">
                                                    <span class="range-display"><?php echo $slider['title_font_size'] ?? 3.5; ?>rem</span>
                                                    <button type="button" class="reset-btn" onclick="resetField('edit_title_font_size_<?php echo $slider['id']; ?>', 3.5)">Reset</button>
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_title_font_family_<?php echo $slider['id']; ?>">Font Family</label>
                                                    <select id="edit_title_font_family_<?php echo $slider['id']; ?>" name="title_font_family">
                                                        <?php foreach ($font_families as $font): ?>
                                                            <option value="<?php echo htmlspecialchars($font['name']); ?>" <?php echo ($slider['title_font_family'] ?? 'System Default') === $font['name'] ? 'selected' : ''; ?>><?php echo htmlspecialchars($font['name']); ?></option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-grid">
                                                <div class="form-group">
                                                    <label for="edit_title_font_weight_<?php echo $slider['id']; ?>">Font Weight</label>
                                                    <select id="edit_title_font_weight_<?php echo $slider['id']; ?>" name="title_font_weight">
                                                        <option value="100" <?php echo ($slider['title_font_weight'] ?? 700) == 100 ? 'selected' : ''; ?>>100 - Thin</option>
                                                        <option value="300" <?php echo ($slider['title_font_weight'] ?? 700) == 300 ? 'selected' : ''; ?>>300 - Light</option>
                                                        <option value="400" <?php echo ($slider['title_font_weight'] ?? 700) == 400 ? 'selected' : ''; ?>>400 - Normal</option>
                                                        <option value="500" <?php echo ($slider['title_font_weight'] ?? 700) == 500 ? 'selected' : ''; ?>>500 - Medium</option>
                                                        <option value="600" <?php echo ($slider['title_font_weight'] ?? 700) == 600 ? 'selected' : ''; ?>>600 - Semi Bold</option>
                                                        <option value="700" <?php echo ($slider['title_font_weight'] ?? 700) == 700 ? 'selected' : ''; ?>>700 - Bold</option>
                                                        <option value="900" <?php echo ($slider['title_font_weight'] ?? 700) == 900 ? 'selected' : ''; ?>>900 - Black</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_title_text_transform_<?php echo $slider['id']; ?>">Text Transform</label>
                                                    <select id="edit_title_text_transform_<?php echo $slider['id']; ?>" name="title_text_transform">
                                                        <option value="none" <?php echo ($slider['title_text_transform'] ?? 'none') === 'none' ? 'selected' : ''; ?>>None</option>
                                                        <option value="uppercase" <?php echo ($slider['title_text_transform'] ?? 'none') === 'uppercase' ? 'selected' : ''; ?>>UPPERCASE</option>
                                                        <option value="lowercase" <?php echo ($slider['title_text_transform'] ?? 'none') === 'lowercase' ? 'selected' : ''; ?>>lowercase</option>
                                                        <option value="capitalize" <?php echo ($slider['title_text_transform'] ?? 'none') === 'capitalize' ? 'selected' : ''; ?>>Capitalize</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-grid">
                                                <div class="form-group">
                                                    <label for="edit_title_line_height_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Line spacing for titles">Line Height</label>
                                                    <input type="range" id="edit_title_line_height_<?php echo $slider['id']; ?>" name="title_line_height" min="0.8" max="2" step="0.1" value="<?php echo $slider['title_line_height'] ?? 1.2; ?>" class="range-input">
                                                    <span class="range-display"><?php echo $slider['title_line_height'] ?? 1.2; ?></span>
                                                    <button type="button" class="reset-btn" onclick="resetField('edit_title_line_height_<?php echo $slider['id']; ?>', 1.2)">Reset</button>
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_title_break_type_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="How to break long titles into lines">Line Breaking</label>
                                                    <select id="edit_title_break_type_<?php echo $slider['id']; ?>" name="title_break_type" onchange="toggleBreakLimitField('edit_title', '<?php echo $slider['id']; ?>')">
                                                        <option value="none" <?php echo ($slider['title_break_type'] ?? 'none') === 'none' ? 'selected' : ''; ?>>No Breaking</option>
                                                        <option value="character" <?php echo ($slider['title_break_type'] ?? 'none') === 'character' ? 'selected' : ''; ?>>Character Based</option>
                                                        <option value="word" <?php echo ($slider['title_break_type'] ?? 'none') === 'word' ? 'selected' : ''; ?>>Word Based</option>
                                                        <option value="manual" <?php echo ($slider['title_break_type'] ?? 'none') === 'manual' ? 'selected' : ''; ?>>Manual (use | for line breaks)</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label for="edit_title_break_limit_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Characters or words per line before breaking">Break Limit</label>
                                                <input type="number" id="edit_title_break_limit_<?php echo $slider['id']; ?>" name="title_break_limit" value="<?php echo $slider['title_break_limit'] ?? 50; ?>" min="10" max="100">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Description Typography -->
                                    <div class="control-section">
                                        <div class="control-section-header" onclick="toggleSection(this)">
                                            <span>📝 Description Typography</span>
                                            <span>▼</span>
                                        </div>
                                        <div class="control-section-body">
                                            <div class="form-grid">
                                                <div class="form-group">
                                                    <label for="edit_description_font_size_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Font size for descriptions (0.8-2rem)">Font Size</label>
                                                    <input type="range" id="edit_description_font_size_<?php echo $slider['id']; ?>" name="description_font_size" min="0.8" max="2" step="0.1" value="<?php echo $slider['description_font_size'] ?? 1.25; ?>" class="range-input">
                                                    <span class="range-display"><?php echo $slider['description_font_size'] ?? 1.25; ?>rem</span>
                                                    <button type="button" class="reset-btn" onclick="resetField('edit_description_font_size_<?php echo $slider['id']; ?>', 1.25)">Reset</button>
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_description_font_family_<?php echo $slider['id']; ?>">Font Family</label>
                                                    <select id="edit_description_font_family_<?php echo $slider['id']; ?>" name="description_font_family">
                                                        <?php foreach ($font_families as $font): ?>
                                                            <option value="<?php echo htmlspecialchars($font['name']); ?>" <?php echo ($slider['description_font_family'] ?? 'System Default') === $font['name'] ? 'selected' : ''; ?>><?php echo htmlspecialchars($font['name']); ?></option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-grid">
                                                <div class="form-group">
                                                    <label for="edit_description_font_weight_<?php echo $slider['id']; ?>">Font Weight</label>
                                                    <select id="edit_description_font_weight_<?php echo $slider['id']; ?>" name="description_font_weight">
                                                        <option value="100" <?php echo ($slider['description_font_weight'] ?? 400) == 100 ? 'selected' : ''; ?>>100 - Thin</option>
                                                        <option value="300" <?php echo ($slider['description_font_weight'] ?? 400) == 300 ? 'selected' : ''; ?>>300 - Light</option>
                                                        <option value="400" <?php echo ($slider['description_font_weight'] ?? 400) == 400 ? 'selected' : ''; ?>>400 - Normal</option>
                                                        <option value="500" <?php echo ($slider['description_font_weight'] ?? 400) == 500 ? 'selected' : ''; ?>>500 - Medium</option>
                                                        <option value="600" <?php echo ($slider['description_font_weight'] ?? 400) == 600 ? 'selected' : ''; ?>>600 - Semi Bold</option>
                                                        <option value="700" <?php echo ($slider['description_font_weight'] ?? 400) == 700 ? 'selected' : ''; ?>>700 - Bold</option>
                                                        <option value="900" <?php echo ($slider['description_font_weight'] ?? 400) == 900 ? 'selected' : ''; ?>>900 - Black</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_description_text_transform_<?php echo $slider['id']; ?>">Text Transform</label>
                                                    <select id="edit_description_text_transform_<?php echo $slider['id']; ?>" name="description_text_transform">
                                                        <option value="none" <?php echo ($slider['description_text_transform'] ?? 'none') === 'none' ? 'selected' : ''; ?>>None</option>
                                                        <option value="uppercase" <?php echo ($slider['description_text_transform'] ?? 'none') === 'uppercase' ? 'selected' : ''; ?>>UPPERCASE</option>
                                                        <option value="lowercase" <?php echo ($slider['description_text_transform'] ?? 'none') === 'lowercase' ? 'selected' : ''; ?>>lowercase</option>
                                                        <option value="capitalize" <?php echo ($slider['description_text_transform'] ?? 'none') === 'capitalize' ? 'selected' : ''; ?>>Capitalize</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-grid">
                                                <div class="form-group">
                                                    <label for="edit_description_line_height_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Line spacing for descriptions">Line Height</label>
                                                    <input type="range" id="edit_description_line_height_<?php echo $slider['id']; ?>" name="description_line_height" min="1" max="2.5" step="0.1" value="<?php echo $slider['description_line_height'] ?? 1.6; ?>" class="range-input">
                                                    <span class="range-display"><?php echo $slider['description_line_height'] ?? 1.6; ?></span>
                                                    <button type="button" class="reset-btn" onclick="resetField('edit_description_line_height_<?php echo $slider['id']; ?>', 1.6)">Reset</button>
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_description_break_type_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="How to break long descriptions into lines">Line Breaking</label>
                                                    <select id="edit_description_break_type_<?php echo $slider['id']; ?>" name="description_break_type" onchange="toggleBreakLimitField('edit_description', '<?php echo $slider['id']; ?>')">
                                                        <option value="none" <?php echo ($slider['description_break_type'] ?? 'none') === 'none' ? 'selected' : ''; ?>>No Breaking</option>
                                                        <option value="character" <?php echo ($slider['description_break_type'] ?? 'none') === 'character' ? 'selected' : ''; ?>>Character Based</option>
                                                        <option value="word" <?php echo ($slider['description_break_type'] ?? 'none') === 'word' ? 'selected' : ''; ?>>Word Based</option>
                                                        <option value="manual" <?php echo ($slider['description_break_type'] ?? 'none') === 'manual' ? 'selected' : ''; ?>>Manual (use | for line breaks)</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label for="edit_description_break_limit_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Characters or words per line before breaking">Break Limit</label>
                                                <input type="number" id="edit_description_break_limit_<?php echo $slider['id']; ?>" name="description_break_limit" value="<?php echo $slider['description_break_limit'] ?? 100; ?>" min="50" max="200">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Layout & Height Controls -->
                                    <div class="control-section">
                                        <div class="control-section-header" onclick="toggleSection(this)">
                                            <span>📐 Layout & Height Controls</span>
                                            <span>▼</span>
                                        </div>
                                        <div class="control-section-body">
                                            <div class="form-grid-3">
                                                <div class="form-group">
                                                    <label for="edit_height_desktop_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Slider height on desktop (200-1200px)">Desktop Height</label>
                                                    <select id="edit_height_desktop_<?php echo $slider['id']; ?>" name="height_desktop" onchange="updateHeightInput(this, 'edit_height_desktop_custom_<?php echo $slider['id']; ?>')">
                                                        <option value="300" <?php echo ($slider['height_desktop'] ?? 500) == 300 ? 'selected' : ''; ?>>300px</option>
                                                        <option value="400" <?php echo ($slider['height_desktop'] ?? 500) == 400 ? 'selected' : ''; ?>>400px</option>
                                                        <option value="500" <?php echo ($slider['height_desktop'] ?? 500) == 500 ? 'selected' : ''; ?>>500px</option>
                                                        <option value="600" <?php echo ($slider['height_desktop'] ?? 500) == 600 ? 'selected' : ''; ?>>600px</option>
                                                        <option value="700" <?php echo ($slider['height_desktop'] ?? 500) == 700 ? 'selected' : ''; ?>>700px</option>
                                                        <option value="800" <?php echo ($slider['height_desktop'] ?? 500) == 800 ? 'selected' : ''; ?>>800px</option>
                                                        <option value="custom" <?php echo !in_array($slider['height_desktop'] ?? 500, [300, 400, 500, 600, 700, 800]) ? 'selected' : ''; ?>>Custom</option>
                                                    </select>
                                                    <input type="number" id="edit_height_desktop_custom_<?php echo $slider['id']; ?>" min="200" max="1200"
                                                           style="<?php echo !in_array($slider['height_desktop'] ?? 500, [300, 400, 500, 600, 700, 800]) ? 'display: block;' : 'display: none;'; ?> margin-top: 0.5rem;"
                                                           placeholder="Custom height" value="<?php echo !in_array($slider['height_desktop'] ?? 500, [300, 400, 500, 600, 700, 800]) ? $slider['height_desktop'] : ''; ?>">
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_height_tablet_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Slider height on tablet">Tablet Height</label>
                                                    <select id="edit_height_tablet_<?php echo $slider['id']; ?>" name="height_tablet" onchange="updateHeightInput(this, 'edit_height_tablet_custom_<?php echo $slider['id']; ?>')">
                                                        <option value="300" <?php echo ($slider['height_tablet'] ?? 400) == 300 ? 'selected' : ''; ?>>300px</option>
                                                        <option value="400" <?php echo ($slider['height_tablet'] ?? 400) == 400 ? 'selected' : ''; ?>>400px</option>
                                                        <option value="500" <?php echo ($slider['height_tablet'] ?? 400) == 500 ? 'selected' : ''; ?>>500px</option>
                                                        <option value="600" <?php echo ($slider['height_tablet'] ?? 400) == 600 ? 'selected' : ''; ?>>600px</option>
                                                        <option value="custom" <?php echo !in_array($slider['height_tablet'] ?? 400, [300, 400, 500, 600]) ? 'selected' : ''; ?>>Custom</option>
                                                    </select>
                                                    <input type="number" id="edit_height_tablet_custom_<?php echo $slider['id']; ?>" min="200" max="800"
                                                           style="<?php echo !in_array($slider['height_tablet'] ?? 400, [300, 400, 500, 600]) ? 'display: block;' : 'display: none;'; ?> margin-top: 0.5rem;"
                                                           placeholder="Custom height" value="<?php echo !in_array($slider['height_tablet'] ?? 400, [300, 400, 500, 600]) ? $slider['height_tablet'] : ''; ?>">
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_height_mobile_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Slider height on mobile">Mobile Height</label>
                                                    <select id="edit_height_mobile_<?php echo $slider['id']; ?>" name="height_mobile" onchange="updateHeightInput(this, 'edit_height_mobile_custom_<?php echo $slider['id']; ?>')">
                                                        <option value="250" <?php echo ($slider['height_mobile'] ?? 300) == 250 ? 'selected' : ''; ?>>250px</option>
                                                        <option value="300" <?php echo ($slider['height_mobile'] ?? 300) == 300 ? 'selected' : ''; ?>>300px</option>
                                                        <option value="350" <?php echo ($slider['height_mobile'] ?? 300) == 350 ? 'selected' : ''; ?>>350px</option>
                                                        <option value="400" <?php echo ($slider['height_mobile'] ?? 300) == 400 ? 'selected' : ''; ?>>400px</option>
                                                        <option value="custom" <?php echo !in_array($slider['height_mobile'] ?? 300, [250, 300, 350, 400]) ? 'selected' : ''; ?>>Custom</option>
                                                    </select>
                                                    <input type="number" id="edit_height_mobile_custom_<?php echo $slider['id']; ?>" min="200" max="600"
                                                           style="<?php echo !in_array($slider['height_mobile'] ?? 300, [250, 300, 350, 400]) ? 'display: block;' : 'display: none;'; ?> margin-top: 0.5rem;"
                                                           placeholder="Custom height" value="<?php echo !in_array($slider['height_mobile'] ?? 300, [250, 300, 350, 400]) ? $slider['height_mobile'] : ''; ?>">
                                                </div>
                                            </div>

                                            <h4>Padding Controls</h4>
                                            <div class="form-grid">
                                                <div class="form-group">
                                                    <label for="edit_padding_top_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Space from top of slider to title (0px and up)">Top Padding</label>
                                                    <input type="number" id="edit_padding_top_<?php echo $slider['id']; ?>" name="padding_top" min="0" step="5" value="<?php echo $slider['padding_top'] ?? 90; ?>" class="number-input" placeholder="Enter padding in pixels">
                                                    <span class="input-suffix">px</span>
                                                    <button type="button" class="reset-btn" onclick="resetField('edit_padding_top_<?php echo $slider['id']; ?>', 90)">Reset</button>
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_padding_bottom_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Space from bottom content to bottom of slider (0px and up)">Bottom Padding</label>
                                                    <input type="number" id="edit_padding_bottom_<?php echo $slider['id']; ?>" name="padding_bottom" min="0" step="5" value="<?php echo $slider['padding_bottom'] ?? 40; ?>" class="number-input" placeholder="Enter padding in pixels">
                                                    <span class="input-suffix">px</span>
                                                    <button type="button" class="reset-btn" onclick="resetField('edit_padding_bottom_<?php echo $slider['id']; ?>', 40)">Reset</button>
                                                </div>
                                            </div>

                                            <h4>Content Alignment</h4>
                                            <div class="form-grid">
                                                <div class="form-group">
                                                    <label for="edit_content_alignment_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Horizontal alignment of content">Content Alignment</label>
                                                    <select id="edit_content_alignment_<?php echo $slider['id']; ?>" name="content_alignment">
                                                        <option value="left" <?php echo ($slider['content_alignment'] ?? 'center') === 'left' ? 'selected' : ''; ?>>Left</option>
                                                        <option value="center" <?php echo ($slider['content_alignment'] ?? 'center') === 'center' ? 'selected' : ''; ?>>Center</option>
                                                        <option value="right" <?php echo ($slider['content_alignment'] ?? 'center') === 'right' ? 'selected' : ''; ?>>Right</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_vertical_alignment_<?php echo $slider['id']; ?>" class="tooltip" data-tooltip="Vertical alignment of content">Vertical Alignment</label>
                                                    <select id="edit_vertical_alignment_<?php echo $slider['id']; ?>" name="vertical_alignment">
                                                        <option value="top" <?php echo ($slider['vertical_alignment'] ?? 'center') === 'top' ? 'selected' : ''; ?>>Top</option>
                                                        <option value="center" <?php echo ($slider['vertical_alignment'] ?? 'center') === 'center' ? 'selected' : ''; ?>>Center</option>
                                                        <option value="bottom" <?php echo ($slider['vertical_alignment'] ?? 'center') === 'bottom' ? 'selected' : ''; ?>>Bottom</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Basic Settings -->
                                    <div class="control-section">
                                        <div class="control-section-header" onclick="toggleSection(this)">
                                            <span>⚙️ Basic Settings</span>
                                            <span>▼</span>
                                        </div>
                                        <div class="control-section-body active">
                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="edit_text_color_<?php echo $slider['id']; ?>">Text Color</label>
                                                    <input type="color" id="edit_text_color_<?php echo $slider['id']; ?>" name="text_color" value="<?php echo $slider['text_color'] ?? '#ffffff'; ?>" class="color-input">
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_overlay_color_<?php echo $slider['id']; ?>">Overlay Color</label>
                                                    <input type="color" id="edit_overlay_color_<?php echo $slider['id']; ?>" name="overlay_color" value="<?php echo $slider['overlay_color'] ?? '#000000'; ?>" class="color-input">
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label for="edit_overlay_opacity_<?php echo $slider['id']; ?>">Overlay Opacity</label>
                                                <input type="range" id="edit_overlay_opacity_<?php echo $slider['id']; ?>" name="overlay_opacity" min="0" max="1" step="0.1" value="<?php echo $slider['overlay_opacity'] ?? 0.5; ?>" class="range-input">
                                                <span class="range-display"><?php echo $slider['overlay_opacity'] ?? 0.5; ?></span>
                                            </div>

                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="edit_title_char_limit_<?php echo $slider['id']; ?>">Title Character Limit</label>
                                                    <input type="number" id="edit_title_char_limit_<?php echo $slider['id']; ?>" name="title_char_limit" value="<?php echo $slider['title_char_limit'] ?? 50; ?>" min="20" max="100">
                                                    <small>Characters before line break</small>
                                                </div>
                                                <div class="form-group">
                                                    <label for="edit_description_char_limit_<?php echo $slider['id']; ?>">Description Character Limit</label>
                                                    <input type="number" id="edit_description_char_limit_<?php echo $slider['id']; ?>" name="description_char_limit" value="<?php echo $slider['description_char_limit'] ?? 100; ?>" min="50" max="200">
                                                    <small>Characters per line before break</small>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" name="active" <?php echo $slider['active'] ? 'checked' : ''; ?>> Active
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn">Update Slider</button>
                                    <button type="button" class="btn" onclick="toggleForm('edit-slider-<?php echo $slider['id']; ?>')" style="background: #95a5a6;">Cancel</button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function toggleForm(formId) {
            const form = document.getElementById(formId);
            form.classList.toggle('active');
        }

        function toggleSection(header) {
            const body = header.nextElementSibling;
            const arrow = header.querySelector('span:last-child');

            if (body.classList.contains('active')) {
                body.classList.remove('active');
                arrow.textContent = '▼';
            } else {
                body.classList.add('active');
                arrow.textContent = '▲';
            }
        }

        function toggleCustomUrl(selectElement) {
            const customUrlInput = selectElement.parentNode.querySelector('input[name="custom_url"]');
            if (selectElement.value === 'custom') {
                customUrlInput.style.display = 'block';
                customUrlInput.required = true;
            } else {
                customUrlInput.style.display = 'none';
                customUrlInput.required = false;
                customUrlInput.value = '';
            }
        }

        function updateHeightInput(selectElement, customInputId) {
            const customInput = document.getElementById(customInputId);
            if (selectElement.value === 'custom') {
                customInput.style.display = 'block';
                customInput.required = true;
                // Update the actual form field name
                customInput.name = selectElement.name;
                selectElement.name = selectElement.name + '_preset';
            } else {
                customInput.style.display = 'none';
                customInput.required = false;
                customInput.name = '';
                selectElement.name = selectElement.name.replace('_preset', '');
            }
        }

        function resetField(fieldId, defaultValue) {
            const field = document.getElementById(fieldId);
            field.value = defaultValue;
            // Update display if it's a range input
            const display = field.nextElementSibling;
            if (display && display.classList.contains('range-display')) {
                display.textContent = defaultValue + (fieldId.includes('font_size') || fieldId.includes('line_height') ? 'rem' : fieldId.includes('duration') ? 's' : '');
            }
        }

        // Update range displays
        document.addEventListener('DOMContentLoaded', function() {
            const rangeInputs = document.querySelectorAll('input[type="range"]');
            rangeInputs.forEach(input => {
                const display = input.nextElementSibling;
                if (display && display.classList.contains('range-display')) {
                    input.addEventListener('input', function() {
                        let suffix = '';
                        if (this.id.includes('font_size') || this.id.includes('line_height')) {
                            suffix = 'rem';
                        } else if (this.id.includes('duration')) {
                            suffix = 's';
                        }
                        display.textContent = this.value + suffix;
                    });
                }
            });

            // Initialize section states
            const sections = document.querySelectorAll('.control-section-body');
            sections.forEach(section => {
                // Ensure proper initial state
                if (section.classList.contains('active')) {
                    section.style.display = 'block';
                } else {
                    section.style.display = 'none';
                }
            });

            // Initialize arrows
            const headers = document.querySelectorAll('.control-section-header');
            headers.forEach(header => {
                const body = header.nextElementSibling;
                const arrow = header.querySelector('span:last-child');
                if (body && arrow) {
                    arrow.textContent = body.classList.contains('active') ? '▲' : '▼';
                }
            });
        });

        // Function to toggle break limit field visibility
        function toggleBreakLimitField(type, id = '') {
            const selectId = id ? `${type}_break_type_${id}` : `${type}_break_type`;
            const groupId = id ? `${type}_break_limit_group_${id}` : `${type}_break_limit_group`;

            const select = document.getElementById(selectId);
            const group = document.getElementById(groupId);

            if (select && group) {
                const isManual = select.value === 'manual';
                const label = group.querySelector('label');
                const input = group.querySelector('input');
                const small = group.querySelector('small');

                if (isManual) {
                    input.style.opacity = '0.5';
                    input.disabled = true;
                    if (label) label.style.opacity = '0.5';
                    if (small) small.style.display = 'block';
                } else {
                    input.style.opacity = '1';
                    input.disabled = false;
                    if (label) label.style.opacity = '1';
                    if (small) small.style.display = 'block';
                }
            }
        }

        // Initialize break limit field states on page load
        document.addEventListener('DOMContentLoaded', function() {
            // For add form
            toggleBreakLimitField('title');
            toggleBreakLimitField('description');

            // For edit forms
            const editForms = document.querySelectorAll('[id^="edit_title_break_type_"]');
            editForms.forEach(select => {
                const id = select.id.split('_').pop();
                toggleBreakLimitField('edit_title', id);
            });

            const editDescForms = document.querySelectorAll('[id^="edit_description_break_type_"]');
            editDescForms.forEach(select => {
                const id = select.id.split('_').pop();
                toggleBreakLimitField('edit_description', id);
            });
        });
    </script>
</body>
</html>
