<?php
/**
 * Test Admin Navigation
 * Verify Hero Headers appears in admin navigation
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>ADMIN NAVIGATION TEST</h1><pre>";

try {
    echo "=== TESTING ADMIN NAVIGATION ===\n\n";
    
    // Get admin navigation items
    $nav_items = getAdminNavigation();
    
    echo "📋 Admin Navigation Items:\n";
    echo str_repeat("-", 60) . "\n";
    printf("%-20s %-25s %-15s %-8s\n", "Title", "URL", "Icon", "Active");
    echo str_repeat("-", 60) . "\n";
    
    $hero_headers_found = false;
    $hero_sections_found = false;
    
    foreach ($nav_items as $item) {
        printf("%-20s %-25s %-15s %-8s\n", 
            $item['title'], 
            $item['url'], 
            $item['icon'], 
            $item['active'] ? 'Yes' : 'No'
        );
        
        if ($item['title'] === 'Hero Headers') {
            $hero_headers_found = true;
        }
        if ($item['title'] === 'Hero Sections') {
            $hero_sections_found = true;
        }
    }
    
    echo str_repeat("-", 60) . "\n";
    echo "Total navigation items: " . count($nav_items) . "\n\n";
    
    // Verification
    echo "=== VERIFICATION ===\n";
    
    if ($hero_sections_found) {
        echo "✅ Hero Sections found in navigation\n";
    } else {
        echo "❌ Hero Sections NOT found in navigation\n";
    }
    
    if ($hero_headers_found) {
        echo "✅ Hero Headers found in navigation\n";
    } else {
        echo "❌ Hero Headers NOT found in navigation\n";
    }
    
    // Check if both hero items are present
    if ($hero_sections_found && $hero_headers_found) {
        echo "\n🎉 SUCCESS: Both Hero Sections and Hero Headers are in the admin navigation!\n";
        echo "\nAdmin users will see:\n";
        echo "- Hero Sections (for footer CTAs and newsletter forms)\n";
        echo "- Hero Headers (for page headers with titles and breadcrumbs)\n";
    } else {
        echo "\n❌ ISSUE: Missing hero navigation items\n";
    }
    
    // Show the specific Hero Headers item details
    echo "\n=== HERO HEADERS DETAILS ===\n";
    foreach ($nav_items as $item) {
        if ($item['title'] === 'Hero Headers') {
            echo "Title: {$item['title']}\n";
            echo "URL: {$item['url']}\n";
            echo "Icon: {$item['icon']}\n";
            echo "Full URL: http://localhost/monolith-design/admin/{$item['url']}\n";
            break;
        }
    }
    
    // Test file existence
    echo "\n=== FILE VERIFICATION ===\n";
    
    $hero_headers_file = __DIR__ . '/../admin/hero-headers.php';
    if (file_exists($hero_headers_file)) {
        echo "✅ Hero Headers admin file exists: admin/hero-headers.php\n";
        echo "   File size: " . number_format(filesize($hero_headers_file)) . " bytes\n";
    } else {
        echo "❌ Hero Headers admin file NOT found: admin/hero-headers.php\n";
    }
    
    $hero_template_file = __DIR__ . '/../templates/hero-header.php';
    if (file_exists($hero_template_file)) {
        echo "✅ Hero Header template exists: templates/hero-header.php\n";
        echo "   File size: " . number_format(filesize($hero_template_file)) . " bytes\n";
    } else {
        echo "❌ Hero Header template NOT found: templates/hero-header.php\n";
    }
    
    // Test database table
    echo "\n=== DATABASE VERIFICATION ===\n";
    
    try {
        $db = Database::getConnection();
        $stmt = $db->query("SHOW TABLES LIKE 'hero_headers'");
        $table_exists = $stmt->fetch();
        
        if ($table_exists) {
            echo "✅ hero_headers table exists in database\n";
            
            // Count entries
            $stmt = $db->query("SELECT COUNT(*) as count FROM hero_headers");
            $count = $stmt->fetch()['count'];
            echo "   Total hero headers: $count\n";
            
            // Show sample entries
            if ($count > 0) {
                $stmt = $db->query("SELECT page_name, page_title, active FROM hero_headers LIMIT 3");
                $samples = $stmt->fetchAll();
                echo "   Sample entries:\n";
                foreach ($samples as $sample) {
                    echo "   - {$sample['page_name']}: {$sample['page_title']} (" . ($sample['active'] ? 'Active' : 'Inactive') . ")\n";
                }
            }
        } else {
            echo "❌ hero_headers table NOT found in database\n";
            echo "   Run setup script: test_delete/test-hero-headers.php\n";
        }
    } catch (Exception $e) {
        echo "❌ Database error: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== SUMMARY ===\n";
    echo "✅ Admin navigation updated successfully\n";
    echo "✅ Hero Headers item added to admin menu\n";
    echo "✅ Separation between Hero Sections and Hero Headers complete\n";
    echo "\nAdmin users can now access:\n";
    echo "- Hero Sections: /admin/hero-sections.php (footer CTAs)\n";
    echo "- Hero Headers: /admin/hero-headers.php (page headers)\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
