<?php
/**
 * Testimonials Form Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure edit_testimonial variable is available
if (!isset($edit_testimonial)) {
    $edit_testimonial = null;
}
?>

<div class="admin-card">
    <div class="admin-card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas <?php echo $edit_testimonial ? 'fa-edit' : 'fa-plus'; ?> me-2"></i>
            <?php echo $edit_testimonial ? 'Edit Testimonial' : 'Add New Testimonial'; ?>
        </h5>
        <button type="button" 
                class="btn btn-outline-secondary btn-sm" 
                onclick="<?php echo $edit_testimonial ? 'cancelForm()' : 'toggleForm()'; ?>">
            <i class="fas <?php echo $edit_testimonial ? 'fa-times' : 'fa-plus'; ?> me-1"></i>
            <?php echo $edit_testimonial ? 'Cancel Edit' : 'Add New Testimonial'; ?>
        </button>
    </div>
    
    <div class="admin-card-body">
        <form method="POST" 
              enctype="multipart/form-data" 
              class="testimonial-form show" 
              id="testimonialForm">
            <input type="hidden" name="MAX_FILE_SIZE" value="<?php echo MAX_FILE_SIZE; ?>">
            <input type="hidden" name="action" value="<?php echo $edit_testimonial ? 'update_testimonial' : 'add_testimonial'; ?>">
            <?php if ($edit_testimonial): ?>
                <input type="hidden" name="id" value="<?php echo $edit_testimonial['id']; ?>">
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Main Content -->
                    <div class="form-group mb-3">
                        <label for="client_name" class="form-label">
                            <i class="fas fa-user me-1"></i>
                            Client Name <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="client_name" 
                               name="client_name" 
                               class="form-control" 
                               required 
                               value="<?php echo $edit_testimonial ? htmlspecialchars($edit_testimonial['client_name']) : ''; ?>"
                               placeholder="Enter client's full name">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="client_company" class="form-label">
                                    <i class="fas fa-building me-1"></i>
                                    Company <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       id="client_company" 
                                       name="client_company" 
                                       class="form-control" 
                                       required 
                                       value="<?php echo $edit_testimonial ? htmlspecialchars($edit_testimonial['client_company']) : ''; ?>"
                                       placeholder="Company name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="client_position" class="form-label">
                                    <i class="fas fa-briefcase me-1"></i>
                                    Position <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       id="client_position" 
                                       name="client_position" 
                                       class="form-control" 
                                       required 
                                       value="<?php echo $edit_testimonial ? htmlspecialchars($edit_testimonial['client_position']) : ''; ?>"
                                       placeholder="Job title or position">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="quote" class="form-label">
                            <i class="fas fa-quote-right me-1"></i>
                            Testimonial Quote <span class="text-danger">*</span>
                        </label>
                        <textarea id="quote" 
                                  name="quote" 
                                  class="form-control" 
                                  required 
                                  rows="6"
                                  placeholder="Enter the client's testimonial quote..."><?php echo $edit_testimonial ? htmlspecialchars($edit_testimonial['quote']) : ''; ?></textarea>
                        <div class="form-text">
                            The client's testimonial about your work or services
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- Sidebar Settings -->
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cog me-1"></i>
                                Settings
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="active" 
                                           name="active" 
                                           value="1" 
                                           <?php echo ($edit_testimonial && $edit_testimonial['active']) || !$edit_testimonial ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="active">
                                        <strong>Active</strong>
                                    </label>
                                </div>
                                <div class="form-text">Show this testimonial on the website</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-user-circle me-1"></i>
                                Client Photo
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <?php if ($edit_testimonial && $edit_testimonial['client_image']): ?>
                                <div class="current-image mb-3 text-center">
                                    <img src="<?php echo ensureAbsoluteUrl($edit_testimonial['client_image']); ?>" 
                                         alt="Current Client Photo" 
                                         class="img-fluid rounded-circle" 
                                         style="max-width: 120px;">
                                    <small class="text-muted d-block mt-1">Current client photo</small>
                                </div>
                            <?php endif; ?>
                            <input type="file" 
                                   id="client_image" 
                                   name="client_image" 
                                   class="form-control" 
                                   accept="image/*">
                            <div class="form-text">
                                Recommended: Square image, 300x300px or larger<br>
                                Formats: JPG, PNG, WebP
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-image me-1"></i>
                                Background Image
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <?php if ($edit_testimonial && $edit_testimonial['background_image']): ?>
                                <div class="current-image mb-3">
                                    <img src="<?php echo ensureAbsoluteUrl($edit_testimonial['background_image']); ?>" 
                                         alt="Current Background Image" 
                                         class="img-fluid rounded">
                                    <small class="text-muted d-block mt-1">Current background image</small>
                                </div>
                            <?php endif; ?>
                            <input type="file" 
                                   id="background_image" 
                                   name="background_image" 
                                   class="form-control" 
                                   accept="image/*">
                            <div class="form-text">
                                Optional background image for testimonial section<br>
                                Recommended: 1920x1080px or larger<br>
                                Formats: JPG, PNG, WebP
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2 mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas <?php echo $edit_testimonial ? 'fa-save' : 'fa-plus'; ?> me-1"></i>
                    <?php echo $edit_testimonial ? 'Update Testimonial' : 'Add Testimonial'; ?>
                </button>
                <button type="button" class="btn btn-secondary" onclick="cancelForm()">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.testimonial-form {
    display: none;
}

.testimonial-form.show {
    display: block;
}

.current-image img {
    max-height: 150px;
    object-fit: cover;
}

.admin-card .admin-card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-text {
    font-size: 0.8rem;
}

.form-check-input:checked {
    background-color: var(--admin-accent-color);
    border-color: var(--admin-accent-color);
}
</style>
