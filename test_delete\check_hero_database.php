<?php
/**
 * Check Hero Database Status
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

echo "<h1>Hero Database Status Check</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 2rem; }
    .success { color: #27ae60; }
    .error { color: #e74c3c; }
    .info { color: #3498db; }
    .warning { color: #f39c12; }
    pre { background: #f8f9fa; padding: 1rem; border-radius: 4px; }
    table { border-collapse: collapse; width: 100%; margin: 1rem 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

try {
    $db = Database::getConnection();
    
    // Check if hero_sections table exists
    $stmt = $db->query('SHOW TABLES LIKE "hero_sections"');
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "<p class='success'>✅ hero_sections table exists</p>";
        
        // Check table structure
        $stmt = $db->query('DESCRIBE hero_sections');
        $columns = $stmt->fetchAll();
        echo "<h2>📋 Table Structure:</h2>";
        echo "<table>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check data
        $stmt = $db->query('SELECT COUNT(*) as count FROM hero_sections');
        $count = $stmt->fetch()['count'];
        echo "<h2>📊 Total hero sections: $count</h2>";
        
        if ($count > 0) {
            $stmt = $db->query('SELECT * FROM hero_sections ORDER BY page_name');
            $heroes = $stmt->fetchAll();
            echo "<h2>📄 Hero Sections:</h2>";
            echo "<table>";
            echo "<tr><th>Page Name</th><th>Page Title</th><th>Title</th><th>Active</th><th>Background Type</th><th>Updated</th></tr>";
            foreach ($heroes as $hero) {
                $status = $hero['active'] ? '✅' : '❌';
                echo "<tr>";
                echo "<td>{$hero['page_name']}</td>";
                echo "<td>{$hero['page_title']}</td>";
                echo "<td>" . htmlspecialchars($hero['title']) . "</td>";
                echo "<td>$status</td>";
                echo "<td>{$hero['background_type']}</td>";
                echo "<td>{$hero['updated_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Test getHeroSection function
        echo "<h2>🔍 Testing getHeroSection() Function:</h2>";
        $test_pages = ['home', 'about', 'services', 'projects', 'contact'];
        foreach ($test_pages as $page) {
            $hero = getHeroSection($page);
            if ($hero) {
                echo "<p class='success'>✅ $page: Found hero section - '{$hero['title']}'</p>";
            } else {
                echo "<p class='warning'>⚠️ $page: No hero section found</p>";
            }
        }
        
    } else {
        echo "<p class='error'>❌ hero_sections table does not exist!</p>";
        echo "<p class='info'>The table needs to be created. Check test_delete/create-hero-sections-table.php</p>";
    }
    
    // Check site_pages table
    echo "<h2>📄 Site Pages Table:</h2>";
    $stmt = $db->query('SHOW TABLES LIKE "site_pages"');
    $site_pages_exists = $stmt->fetch();
    
    if ($site_pages_exists) {
        echo "<p class='success'>✅ site_pages table exists</p>";
        $stmt = $db->query('SELECT COUNT(*) as count FROM site_pages');
        $pages_count = $stmt->fetch()['count'];
        echo "<p>Total pages tracked: $pages_count</p>";
        
        if ($pages_count > 0) {
            $stmt = $db->query('SELECT page_name, page_title, has_hero_section FROM site_pages ORDER BY page_name');
            $pages = $stmt->fetchAll();
            echo "<table>";
            echo "<tr><th>Page Name</th><th>Page Title</th><th>Has Hero</th></tr>";
            foreach ($pages as $page) {
                $has_hero = $page['has_hero_section'] ? '✅' : '❌';
                echo "<tr>";
                echo "<td>{$page['page_name']}</td>";
                echo "<td>{$page['page_title']}</td>";
                echo "<td>$has_hero</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p class='warning'>⚠️ site_pages table does not exist</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

?>
