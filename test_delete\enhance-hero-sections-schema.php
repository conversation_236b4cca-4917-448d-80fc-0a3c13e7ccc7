<?php
/**
 * Enhance Hero Sections Database Schema
 * Add new columns for advanced styling controls and dynamic page support
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1><PERSON><PERSON><PERSON><PERSON>E HERO SECTIONS SCHEMA</h1><pre>";

try {
    $db = Database::getConnection();
    
    echo "=== ADDING NEW COLUMNS TO HERO SECTIONS TABLE ===\n\n";
    
    // Add new columns for enhanced styling controls
    $new_columns = [
        // Height controls
        "ADD COLUMN height_type ENUM('small', 'medium', 'large', 'custom') DEFAULT 'medium' AFTER background_gradient",
        "ADD COLUMN height_custom INT DEFAULT NULL AFTER height_type",
        
        // Text color controls
        "ADD COLUMN caption_color VARCHAR(7) DEFAULT '#ffffff' AFTER height_custom",
        "ADD COLUMN title_color VARCHAR(7) DEFAULT '#ffffff' AFTER caption_color",
        "ADD COLUMN description_color VARCHAR(7) DEFAULT '#ffffff' AFTER title_color",
        
        // Button styling controls
        "ADD COLUMN button_bg_color VARCHAR(7) DEFAULT '#E67E22' AFTER description_color",
        "ADD COLUMN button_text_color VARCHAR(7) DEFAULT '#ffffff' AFTER button_bg_color",
        "ADD COLUMN button_hover_bg_color VARCHAR(7) DEFAULT '#d35400' AFTER button_text_color",
        
        // Background controls
        "ADD COLUMN background_color VARCHAR(7) DEFAULT '#000000' AFTER button_hover_bg_color",
        "ADD COLUMN background_opacity DECIMAL(3,2) DEFAULT 0.60 AFTER background_color",
        
        // Dynamic page support
        "ADD COLUMN is_dynamic TINYINT(1) DEFAULT 0 AFTER background_opacity",
        "ADD COLUMN auto_detected TINYINT(1) DEFAULT 0 AFTER is_dynamic"
    ];
    
    foreach ($new_columns as $column_sql) {
        try {
            $db->exec("ALTER TABLE hero_sections $column_sql");
            echo "✅ Added column: " . substr($column_sql, 11, 50) . "...\n";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "ℹ️ Column already exists: " . substr($column_sql, 11, 30) . "...\n";
            } else {
                echo "❌ Error adding column: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n=== UPDATING EXISTING HERO SECTIONS WITH DEFAULT VALUES ===\n\n";
    
    // Update existing records with default styling values
    $update_sql = "
        UPDATE hero_sections SET 
            height_type = 'medium',
            caption_color = '#ffffff',
            title_color = '#ffffff', 
            description_color = '#ffffff',
            button_bg_color = '#E67E22',
            button_text_color = '#ffffff',
            button_hover_bg_color = '#d35400',
            background_color = '#000000',
            background_opacity = 0.60,
            is_dynamic = 0,
            auto_detected = 0
        WHERE height_type IS NULL OR height_type = ''
    ";
    
    $result = $db->exec($update_sql);
    echo "✅ Updated $result existing hero sections with default styling values\n";
    
    echo "\n=== CREATING PAGES TABLE FOR DYNAMIC DETECTION ===\n\n";
    
    // Create pages table to track all site pages
    $pages_table_sql = "
    CREATE TABLE IF NOT EXISTS site_pages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_name VARCHAR(100) NOT NULL UNIQUE,
        page_title VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        has_hero_section TINYINT(1) DEFAULT 0,
        auto_detected TINYINT(1) DEFAULT 1,
        last_checked TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $db->exec($pages_table_sql);
    echo "✅ Site pages table created successfully\n";
    
    echo "\n=== INSERTING KNOWN PAGES ===\n\n";
    
    // Insert known pages
    $known_pages = [
        ['home', 'Homepage', 'index.php'],
        ['about', 'About Us', 'about.php'],
        ['services', 'Services', 'services.php'],
        ['projects', 'Projects', 'projects.php'],
        ['news', 'News & Blog', 'news.php'],
        ['team', 'Our Team', 'team.php'],
        ['contact', 'Contact Us', 'contact.php']
    ];
    
    $stmt = $db->prepare("
        INSERT INTO site_pages (page_name, page_title, file_path, has_hero_section) 
        VALUES (?, ?, ?, 1)
        ON DUPLICATE KEY UPDATE 
            page_title = VALUES(page_title),
            file_path = VALUES(file_path),
            has_hero_section = 1
    ");
    
    foreach ($known_pages as $page) {
        $result = $stmt->execute($page);
        if ($result) {
            echo "✅ Added page: {$page[0]} ({$page[1]})\n";
        }
    }
    
    echo "\n=== VERIFYING ENHANCED SCHEMA ===\n";
    
    // Check the new table structure
    $stmt = $db->query("DESCRIBE hero_sections");
    $columns = $stmt->fetchAll();
    
    echo "\nHero Sections Table Columns:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']}\n";
    }
    
    echo "\n=== SCHEMA ENHANCEMENT COMPLETE ===\n";
    echo "✅ Hero sections table enhanced with styling controls\n";
    echo "✅ Site pages table created for dynamic page detection\n";
    echo "✅ Default values applied to existing hero sections\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
