<?php
/**
 * Admin Layout Template
 * Main layout template for all admin pages
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Load theme configuration
require_once __DIR__ . '/../config/theme-config.php';
$themeConfig = getAdminThemeConfig();
$cssVars = $themeConfig->getCSSVariables();

// Define helper functions if not exists
if (!function_exists('themeUrl')) {
    function themeUrl($path) {
        return '../' . $path;
    }
}

if (!function_exists('siteUrl')) {
    function siteUrl() {
        return '../';
    }
}

// Define constants if not defined
if (!defined('ADMIN_PATH')) {
    define('ADMIN_PATH', '.');
}

if (!defined('ADMIN_EMAIL')) {
    define('ADMIN_EMAIL', '<EMAIL>');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - ' : ''; ?>Admin Dashboard - Monolith Design Co.</title>
    
    <!-- Favicon -->
    <?php
    $favicon_url = getThemeOption('favicon', themeUrl('images/favicon.ico'));
    $favicon_ext = strtolower(pathinfo($favicon_url, PATHINFO_EXTENSION));
    $favicon_type = $favicon_ext === 'png' ? 'image/png' : ($favicon_ext === 'svg' ? 'image/svg+xml' : 'image/x-icon');
    ?>
    <link rel="icon" type="<?php echo $favicon_type; ?>" href="<?php echo $favicon_url; ?>">
    <link rel="alternate icon" type="image/x-icon" href="<?php echo $favicon_url; ?>">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Public+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin Theme CSS -->
    <link rel="stylesheet" href="<?php echo ADMIN_PATH; ?>/theme/css/admin-theme.css">
    <link rel="stylesheet" href="<?php echo ADMIN_PATH; ?>/theme/css/admin-components.css">
    
    <!-- Dynamic CSS Variables -->
    <style>
        :root {
            <?php foreach ($cssVars as $property => $value): ?>
            <?php echo $property; ?>: <?php echo $value; ?>;
            <?php endforeach; ?>
        }
    </style>
    
    <!-- Page-specific CSS -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link rel="stylesheet" href="<?php echo $css_file; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <?php if (isset($inline_css)): ?>
        <style><?php echo $inline_css; ?></style>
    <?php endif; ?>
</head>
<body class="admin-body <?php echo isset($body_class) ? $body_class : ''; ?>">
    
    <!-- Admin Header -->
    <?php include __DIR__ . '/../templates/admin-header.php'; ?>
    
    <!-- Admin Navigation -->
    <?php include __DIR__ . '/../templates/admin-nav.php'; ?>
    
    <!-- Main Content Area -->
    <main class="admin-main">
        <div class="admin-container">
            
            <!-- Page Header -->
            <?php if (isset($show_page_header) && $show_page_header): ?>
                <div class="admin-page-header">
                    <div class="page-header-content">
                        <?php if (isset($page_icon)): ?>
                            <div class="page-icon">
                                <i class="<?php echo htmlspecialchars($page_icon); ?>"></i>
                            </div>
                        <?php endif; ?>
                        <div class="page-title-section">
                            <h1 class="page-title"><?php echo isset($page_title) ? htmlspecialchars($page_title) : 'Admin Dashboard'; ?></h1>
                            <?php if (isset($page_description)): ?>
                                <p class="page-description"><?php echo htmlspecialchars($page_description); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if (isset($page_actions)): ?>
                        <div class="page-actions">
                            <?php foreach ($page_actions as $action): ?>
                                <a href="<?php echo htmlspecialchars($action['url']); ?>" 
                                   class="btn <?php echo isset($action['class']) ? $action['class'] : 'btn-primary'; ?>">
                                    <?php if (isset($action['icon'])): ?>
                                        <i class="<?php echo htmlspecialchars($action['icon']); ?>"></i>
                                    <?php endif; ?>
                                    <?php echo htmlspecialchars($action['label']); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <!-- Messages -->
            <?php if (isset($message) && $message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error) && $error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Page Content -->
            <div class="admin-content">
                <?php
                // Include the page content
                if (isset($content_file) && file_exists($content_file)) {
                    include $content_file;
                } elseif (isset($content)) {
                    echo $content;
                } else {
                    echo '<div class="alert alert-warning">No content specified for this page.</div>';
                }
                ?>
            </div>
            
        </div>
    </main>
    
    <!-- Admin Footer -->
    <?php include __DIR__ . '/../templates/admin-footer.php'; ?>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin Theme JS -->
    <script src="<?php echo ADMIN_PATH; ?>/theme/js/admin-theme.js"></script>
    
    <!-- Page-specific JS -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js_file): ?>
            <script src="<?php echo $js_file; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <?php if (isset($inline_js)): ?>
        <script><?php echo $inline_js; ?></script>
    <?php endif; ?>
    
</body>
</html>
