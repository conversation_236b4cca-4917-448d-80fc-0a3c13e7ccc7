/**
 * Admin Theme JavaScript
 * Interactive functionality for the admin dashboard
 */

(function() {
    'use strict';

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeAdminTheme();
    });

    /**
     * Initialize admin theme functionality
     */
    function initializeAdminTheme() {
        initializeTabs();
        initializeColorPickers();
        initializeFormValidation();
        initializeTooltips();
        initializeConfirmDialogs();
        initializeAutoSave();
        initializeSearchFilters();
    }

    /**
     * Initialize tab functionality
     */
    function initializeTabs() {
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const targetId = this.getAttribute('onclick')?.match(/showTab\('(.+?)'\)/)?.[1];
                if (targetId) {
                    showTab(targetId);
                }
            });
        });

        // Global showTab function for backward compatibility
        window.showTab = function(tabId) {
            // Hide all tab contents
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show target tab content
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // Add active class to clicked tab
            const activeTab = document.querySelector(`[onclick*="showTab('${tabId}')"]`);
            if (activeTab) {
                activeTab.classList.add('active');
            }

            // Save active tab to localStorage
            localStorage.setItem('activeAdminTab', tabId);
        };

        // Restore active tab from localStorage
        const savedTab = localStorage.getItem('activeAdminTab');
        if (savedTab && document.getElementById(savedTab)) {
            showTab(savedTab);
        }
    }

    /**
     * Initialize color picker functionality
     */
    function initializeColorPickers() {
        const colorPickers = document.querySelectorAll('.color-picker');
        
        colorPickers.forEach(picker => {
            const textInput = picker.parentElement.querySelector('.color-text');
            
            if (textInput) {
                // Update text input when color picker changes
                picker.addEventListener('input', function() {
                    textInput.value = this.value.toUpperCase();
                });

                // Update color picker when text input changes
                textInput.addEventListener('input', function() {
                    if (isValidHexColor(this.value)) {
                        picker.value = this.value;
                    }
                });
            }
        });
    }

    /**
     * Validate hex color format
     */
    function isValidHexColor(hex) {
        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
    }

    /**
     * Initialize form validation
     */
    function initializeFormValidation() {
        const forms = document.querySelectorAll('form[data-validate="true"]');
        
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!validateForm(this)) {
                    e.preventDefault();
                    showAlert('Please fill in all required fields correctly.', 'danger');
                }
            });
        });
    }

    /**
     * Validate form fields
     */
    function validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        return isValid;
    }

    /**
     * Initialize Bootstrap tooltips
     */
    function initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    /**
     * Initialize confirmation dialogs
     */
    function initializeConfirmDialogs() {
        const confirmButtons = document.querySelectorAll('[data-confirm]');
        
        confirmButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                const message = this.getAttribute('data-confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                }
            });
        });
    }

    /**
     * Initialize auto-save functionality
     */
    function initializeAutoSave() {
        const autoSaveForms = document.querySelectorAll('[data-autosave="true"]');
        
        autoSaveForms.forEach(form => {
            const inputs = form.querySelectorAll('input, select, textarea');
            let saveTimeout;

            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(() => {
                        autoSaveForm(form);
                    }, 2000); // Save after 2 seconds of inactivity
                });
            });
        });
    }

    /**
     * Auto-save form data
     */
    function autoSaveForm(form) {
        const formData = new FormData(form);
        const formId = form.id || 'admin-form';
        
        // Save to localStorage
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        localStorage.setItem(`autosave_${formId}`, JSON.stringify(data));
        showAlert('Changes auto-saved', 'info', 2000);
    }

    /**
     * Initialize search and filter functionality
     */
    function initializeSearchFilters() {
        const searchInputs = document.querySelectorAll('[data-search-target]');
        
        searchInputs.forEach(input => {
            const targetSelector = input.getAttribute('data-search-target');
            const targets = document.querySelectorAll(targetSelector);
            
            input.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                
                targets.forEach(target => {
                    const text = target.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        target.style.display = '';
                    } else {
                        target.style.display = 'none';
                    }
                });
            });
        });
    }

    /**
     * Show alert message
     */
    function showAlert(message, type = 'info', duration = 5000) {
        const alertContainer = document.querySelector('.admin-content') || document.body;
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        alertContainer.insertBefore(alert, alertContainer.firstChild);
        
        // Auto-dismiss after duration
        if (duration > 0) {
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, duration);
        }
    }

    /**
     * Get icon for alert type
     */
    function getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Loading state management
     */
    function setLoadingState(element, loading = true) {
        if (loading) {
            element.classList.add('loading');
            element.disabled = true;
        } else {
            element.classList.remove('loading');
            element.disabled = false;
        }
    }

    /**
     * AJAX form submission helper
     */
    function submitFormAjax(form, callback) {
        const formData = new FormData(form);
        const submitButton = form.querySelector('[type="submit"]');
        
        setLoadingState(submitButton, true);
        
        fetch(form.action || window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            setLoadingState(submitButton, false);
            if (callback) callback(data);
        })
        .catch(error => {
            setLoadingState(submitButton, false);
            showAlert('An error occurred. Please try again.', 'danger');
            console.error('Form submission error:', error);
        });
    }

    /**
     * Theme preview functionality
     */
    window.previewTheme = function() {
        const root = document.documentElement;
        const form = document.querySelector('form');
        
        if (form) {
            const formData = new FormData(form);
            
            // Apply theme changes temporarily
            if (formData.get('admin_primary_color')) {
                root.style.setProperty('--admin-primary-color', formData.get('admin_primary_color'));
            }
            if (formData.get('admin_secondary_color')) {
                root.style.setProperty('--admin-secondary-color', formData.get('admin_secondary_color'));
            }
            if (formData.get('admin_accent_color')) {
                root.style.setProperty('--admin-accent-color', formData.get('admin_accent_color'));
            }
            if (formData.get('border_radius')) {
                root.style.setProperty('--admin-border-radius', formData.get('border_radius'));
            }
            
            showAlert('Theme preview applied! Save to make changes permanent.', 'info');
        }
    };

    /**
     * Reset theme to defaults
     */
    window.resetAdminTheme = function() {
        if (confirm('Are you sure you want to reset the admin theme to default settings?')) {
            // Reset form fields to defaults
            const form = document.querySelector('form');
            if (form) {
                form.reset();
                
                // Set specific default values
                const defaults = {
                    'framework': 'bootstrap5',
                    'admin_primary_color': '#1A1A1A',
                    'admin_secondary_color': '#F5F5F5',
                    'admin_accent_color': '#E67E22',
                    'font_primary': "'Inter', sans-serif",
                    'font_secondary': "'Public Sans', sans-serif",
                    'border_radius': '8px',
                    'container_width': '1400px'
                };
                
                Object.entries(defaults).forEach(([key, value]) => {
                    const field = form.querySelector(`[name="${key}"]`);
                    if (field) {
                        field.value = value;
                    }
                });
                
                showAlert('Theme reset to default settings.', 'success');
            }
        }
    };

    // Export functions for global use
    window.AdminTheme = {
        showAlert,
        setLoadingState,
        submitFormAjax,
        validateForm
    };

})();
