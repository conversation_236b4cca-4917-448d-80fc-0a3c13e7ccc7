<?php
/**
 * Admin Theme Configuration System
 * Monolith Design Co. - Professional Admin Theme Framework
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

class AdminThemeConfig {
    private $db;
    private $cache = [];
    
    public function __construct() {
        $this->db = Database::getConnection();
    }
    
    /**
     * Get theme configuration value
     */
    public function get($section, $key = null, $default = null) {
        $cache_key = $section . ($key ? '.' . $key : '');
        
        if (isset($this->cache[$cache_key])) {
            return $this->cache[$cache_key];
        }
        
        if ($key) {
            // Get specific key from section
            $option_name = "admin_theme_{$section}_{$key}";
            $value = $this->getThemeOption($option_name, $default);
        } else {
            // Get entire section
            $value = $this->getThemeSection($section);
        }
        
        $this->cache[$cache_key] = $value;
        return $value;
    }
    
    /**
     * Set theme configuration value
     */
    public function set($section, $key, $value) {
        $option_name = "admin_theme_{$section}_{$key}";
        $this->updateThemeOption($option_name, $value);
        
        // Clear cache
        $cache_key = $section . '.' . $key;
        unset($this->cache[$cache_key]);
        unset($this->cache[$section]);
    }
    
    /**
     * Get theme section as array
     */
    private function getThemeSection($section) {
        $stmt = $this->db->prepare("SELECT option_name, option_value FROM theme_options WHERE option_name LIKE ?");
        $stmt->execute(["admin_theme_{$section}_%"]);
        $results = $stmt->fetchAll();
        
        $section_data = [];
        foreach ($results as $row) {
            $key = str_replace("admin_theme_{$section}_", '', $row['option_name']);
            $section_data[$key] = $row['option_value'];
        }
        
        // Apply defaults if section is empty
        if (empty($section_data)) {
            $section_data = $this->getDefaultConfig($section);
        }
        
        return $section_data;
    }
    
    /**
     * Get single theme option
     */
    private function getThemeOption($option_name, $default = null) {
        $stmt = $this->db->prepare("SELECT option_value FROM theme_options WHERE option_name = ?");
        $stmt->execute([$option_name]);
        $result = $stmt->fetch();
        
        return $result ? $result['option_value'] : $default;
    }
    
    /**
     * Update theme option
     */
    private function updateThemeOption($option_name, $value) {
        $stmt = $this->db->prepare("
            INSERT INTO theme_options (option_name, option_value)
            VALUES (?, ?)
            ON DUPLICATE KEY UPDATE option_value = VALUES(option_value)
        ");
        return $stmt->execute([$option_name, $value]);
    }
    
    /**
     * Get default configuration for sections
     */
    private function getDefaultConfig($section) {
        $defaults = [
            'admin_theme' => [
                'framework' => 'bootstrap5',
                'primary_color' => '#1A1A1A',
                'secondary_color' => '#F5F5F5',
                'accent_color' => '#E67E22',
                'font_family_primary' => "'Inter', sans-serif",
                'font_family_secondary' => "'Public Sans', sans-serif",
                'border_radius' => '8px',
                'container_max_width' => '1400px',
                'compact_mode' => false,
                'dark_mode' => false,
                'enable_animations' => true,
                'sidebar_style' => 'modern',
                'card_style' => 'elevated'
            ],
            'navigation' => [
                'style' => 'horizontal',
                'show_icons' => true,
                'sticky' => true,
                'background' => 'white',
                'text_color' => '#1A1A1A',
                'active_color' => '#E67E22'
            ],
            'layout' => [
                'header_height' => '70px',
                'content_padding' => '2rem',
                'card_spacing' => '1.5rem',
                'border_radius' => '12px',
                'shadow_style' => 'soft'
            ]
        ];
        
        return isset($defaults[$section]) ? $defaults[$section] : [];
    }
    
    /**
     * Get complete theme configuration
     */
    public function getAllConfig() {
        return [
            'admin_theme' => $this->get('admin_theme'),
            'navigation' => $this->get('navigation'),
            'layout' => $this->get('layout')
        ];
    }
    
    /**
     * Reset section to defaults
     */
    public function resetSection($section) {
        // Delete all options for this section
        $stmt = $this->db->prepare("DELETE FROM theme_options WHERE option_name LIKE ?");
        $stmt->execute(["admin_theme_{$section}_%"]);
        
        // Clear cache
        $this->cache = [];
        
        return true;
    }
    
    /**
     * Export theme configuration
     */
    public function exportConfig() {
        return json_encode($this->getAllConfig(), JSON_PRETTY_PRINT);
    }
    
    /**
     * Import theme configuration
     */
    public function importConfig($json_config) {
        $config = json_decode($json_config, true);
        if (!$config) {
            return false;
        }
        
        foreach ($config as $section => $settings) {
            foreach ($settings as $key => $value) {
                $this->set($section, $key, $value);
            }
        }
        
        return true;
    }
    
    /**
     * Get CSS variables for theme
     */
    public function getCSSVariables() {
        $theme = $this->get('admin_theme');
        $layout = $this->get('layout');
        
        return [
            '--admin-primary-color' => $theme['primary_color'] ?? '#1A1A1A',
            '--admin-secondary-color' => $theme['secondary_color'] ?? '#F5F5F5',
            '--admin-accent-color' => $theme['accent_color'] ?? '#E67E22',
            '--admin-font-primary' => $theme['font_family_primary'] ?? "'Inter', sans-serif",
            '--admin-font-secondary' => $theme['font_family_secondary'] ?? "'Public Sans', sans-serif",
            '--admin-border-radius' => $theme['border_radius'] ?? '8px',
            '--admin-container-width' => $theme['container_max_width'] ?? '1400px',
            '--admin-header-height' => $layout['header_height'] ?? '70px',
            '--admin-content-padding' => $layout['content_padding'] ?? '2rem',
            '--admin-card-spacing' => $layout['card_spacing'] ?? '1.5rem',
            '--admin-shadow-soft' => '0 2px 12px rgba(0, 0, 0, 0.08)',
            '--admin-shadow-medium' => '0 4px 20px rgba(0, 0, 0, 0.12)',
            '--admin-shadow-strong' => '0 8px 32px rgba(0, 0, 0, 0.16)'
        ];
    }
}

// Initialize global theme config instance
if (!isset($GLOBALS['adminThemeConfig'])) {
    $GLOBALS['adminThemeConfig'] = new AdminThemeConfig();
}

/**
 * Helper function to get theme config instance
 */
if (!function_exists('getAdminThemeConfig')) {
    function getAdminThemeConfig() {
        return $GLOBALS['adminThemeConfig'];
    }
}

/**
 * Helper function to get admin theme setting
 */
if (!function_exists('getAdminThemeSetting')) {
    function getAdminThemeSetting($section, $key = null, $default = null) {
        return getAdminThemeConfig()->get($section, $key, $default);
    }
}

/**
 * Helper function to set admin theme setting
 */
if (!function_exists('setAdminThemeSetting')) {
    function setAdminThemeSetting($section, $key, $value) {
        return getAdminThemeConfig()->set($section, $key, $value);
    }
}
