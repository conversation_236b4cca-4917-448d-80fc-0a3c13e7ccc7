<?php
/**
 * Testimonials Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Session is already started in functions.php

// Check admin authentication
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_testimonial':
                $client_name = sanitizeInput($_POST['client_name']);
                $client_company = sanitizeInput($_POST['client_company']);
                $client_position = sanitizeInput($_POST['client_position']);
                $quote = sanitizeInput($_POST['quote']);
                $active = isset($_POST['active']) ? 1 : 0;
                $client_image = '';
                $background_image = '';

                // Handle client image upload
                if (isset($_FILES['client_image']) && $_FILES['client_image']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['client_image'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload_result['success']) {
                        $client_image = $upload_result['url'];
                    } else {
                        $error = 'Client image upload failed: ' . $upload_result['message'];
                        break;
                    }
                }

                // Handle background image upload
                if (isset($_FILES['background_image']) && $_FILES['background_image']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload_result['success']) {
                        $background_image = $upload_result['url'];
                    } else {
                        $error = 'Background image upload failed: ' . $upload_result['message'];
                        break;
                    }
                }

                if (!$error) {
                    try {
                        $stmt = $db->prepare("INSERT INTO testimonials (client_name, client_company, client_position, quote, client_image, background_image, active) VALUES (?, ?, ?, ?, ?, ?, ?)");
                        $result = $stmt->execute([$client_name, $client_company, $client_position, $quote, $client_image, $background_image, $active]);
                        
                        if ($result) {
                            $message = 'Testimonial added successfully!';
                        } else {
                            $error = 'Failed to add testimonial.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'update_testimonial':
                $id = intval($_POST['id']);
                $client_name = sanitizeInput($_POST['client_name']);
                $client_company = sanitizeInput($_POST['client_company']);
                $client_position = sanitizeInput($_POST['client_position']);
                $quote = sanitizeInput($_POST['quote']);
                $active = isset($_POST['active']) ? 1 : 0;

                // Get current images
                $stmt = $db->prepare("SELECT client_image, background_image FROM testimonials WHERE id = ?");
                $stmt->execute([$id]);
                $current = $stmt->fetch();
                $client_image = $current['client_image'] ?? '';
                $background_image = $current['background_image'] ?? '';

                // Handle client image upload
                if (isset($_FILES['client_image']) && $_FILES['client_image']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['client_image'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload_result['success']) {
                        $client_image = $upload_result['url'];
                    } else {
                        $error = 'Client image upload failed: ' . $upload_result['message'];
                        break;
                    }
                }

                // Handle background image upload
                if (isset($_FILES['background_image']) && $_FILES['background_image']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload_result['success']) {
                        $background_image = $upload_result['url'];
                    } else {
                        $error = 'Background image upload failed: ' . $upload_result['message'];
                        break;
                    }
                }

                if (!$error) {
                    try {
                        $stmt = $db->prepare("UPDATE testimonials SET client_name = ?, client_company = ?, client_position = ?, quote = ?, client_image = ?, background_image = ?, active = ? WHERE id = ?");
                        $result = $stmt->execute([$client_name, $client_company, $client_position, $quote, $client_image, $background_image, $active, $id]);
                        
                        if ($result) {
                            $message = 'Testimonial updated successfully!';
                        } else {
                            $error = 'Failed to update testimonial.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'delete_testimonial':
                $id = intval($_POST['id']);
                try {
                    $stmt = $db->prepare("DELETE FROM testimonials WHERE id = ?");
                    $result = $stmt->execute([$id]);
                    
                    if ($result) {
                        $message = 'Testimonial deleted successfully!';
                    } else {
                        $error = 'Failed to delete testimonial.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get all testimonials
try {
    $stmt = $db->query("SELECT * FROM testimonials ORDER BY created_at DESC");
    $testimonials = $stmt->fetchAll();

    // Get statistics
    $stats = [
        'total' => count($testimonials),
        'active' => count(array_filter($testimonials, fn($t) => $t['active'] == 1)),
        'inactive' => count(array_filter($testimonials, fn($t) => $t['active'] == 0)),
        'recent' => count(array_filter($testimonials, fn($t) => strtotime($t['created_at']) > strtotime('-30 days')))
    ];
} catch (Exception $e) {
    $testimonials = [];
    $stats = ['total' => 0, 'active' => 0, 'inactive' => 0, 'recent' => 0];
    $error = 'Failed to load testimonials: ' . $e->getMessage();
}

// Get testimonial for editing
$edit_testimonial = null;
if (isset($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $stmt = $db->prepare("SELECT * FROM testimonials WHERE id = ?");
    $stmt->execute([$edit_id]);
    $edit_testimonial = $stmt->fetch();
}

// Render the page using the new theme system
renderAdminPage('management', [
    'page_title' => 'Testimonials Management',
    'page_icon' => 'fas fa-quote-right',
    'page_description' => 'Manage client testimonials and reviews to showcase your work and build trust.',
    'management_title' => 'Client Testimonials',
    'management_description' => 'Collect and display client testimonials to build credibility and showcase your expertise.',
    'management_actions' => [
        [
            'url' => siteUrl('testimonials'),
            'label' => 'View Testimonials',
            'class' => 'btn-outline-primary',
            'icon' => 'fas fa-external-link-alt',
            'target' => '_blank'
        ]
    ],
    'show_search' => true,
    'search_placeholder' => 'Search testimonials by client name, company, or content...',
    'search_target' => '.searchable-item',
    'show_table' => true,
    'table_title' => 'All Testimonials',
    'table_content_file' => __DIR__ . '/theme/content/testimonials-table.php',
    'show_stats' => true,
    'stats_cards' => [
        [
            'icon' => 'fas fa-quote-right',
            'number' => $stats['total'],
            'label' => 'Total Testimonials'
        ],
        [
            'icon' => 'fas fa-check-circle',
            'number' => $stats['active'],
            'label' => 'Active'
        ],
        [
            'icon' => 'fas fa-pause-circle',
            'number' => $stats['inactive'],
            'label' => 'Inactive'
        ],
        [
            'icon' => 'fas fa-calendar-alt',
            'number' => $stats['recent'],
            'label' => 'Recent (30d)'
        ]
    ],
    'custom_content_before_table' => function() use ($edit_testimonial) {
        include __DIR__ . '/theme/content/testimonials-form.php';
    },
    'message' => $message,
    'error' => $error,
    'testimonials' => $testimonials,
    'edit_testimonial' => $edit_testimonial,
    'custom_js' => '
        function toggleForm() {
            const form = document.getElementById("testimonialForm");
            if (form) {
                form.classList.toggle("show");
                if (form.classList.contains("show")) {
                    document.getElementById("client_name").focus();
                }
            }
        }

        function cancelForm() {
            window.location.href = "testimonials.php";
        }
    '
]);
?>
