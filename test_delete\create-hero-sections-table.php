<?php
/**
 * Create Hero Sections Table
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>CREATE HERO SECTIONS TABLE</h1><pre>";

try {
    $db = Database::getConnection();
    
    echo "=== CREATING HERO SECTIONS TABLE ===\n\n";
    
    // Create hero_sections table
    $sql = "
    CREATE TABLE IF NOT EXISTS hero_sections (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_name VARCHAR(100) NOT NULL UNIQUE,
        page_title VARCHAR(255) NOT NULL,
        caption VARCHAR(255) DEFAULT '',
        title VARCHAR(255) NOT NULL,
        description TEXT,
        button_text VARCHAR(100) DEFAULT 'Get Started',
        button_link VARCHAR(255) DEFAULT 'contact',
        background_type ENUM('image', 'gradient') DEFAULT 'gradient',
        background_image VARCHAR(500) DEFAULT '',
        background_gradient VARCHAR(255) DEFAULT 'linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)',
        active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $db->exec($sql);
    echo "✅ Hero sections table created successfully\n\n";
    
    echo "=== INSERTING DEFAULT HERO SECTIONS ===\n\n";
    
    // Insert default hero sections for each page
    $default_heroes = [
        [
            'page_name' => 'home',
            'page_title' => 'Homepage',
            'caption' => 'Ready to Build?',
            'title' => 'Ready to Get Started?',
            'description' => "Let's transform your vision into reality with our innovative architectural solutions and expert craftsmanship.",
            'button_text' => 'Start Your Project',
            'button_link' => 'contact',
            'background_type' => 'gradient',
            'background_gradient' => 'linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)'
        ],
        [
            'page_name' => 'about',
            'page_title' => 'About Page',
            'caption' => 'Ready to Build?',
            'title' => 'Ready to Get Started?',
            'description' => "Let's create something amazing together. Our experienced team is ready to bring your vision to life.",
            'button_text' => 'Start Your Project',
            'button_link' => 'contact',
            'background_type' => 'gradient',
            'background_gradient' => 'linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)'
        ],
        [
            'page_name' => 'services',
            'page_title' => 'Services Page',
            'caption' => 'Ready to Build?',
            'title' => 'Ready to Get Started?',
            'description' => 'Ready to bring your vision to life? Our team of experienced professionals is here to guide you through every step of your construction journey.',
            'button_text' => 'Start Your Project',
            'button_link' => 'contact',
            'background_type' => 'gradient',
            'background_gradient' => 'linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)'
        ],
        [
            'page_name' => 'projects',
            'page_title' => 'Projects Page',
            'caption' => 'Ready to Build?',
            'title' => 'Ready to Get Started?',
            'description' => 'Discover our portfolio of architectural excellence and let us bring your vision to life.',
            'button_text' => 'View Our Work',
            'button_link' => 'projects',
            'background_type' => 'gradient',
            'background_gradient' => 'linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)'
        ],
        [
            'page_name' => 'news',
            'page_title' => 'News Page',
            'caption' => 'Stay Updated',
            'title' => 'Ready to Get Started?',
            'description' => 'Stay connected with the latest developments and insights from our team of experts.',
            'button_text' => 'Contact Us',
            'button_link' => 'contact',
            'background_type' => 'gradient',
            'background_gradient' => 'linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)'
        ],
        [
            'page_name' => 'team',
            'page_title' => 'Team Page',
            'caption' => 'Join Our Team',
            'title' => 'Ready to Get Started?',
            'description' => 'Meet our expert team and discover how we can help bring your architectural vision to life.',
            'button_text' => 'Contact Us',
            'button_link' => 'contact',
            'background_type' => 'gradient',
            'background_gradient' => 'linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)'
        ],
        [
            'page_name' => 'contact',
            'page_title' => 'Contact Page',
            'caption' => 'Get In Touch',
            'title' => 'Ready to Get Started?',
            'description' => 'Ready to discuss your project? Our team is here to help you every step of the way.',
            'button_text' => 'Call Us Now',
            'button_link' => 'tel:+15551234567',
            'background_type' => 'gradient',
            'background_gradient' => 'linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%)'
        ]
    ];
    
    $stmt = $db->prepare("
        INSERT INTO hero_sections (page_name, page_title, caption, title, description, button_text, button_link, background_type, background_gradient) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
            page_title = VALUES(page_title),
            caption = VALUES(caption),
            title = VALUES(title),
            description = VALUES(description),
            button_text = VALUES(button_text),
            button_link = VALUES(button_link),
            background_type = VALUES(background_type),
            background_gradient = VALUES(background_gradient)
    ");
    
    foreach ($default_heroes as $hero) {
        $result = $stmt->execute([
            $hero['page_name'],
            $hero['page_title'],
            $hero['caption'],
            $hero['title'],
            $hero['description'],
            $hero['button_text'],
            $hero['button_link'],
            $hero['background_type'],
            $hero['background_gradient']
        ]);
        
        if ($result) {
            echo "✅ Added hero for {$hero['page_name']} page\n";
        } else {
            echo "❌ Failed to add hero for {$hero['page_name']} page\n";
        }
    }
    
    echo "\n=== VERIFYING HERO SECTIONS ===\n";
    
    $stmt = $db->query("SELECT page_name, page_title, caption, title FROM hero_sections ORDER BY page_name");
    $heroes = $stmt->fetchAll();
    
    foreach ($heroes as $hero) {
        echo "✅ {$hero['page_name']}: {$hero['title']}\n";
    }
    
    echo "\n=== HERO SECTIONS TABLE READY ===\n";
    echo "Total hero sections: " . count($heroes) . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
