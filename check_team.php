<?php
require_once 'config.php';
require_once 'includes/functions.php';

$members = getTeamMembers();
echo "Team members in database: " . count($members) . "\n";

foreach($members as $member) {
    echo "- " . $member['name'] . " (" . $member['position'] . ")\n";
}

if (count($members) == 0) {
    echo "\nNo team members found. You can add them through the admin panel at admin/team.php\n";
    echo "Or we can add some sample data now.\n";
}
?>
