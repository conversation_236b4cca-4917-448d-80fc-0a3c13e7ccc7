<?php
/**
 * Test Dynamic Page Detection System
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/hero-page-detection.php';

echo "<h1>TEST DYNAMIC PAGE DETECTION</h1><pre>";

try {
    echo "=== SCANNING FOR PAGES ===\n\n";
    
    $detected_pages = scanForNewPages();
    echo "Found " . count($detected_pages) . " pages:\n";
    
    foreach ($detected_pages as $page) {
        echo "- {$page['page_name']}: {$page['page_title']} ({$page['file_path']})\n";
    }
    
    echo "\n=== UPDATING PAGE DATABASE ===\n\n";
    
    $new_pages = updateDetectedPages();
    if (empty($new_pages)) {
        echo "No new pages detected\n";
    } else {
        echo "New pages added:\n";
        foreach ($new_pages as $page) {
            echo "- {$page['page_name']}: {$page['page_title']}\n";
        }
    }
    
    echo "\n=== PAGES WITHOUT HERO SECTIONS ===\n\n";
    
    $pages_without_heroes = getPagesWithoutHeroSections();
    if (empty($pages_without_heroes)) {
        echo "All pages have hero sections!\n";
    } else {
        echo "Pages missing hero sections:\n";
        foreach ($pages_without_heroes as $page) {
            echo "- {$page['page_name']}: {$page['page_title']} ({$page['file_path']})\n";
        }
    }
    
    echo "\n=== TESTING HERO SECTION CREATION ===\n\n";
    
    // Test creating a hero section for a page without one
    if (!empty($pages_without_heroes)) {
        $test_page = $pages_without_heroes[0];
        echo "Creating hero section for: {$test_page['page_name']}\n";
        
        $custom_hero_data = [
            'caption' => 'Test Page',
            'title' => 'Welcome to ' . $test_page['page_title'],
            'description' => 'This is a dynamically created hero section for the ' . $test_page['page_title'] . ' page.',
            'button_text' => 'Explore More',
            'height_type' => 'large',
            'title_color' => '#ffffff',
            'button_bg_color' => '#E67E22'
        ];
        
        $result = createHeroSectionForPage(
            $test_page['page_name'], 
            $test_page['page_title'], 
            $custom_hero_data
        );
        
        if ($result) {
            echo "✅ Hero section created successfully!\n";
        } else {
            echo "❌ Failed to create hero section\n";
        }
    }
    
    echo "\n=== TESTING CSS GENERATION ===\n\n";
    
    // Test CSS generation for a hero section
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM hero_sections LIMIT 1");
    $hero = $stmt->fetch();
    
    if ($hero) {
        echo "Testing CSS generation for: {$hero['page_name']}\n";
        $css = getHeroSectionCSS($hero);
        echo "Generated CSS variables:\n$css\n";
        
        echo "\nHeight calculation test:\n";
        echo "- Small: " . getHeroHeight('small') . "\n";
        echo "- Medium: " . getHeroHeight('medium') . "\n";
        echo "- Large: " . getHeroHeight('large') . "\n";
        echo "- Custom (500px): " . getHeroHeight('custom', 500) . "\n";
    }
    
    echo "\n=== CURRENT SITE PAGES STATUS ===\n\n";
    
    $db = Database::getConnection();
    $stmt = $db->query("
        SELECT sp.page_name, sp.page_title, sp.has_hero_section, sp.auto_detected,
               CASE WHEN hs.id IS NOT NULL THEN 'Yes' ELSE 'No' END as hero_exists
        FROM site_pages sp 
        LEFT JOIN hero_sections hs ON sp.page_name = hs.page_name 
        ORDER BY sp.page_name
    ");
    $pages = $stmt->fetchAll();
    
    echo "Page Name       | Page Title           | Has Hero | Auto Detected | Hero Exists\n";
    echo "----------------|---------------------|----------|---------------|------------\n";
    
    foreach ($pages as $page) {
        printf("%-15s | %-19s | %-8s | %-13s | %s\n",
            $page['page_name'],
            substr($page['page_title'], 0, 19),
            $page['has_hero_section'] ? 'Yes' : 'No',
            $page['auto_detected'] ? 'Yes' : 'No',
            $page['hero_exists']
        );
    }
    
    echo "\n=== PAGE DETECTION TEST COMPLETE ===\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>";
?>
