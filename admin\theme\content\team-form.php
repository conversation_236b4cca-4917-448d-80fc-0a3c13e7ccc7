<?php
/**
 * Team Form Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure edit_member variable is available
if (!isset($edit_member)) {
    $edit_member = null;
}
?>

<div class="admin-card">
    <div class="admin-card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas <?php echo $edit_member ? 'fa-edit' : 'fa-plus'; ?> me-2"></i>
            <?php echo $edit_member ? 'Edit Team Member' : 'Add New Team Member'; ?>
        </h5>
        <button type="button" 
                class="btn btn-outline-secondary btn-sm" 
                onclick="<?php echo $edit_member ? 'cancelForm()' : 'toggleForm()'; ?>">
            <i class="fas <?php echo $edit_member ? 'fa-times' : 'fa-plus'; ?> me-1"></i>
            <?php echo $edit_member ? 'Cancel Edit' : 'Add New Member'; ?>
        </button>
    </div>
    
    <div class="admin-card-body">
        <form method="POST" 
              enctype="multipart/form-data" 
              class="team-form show" 
              id="teamForm">
            <input type="hidden" name="MAX_FILE_SIZE" value="<?php echo MAX_FILE_SIZE; ?>">
            <input type="hidden" name="action" value="<?php echo $edit_member ? 'update_team_member' : 'add_team_member'; ?>">
            <?php if ($edit_member): ?>
                <input type="hidden" name="member_id" value="<?php echo $edit_member['id']; ?>">
                <input type="hidden" name="current_photo" value="<?php echo htmlspecialchars($edit_member['photo']); ?>">
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Main Content -->
                    <div class="form-group mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-user me-1"></i>
                            Full Name <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               class="form-control" 
                               required 
                               value="<?php echo $edit_member ? htmlspecialchars($edit_member['name']) : ''; ?>"
                               placeholder="Enter team member's full name">
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="position" class="form-label">
                            <i class="fas fa-briefcase me-1"></i>
                            Position/Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="position" 
                               name="position" 
                               class="form-control" 
                               required 
                               value="<?php echo $edit_member ? htmlspecialchars($edit_member['position']) : ''; ?>"
                               placeholder="e.g., Senior Architect, Project Manager">
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="bio" class="form-label">
                            <i class="fas fa-align-left me-1"></i>
                            Biography
                        </label>
                        <textarea id="bio" 
                                  name="bio" 
                                  class="form-control" 
                                  rows="5"
                                  placeholder="Brief biography, experience, and background..."><?php echo $edit_member ? htmlspecialchars($edit_member['bio']) : ''; ?></textarea>
                        <div class="form-text">
                            Professional background, experience, and achievements
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    Email Address
                                </label>
                                <input type="email" 
                                       id="email" 
                                       name="email" 
                                       class="form-control" 
                                       value="<?php echo $edit_member ? htmlspecialchars($edit_member['email']) : ''; ?>"
                                       placeholder="<EMAIL>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="linkedin_url" class="form-label">
                                    <i class="fab fa-linkedin me-1"></i>
                                    LinkedIn Profile
                                </label>
                                <input type="url" 
                                       id="linkedin_url" 
                                       name="linkedin_url" 
                                       class="form-control" 
                                       value="<?php echo $edit_member ? htmlspecialchars($edit_member['linkedin_url']) : ''; ?>"
                                       placeholder="https://linkedin.com/in/username">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- Sidebar Settings -->
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cog me-1"></i>
                                Settings
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group mb-3">
                                <label for="sort_order" class="form-label">
                                    <i class="fas fa-sort-numeric-up me-1"></i>
                                    Sort Order
                                </label>
                                <input type="number" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       class="form-control" 
                                       min="0" 
                                       value="<?php echo $edit_member ? $edit_member['sort_order'] : '0'; ?>"
                                       placeholder="0">
                                <div class="form-text">Lower numbers appear first</div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="active" 
                                           name="active" 
                                           value="1" 
                                           <?php echo ($edit_member && $edit_member['active']) || !$edit_member ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="active">
                                        <strong>Active</strong>
                                    </label>
                                </div>
                                <div class="form-text">Show this member on the website</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-camera me-1"></i>
                                Profile Photo
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <?php if ($edit_member && $edit_member['photo']): ?>
                                <div class="current-photo mb-3 text-center">
                                    <img src="<?php echo ensureAbsoluteUrl($edit_member['photo']); ?>" 
                                         alt="Current Photo" 
                                         class="img-fluid rounded-circle" 
                                         style="max-width: 150px;">
                                    <small class="text-muted d-block mt-1">Current profile photo</small>
                                </div>
                            <?php endif; ?>
                            <input type="file" 
                                   id="photo" 
                                   name="photo" 
                                   class="form-control" 
                                   accept="image/*">
                            <div class="form-text">
                                Professional headshot photo<br>
                                Recommended: Square image, 400x400px or larger<br>
                                Formats: JPG, PNG, WebP
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2 mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas <?php echo $edit_member ? 'fa-save' : 'fa-plus'; ?> me-1"></i>
                    <?php echo $edit_member ? 'Update Member' : 'Add Member'; ?>
                </button>
                <button type="button" class="btn btn-secondary" onclick="cancelForm()">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.team-form {
    display: none;
}

.team-form.show {
    display: block;
}

.current-photo img {
    max-height: 150px;
    object-fit: cover;
}

.admin-card .admin-card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-text {
    font-size: 0.8rem;
}

.form-check-input:checked {
    background-color: var(--admin-accent-color);
    border-color: var(--admin-accent-color);
}
</style>
