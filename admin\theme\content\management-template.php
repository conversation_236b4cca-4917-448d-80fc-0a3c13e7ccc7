<?php
/**
 * Management Template
 * Template for admin management pages (CRUD operations)
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}
?>

<div class="management-page">
    
    <!-- Management Header -->
    <?php if (isset($management_title)): ?>
        <div class="management-header">
            <div class="management-info">
                <h2 class="management-title"><?php echo htmlspecialchars($management_title); ?></h2>
                <?php if (isset($management_description)): ?>
                    <p class="management-description"><?php echo htmlspecialchars($management_description); ?></p>
                <?php endif; ?>
            </div>
            
            <?php if (isset($management_actions)): ?>
                <div class="management-actions">
                    <?php foreach ($management_actions as $action): ?>
                        <a href="<?php echo htmlspecialchars($action['url']); ?>" 
                           class="btn <?php echo isset($action['class']) ? $action['class'] : 'btn-primary'; ?>"
                           <?php if (isset($action['data_attributes'])): ?>
                               <?php foreach ($action['data_attributes'] as $attr => $value): ?>
                                   data-<?php echo $attr; ?>="<?php echo htmlspecialchars($value); ?>"
                               <?php endforeach; ?>
                           <?php endif; ?>>
                            <?php if (isset($action['icon'])): ?>
                                <i class="<?php echo htmlspecialchars($action['icon']); ?>"></i>
                            <?php endif; ?>
                            <?php echo htmlspecialchars($action['label']); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <?php if (isset($show_stats) && $show_stats && isset($stats_cards)): ?>
        <div class="stats-section">
            <div class="row">
                <?php foreach ($stats_cards as $stat): ?>
                    <div class="col-md-3 col-sm-6">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="<?php echo htmlspecialchars($stat['icon']); ?>"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number"><?php echo htmlspecialchars($stat['number']); ?></div>
                                <div class="stat-label"><?php echo htmlspecialchars($stat['label']); ?></div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Search and Filters -->
    <?php if (isset($show_search) && $show_search): ?>
        <div class="admin-card">
            <div class="admin-card-body">
                <div class="search-filters">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="search-input">Search</label>
                                <input type="text" 
                                       id="search-input" 
                                       class="form-control" 
                                       placeholder="<?php echo isset($search_placeholder) ? htmlspecialchars($search_placeholder) : 'Search...'; ?>"
                                       data-search-target="<?php echo isset($search_target) ? htmlspecialchars($search_target) : '.searchable-item'; ?>">
                            </div>
                        </div>
                        
                        <?php if (isset($show_filters) && $show_filters): ?>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="filter-select">Filter</label>
                                    <select id="filter-select" class="form-control">
                                        <option value="">All Items</option>
                                        <?php if (isset($filter_options)): ?>
                                            <?php foreach ($filter_options as $value => $label): ?>
                                                <option value="<?php echo htmlspecialchars($value); ?>">
                                                    <?php echo htmlspecialchars($label); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Add/Edit Form -->
    <?php if (isset($show_form) && $show_form): ?>
        <div class="admin-card">
            <div class="admin-card-header">
                <?php echo isset($form_title) ? htmlspecialchars($form_title) : 'Add New Item'; ?>
            </div>
            <div class="admin-card-body">
                <?php
                if (isset($form_content_file) && file_exists($form_content_file)) {
                    include $form_content_file;
                } elseif (isset($form_content)) {
                    echo $form_content;
                }
                ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Custom Content Before Table -->
    <?php if (isset($custom_content_before_table)): ?>
        <div class="custom-content-before-table">
            <?php
            if (is_callable($custom_content_before_table)) {
                call_user_func($custom_content_before_table);
            } else {
                echo $custom_content_before_table;
            }
            ?>
        </div>
    <?php endif; ?>

    <!-- Data Table -->
    <?php if (isset($show_table) && $show_table): ?>
        <div class="admin-table">
            <?php if (isset($table_title)): ?>
                <div class="table-header">
                    <h3 class="table-title"><?php echo htmlspecialchars($table_title); ?></h3>
                    <?php if (isset($table_actions)): ?>
                        <div class="table-actions">
                            <?php foreach ($table_actions as $action): ?>
                                <button type="button" 
                                        class="btn btn-sm <?php echo isset($action['class']) ? $action['class'] : 'btn-secondary'; ?>"
                                        onclick="<?php echo htmlspecialchars($action['onclick']); ?>">
                                    <?php if (isset($action['icon'])): ?>
                                        <i class="<?php echo htmlspecialchars($action['icon']); ?>"></i>
                                    <?php endif; ?>
                                    <?php echo htmlspecialchars($action['label']); ?>
                                </button>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <div class="table-content">
                <?php
                if (isset($table_content_file) && file_exists($table_content_file)) {
                    include $table_content_file;
                } elseif (isset($table_content)) {
                    echo $table_content;
                } else {
                    echo '<div class="alert alert-info">No table content specified.</div>';
                }
                ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Custom Content -->
    <?php if (isset($custom_content)): ?>
        <div class="custom-content">
            <?php echo $custom_content; ?>
        </div>
    <?php endif; ?>
    
</div>

<style>
/* Management Template Specific Styles */
.management-page {
    margin-bottom: var(--admin-spacing-xl);
}

.management-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--admin-spacing-lg);
    padding: var(--admin-spacing-lg);
    background: white;
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-shadow-soft);
}

.management-title {
    font-family: var(--admin-font-secondary);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--admin-primary-color);
    margin: 0;
}

.management-description {
    color: #6c757d;
    margin: 0.5rem 0 0 0;
    font-size: 1rem;
}

.management-actions {
    display: flex;
    gap: var(--admin-spacing-sm);
}

.search-filters {
    background: #f8f9fa;
    padding: var(--admin-spacing-md);
    border-radius: var(--admin-border-radius);
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--admin-spacing-md) var(--admin-spacing-lg);
    background: var(--admin-primary-color);
    color: white;
}

.table-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: var(--admin-spacing-xs);
}

.table-content {
    background: white;
}

.stat-card {
    background: white;
    padding: var(--admin-spacing-lg);
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-shadow-soft);
    display: flex;
    align-items: center;
    gap: var(--admin-spacing-md);
    margin-bottom: var(--admin-spacing-md);
    transition: var(--admin-transition-medium);
}

.stat-card:hover {
    box-shadow: var(--admin-shadow-medium);
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--admin-accent-color), #d35400);
    border-radius: var(--admin-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-primary-color);
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

@media (max-width: 767.98px) {
    .management-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--admin-spacing-md);
    }
    
    .management-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--admin-spacing-sm);
    }
}
</style>
