<?php
/**
 * Hero Sections Form Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure edit_hero variable is available
if (!isset($edit_hero)) {
    $edit_hero = null;
}
?>

<?php if ($edit_hero): ?>
<div class="admin-card">
    <div class="admin-card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-edit me-2"></i>
            Edit Hero Section: <?php echo htmlspecialchars($edit_hero['page_title']); ?>
        </h5>
        <a href="hero-sections.php" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left me-1"></i>
            Back to List
        </a>
    </div>
    
    <div class="admin-card-body">
        <form method="POST" enctype="multipart/form-data">
            <input type="hidden" name="action" value="update_hero">
            <input type="hidden" name="hero_id" value="<?php echo $edit_hero['id']; ?>">
            <input type="hidden" name="current_background_image" value="<?php echo htmlspecialchars($edit_hero['background_image']); ?>">
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Content Section -->
                    <h6 class="mb-3">
                        <i class="fas fa-align-left me-1"></i>
                        Content
                    </h6>
                    
                    <div class="form-group mb-3">
                        <label for="caption" class="form-label">Caption Text</label>
                        <input type="text" 
                               id="caption" 
                               name="caption" 
                               class="form-control" 
                               value="<?php echo htmlspecialchars($edit_hero['caption']); ?>" 
                               placeholder="Ready to Build?">
                        <div class="form-text">Small text above the main title</div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="title" class="form-label">
                            Main Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               class="form-control" 
                               value="<?php echo htmlspecialchars($edit_hero['title']); ?>" 
                               required 
                               placeholder="Ready to Get Started?">
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="description" class="form-label">Description Text</label>
                        <textarea id="description" 
                                  name="description" 
                                  class="form-control" 
                                  rows="3" 
                                  placeholder="Let's transform your vision into reality..."><?php echo htmlspecialchars($edit_hero['description']); ?></textarea>
                    </div>
                    
                    <!-- Button/Newsletter Section -->
                    <h6 class="mb-3">
                        <i class="fas fa-mouse-pointer me-1"></i>
                        Call to Action
                    </h6>
                    
                    <div class="form-group mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="show_newsletter_input" 
                                   name="show_newsletter_input" 
                                   <?php echo ($edit_hero['show_newsletter_input'] ?? 0) ? 'checked' : ''; ?> 
                                   onchange="toggleNewsletterFields()">
                            <label class="form-check-label" for="show_newsletter_input">
                                <strong>Enable Email Input (Newsletter Signup)</strong>
                            </label>
                        </div>
                        <div class="form-text">Check this to show an email input field instead of a regular button</div>
                    </div>
                    
                    <div id="button-fields" style="display: <?php echo ($edit_hero['show_newsletter_input'] ?? 0) ? 'none' : 'block'; ?>;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="button_text" class="form-label">Button Text</label>
                                    <input type="text" 
                                           id="button_text" 
                                           name="button_text" 
                                           class="form-control" 
                                           value="<?php echo htmlspecialchars($edit_hero['button_text']); ?>" 
                                           placeholder="Start Your Project">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="button_link" class="form-label">Button Link</label>
                                    <select id="button_link" name="button_link" class="form-control" onchange="toggleCustomLinkInput()">
                                        <option value="">Select a page...</option>
                                        <?php
                                        // Get all pages from site_pages table
                                        $db = Database::getConnection();
                                        $pages_query = "SELECT page_name, page_title FROM site_pages ORDER BY page_title";
                                        $pages_result = $db->query($pages_query);
                                        $current_link = $edit_hero['button_link'] ?? '';
                                        $is_custom_link = true;

                                        if ($pages_result) {
                                            while ($page = $pages_result->fetch(PDO::FETCH_ASSOC)) {
                                                $selected = ($current_link === $page['page_name']) ? 'selected' : '';
                                                if ($selected) $is_custom_link = false;
                                                echo '<option value="' . htmlspecialchars($page['page_name']) . '" ' . $selected . '>' . htmlspecialchars($page['page_title']) . '</option>';
                                            }
                                        }
                                        ?>
                                        <option value="custom" <?php echo $is_custom_link && !empty($current_link) ? 'selected' : ''; ?>>Other/Custom URL</option>
                                    </select>
                                    <input type="text" 
                                           id="custom_button_link" 
                                           name="custom_button_link"
                                           class="form-control mt-2"
                                           value="<?php echo $is_custom_link ? htmlspecialchars($current_link) : ''; ?>"
                                           placeholder="Enter custom URL or page slug"
                                           style="<?php echo $is_custom_link && !empty($current_link) ? '' : 'display: none;'; ?>">
                                    <div class="form-text">Select a page or choose "Other/Custom URL" for external links</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="newsletter-fields" style="display: <?php echo ($edit_hero['show_newsletter_input'] ?? 0) ? 'block' : 'none'; ?>;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="newsletter_placeholder" class="form-label">Email Input Placeholder</label>
                                    <input type="text" 
                                           id="newsletter_placeholder" 
                                           name="newsletter_placeholder" 
                                           class="form-control"
                                           value="<?php echo htmlspecialchars($edit_hero['newsletter_placeholder'] ?? 'Enter your email address'); ?>" 
                                           placeholder="Enter your email address">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="newsletter_button_text" class="form-label">Subscribe Button Text</label>
                                    <input type="text" 
                                           id="newsletter_button_text" 
                                           name="newsletter_button_text" 
                                           class="form-control"
                                           value="<?php echo htmlspecialchars($edit_hero['newsletter_button_text'] ?? 'Subscribe'); ?>" 
                                           placeholder="Subscribe">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="newsletter_success_message" class="form-label">Success Message</label>
                            <input type="text" 
                                   id="newsletter_success_message" 
                                   name="newsletter_success_message" 
                                   class="form-control"
                                   value="<?php echo htmlspecialchars($edit_hero['newsletter_success_message'] ?? 'Thank you for subscribing!'); ?>" 
                                   placeholder="Thank you for subscribing!">
                            <div class="form-text">Message shown after successful subscription</div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- Background Section -->
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-image me-1"></i>
                                Background
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group mb-3">
                                <label class="form-label">Background Type</label>
                                <div class="background-type-toggle">
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="radio" 
                                               name="background_type" 
                                               value="gradient" 
                                               id="bg_gradient"
                                               <?php echo $edit_hero['background_type'] === 'gradient' ? 'checked' : ''; ?> 
                                               onchange="toggleBackgroundType()">
                                        <label class="form-check-label" for="bg_gradient">
                                            Gradient Background
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="radio" 
                                               name="background_type" 
                                               value="image" 
                                               id="bg_image"
                                               <?php echo $edit_hero['background_type'] === 'image' ? 'checked' : ''; ?> 
                                               onchange="toggleBackgroundType()">
                                        <label class="form-check-label" for="bg_image">
                                            Image Background
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Gradient Options -->
                            <div id="gradient-options" style="display: <?php echo $edit_hero['background_type'] === 'gradient' ? 'block' : 'none'; ?>;">
                                <div class="form-group mb-3">
                                    <label for="background_gradient" class="form-label">Background Gradient</label>
                                    <input type="text" 
                                           id="background_gradient" 
                                           name="background_gradient" 
                                           class="form-control" 
                                           value="<?php echo htmlspecialchars($edit_hero['background_gradient']); ?>" 
                                           placeholder="linear-gradient(...)">
                                    <div class="form-text">Choose from presets below or enter custom CSS gradient</div>
                                </div>
                                
                                <div class="gradient-presets">
                                    <div class="gradient-preset" 
                                         style="background: linear-gradient(135deg, rgba(0, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%);" 
                                         onclick="selectGradient('linear-gradient(135deg, rgba(0, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)')">
                                        Professional Dark
                                    </div>
                                    <div class="gradient-preset" 
                                         style="background: linear-gradient(135deg, rgba(230, 126, 34, 0.85) 0%, rgba(211, 84, 0, 0.65) 100%);" 
                                         onclick="selectGradient('linear-gradient(135deg, rgba(230, 126, 34, 0.85) 0%, rgba(211, 84, 0, 0.65) 100%)')">
                                        Warm Orange
                                    </div>
                                    <div class="gradient-preset" 
                                         style="background: linear-gradient(135deg, rgba(44, 62, 80, 0.9) 0%, rgba(52, 73, 94, 0.75) 100%);" 
                                         onclick="selectGradient('linear-gradient(135deg, rgba(44, 62, 80, 0.9) 0%, rgba(52, 73, 94, 0.75) 100%)')">
                                        Corporate Blue
                                    </div>
                                    <div class="gradient-preset" 
                                         style="background: linear-gradient(135deg, rgba(46, 204, 113, 0.8) 0%, rgba(39, 174, 96, 0.6) 100%);" 
                                         onclick="selectGradient('linear-gradient(135deg, rgba(46, 204, 113, 0.8) 0%, rgba(39, 174, 96, 0.6) 100%)')">
                                        Modern Green
                                    </div>
                                    <?php 
                                    // Get current accent color and convert to rgba for gradient
                                    $accent_color = getThemeOption('accent_color', '#E67E22');
                                    // Convert hex to RGB
                                    $hex = ltrim($accent_color, '#');
                                    $r = hexdec(substr($hex, 0, 2));
                                    $g = hexdec(substr($hex, 2, 2));
                                    $b = hexdec(substr($hex, 4, 2));
                                    $gradient_strong = "linear-gradient(135deg, rgba($r, $g, $b, 0.9) 0%, rgba($r, $g, $b, 0.6) 100%)";
                                    ?>
                                    <div class="gradient-preset" 
                                         style="background: <?php echo $gradient_strong; ?>;" 
                                         onclick="selectGradient('<?php echo $gradient_strong; ?>')">
                                        Theme Accent
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Image Options -->
                            <div id="image-options" style="display: <?php echo $edit_hero['background_type'] === 'image' ? 'block' : 'none'; ?>;">
                                <div class="form-group mb-3">
                                    <label for="background_image" class="form-label">Background Image</label>
                                    <input type="file" 
                                           id="background_image" 
                                           name="background_image" 
                                           class="form-control" 
                                           accept="image/*">
                                    <?php if ($edit_hero['background_image']): ?>
                                        <div class="current-image mt-2">
                                            <img src="<?php echo ensureAbsoluteUrl($edit_hero['background_image']); ?>" 
                                                 alt="Current Background" 
                                                 class="img-fluid rounded" 
                                                 style="max-width: 200px;">
                                            <small class="text-muted d-block mt-1">Current background image</small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Height Controls -->
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-arrows-alt-v me-1"></i>
                                Height
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group mb-3">
                                <label for="height_type" class="form-label">Hero Section Height</label>
                                <select name="height_type" id="height_type" class="form-control" onchange="toggleCustomHeight()">
                                    <option value="small" <?php echo ($edit_hero['height_type'] ?? 'medium') === 'small' ? 'selected' : ''; ?>>Small (300px)</option>
                                    <option value="medium" <?php echo ($edit_hero['height_type'] ?? 'medium') === 'medium' ? 'selected' : ''; ?>>Medium (400px)</option>
                                    <option value="large" <?php echo ($edit_hero['height_type'] ?? 'medium') === 'large' ? 'selected' : ''; ?>>Large (600px)</option>
                                    <option value="custom" <?php echo ($edit_hero['height_type'] ?? 'medium') === 'custom' ? 'selected' : ''; ?>>Custom</option>
                                </select>
                            </div>
                            <div class="form-group" id="custom-height-group" style="display: <?php echo ($edit_hero['height_type'] ?? 'medium') === 'custom' ? 'block' : 'none'; ?>;">
                                <label for="height_custom" class="form-label">Custom Height (px)</label>
                                <input type="number"
                                       name="height_custom"
                                       id="height_custom"
                                       class="form-control"
                                       value="<?php echo $edit_hero['height_custom'] ?? 400; ?>"
                                       placeholder="400"
                                       min="200"
                                       max="1000">
                                <div class="form-text">Height in pixels (200-1000)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Color Controls -->
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-palette me-1"></i>
                                Text Colors
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="caption_color" class="form-label">Caption Color</label>
                                        <div class="color-input-group">
                                            <input type="color"
                                                   id="caption_color"
                                                   name="caption_color"
                                                   class="form-control form-control-color"
                                                   value="<?php echo htmlspecialchars($edit_hero['caption_color'] ?? '#ffffff'); ?>"
                                                   title="Choose caption color">
                                            <input type="text"
                                                   class="form-control color-text-input"
                                                   value="<?php echo htmlspecialchars($edit_hero['caption_color'] ?? '#ffffff'); ?>"
                                                   readonly>
                                        </div>
                                        <div class="form-text">Color for the small caption text</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="title_color" class="form-label">Title Color</label>
                                        <div class="color-input-group">
                                            <input type="color"
                                                   id="title_color"
                                                   name="title_color"
                                                   class="form-control form-control-color"
                                                   value="<?php echo htmlspecialchars($edit_hero['title_color'] ?? '#ffffff'); ?>"
                                                   title="Choose title color">
                                            <input type="text"
                                                   class="form-control color-text-input"
                                                   value="<?php echo htmlspecialchars($edit_hero['title_color'] ?? '#ffffff'); ?>"
                                                   readonly>
                                        </div>
                                        <div class="form-text">Color for the main title</div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mb-3">
                                <label for="description_color" class="form-label">Description Color</label>
                                <div class="color-input-group">
                                    <input type="color"
                                           id="description_color"
                                           name="description_color"
                                           class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($edit_hero['description_color'] ?? '#ffffff'); ?>"
                                           title="Choose description color">
                                    <input type="text"
                                           class="form-control color-text-input"
                                           value="<?php echo htmlspecialchars($edit_hero['description_color'] ?? '#ffffff'); ?>"
                                           readonly>
                                </div>
                                <div class="form-text">Color for the description text</div>
                            </div>
                        </div>
                    </div>

                    <!-- Button Colors -->
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-mouse-pointer me-1"></i>
                                Button Colors
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="button_bg_color" class="form-label">Button Background</label>
                                        <div class="color-input-group">
                                            <input type="color"
                                                   id="button_bg_color"
                                                   name="button_bg_color"
                                                   class="form-control form-control-color"
                                                   value="<?php echo htmlspecialchars($edit_hero['button_bg_color'] ?? '#E67E22'); ?>"
                                                   title="Choose button background color">
                                            <input type="text"
                                                   class="form-control color-text-input"
                                                   value="<?php echo htmlspecialchars($edit_hero['button_bg_color'] ?? '#E67E22'); ?>"
                                                   readonly>
                                        </div>
                                        <div class="form-text">Button background color</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="button_text_color" class="form-label">Button Text Color</label>
                                        <div class="color-input-group">
                                            <input type="color"
                                                   id="button_text_color"
                                                   name="button_text_color"
                                                   class="form-control form-control-color"
                                                   value="<?php echo htmlspecialchars($edit_hero['button_text_color'] ?? '#ffffff'); ?>"
                                                   title="Choose button text color">
                                            <input type="text"
                                                   class="form-control color-text-input"
                                                   value="<?php echo htmlspecialchars($edit_hero['button_text_color'] ?? '#ffffff'); ?>"
                                                   readonly>
                                        </div>
                                        <div class="form-text">Button text color</div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mb-3">
                                <label for="button_hover_bg_color" class="form-label">Button Hover Background</label>
                                <div class="color-input-group">
                                    <input type="color"
                                           id="button_hover_bg_color"
                                           name="button_hover_bg_color"
                                           class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($edit_hero['button_hover_bg_color'] ?? '#d35400'); ?>"
                                           title="Choose button hover background color">
                                    <input type="text"
                                           class="form-control color-text-input"
                                           value="<?php echo htmlspecialchars($edit_hero['button_hover_bg_color'] ?? '#d35400'); ?>"
                                           readonly>
                                </div>
                                <div class="form-text">Button background color on hover</div>
                            </div>
                        </div>
                    </div>

                    <!-- Background Overlay -->
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-layer-group me-1"></i>
                                Background Overlay
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group mb-3">
                                        <label for="background_color" class="form-label">Overlay Color</label>
                                        <div class="color-input-group">
                                            <input type="color"
                                                   id="background_color"
                                                   name="background_color"
                                                   class="form-control form-control-color"
                                                   value="<?php echo htmlspecialchars($edit_hero['background_color'] ?? '#000000'); ?>"
                                                   title="Choose overlay color">
                                            <input type="text"
                                                   class="form-control color-text-input"
                                                   value="<?php echo htmlspecialchars($edit_hero['background_color'] ?? '#000000'); ?>"
                                                   readonly>
                                        </div>
                                        <div class="form-text">Background overlay color (for image backgrounds)</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="background_opacity" class="form-label">Overlay Opacity</label>
                                        <input type="range"
                                               id="background_opacity"
                                               name="background_opacity"
                                               class="form-range"
                                               min="0"
                                               max="1"
                                               step="0.1"
                                               value="<?php echo $edit_hero['background_opacity'] ?? 0.6; ?>"
                                               oninput="updateOpacityValue(this.value)">
                                        <div class="d-flex justify-content-between">
                                            <small>0%</small>
                                            <small id="opacity-value"><?php echo round(($edit_hero['background_opacity'] ?? 0.6) * 100); ?>%</small>
                                            <small>100%</small>
                                        </div>
                                        <div class="form-text">Overlay transparency level</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2 mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    Update Hero Section
                </button>
                <a href="hero-sections.php" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>

<style>
.background-type-toggle .form-check {
    margin-bottom: 0.5rem;
}

.gradient-presets {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-top: 1rem;
}

.gradient-preset {
    height: 40px;
    border-radius: 6px;
    border: 2px solid transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    transition: all 0.3s ease;
}

.gradient-preset:hover {
    border-color: var(--admin-accent-color);
    transform: translateY(-1px);
}

.gradient-preset.selected {
    border-color: var(--admin-accent-color);
    box-shadow: 0 0 0 2px rgba(230, 126, 34, 0.2);
}

.current-image img {
    max-height: 120px;
    object-fit: cover;
}

.admin-card .admin-card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-text {
    font-size: 0.8rem;
}

.form-check-input:checked {
    background-color: var(--admin-accent-color);
    border-color: var(--admin-accent-color);
}

.color-input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.form-control-color {
    width: 60px;
    height: 38px;
    border-radius: 6px;
    border: 1px solid #ced4da;
    cursor: pointer;
}

.color-text-input {
    flex: 1;
    font-family: monospace;
    font-size: 0.9rem;
    background-color: #f8f9fa;
}

.form-range {
    width: 100%;
}

#opacity-value {
    font-weight: 600;
    color: var(--admin-accent-color);
}
</style>

<script>
function toggleNewsletterFields() {
    const checkbox = document.getElementById('show_newsletter_input');
    const buttonFields = document.getElementById('button-fields');
    const newsletterFields = document.getElementById('newsletter-fields');

    if (checkbox.checked) {
        buttonFields.style.display = 'none';
        newsletterFields.style.display = 'block';
    } else {
        buttonFields.style.display = 'block';
        newsletterFields.style.display = 'none';
    }
}

function toggleCustomLinkInput() {
    const select = document.getElementById('button_link');
    const customInput = document.getElementById('custom_button_link');

    if (select.value === 'custom') {
        customInput.style.display = 'block';
        customInput.focus();
    } else {
        customInput.style.display = 'none';
        customInput.value = '';
    }
}

function toggleBackgroundType() {
    const gradientRadio = document.getElementById('bg_gradient');
    const gradientOptions = document.getElementById('gradient-options');
    const imageOptions = document.getElementById('image-options');

    if (gradientRadio.checked) {
        gradientOptions.style.display = 'block';
        imageOptions.style.display = 'none';
    } else {
        gradientOptions.style.display = 'none';
        imageOptions.style.display = 'block';
    }
}

function selectGradient(gradient) {
    document.getElementById('background_gradient').value = gradient;

    // Remove selected class from all presets
    document.querySelectorAll('.gradient-preset').forEach(preset => {
        preset.classList.remove('selected');
    });

    // Add selected class to clicked preset
    event.target.classList.add('selected');
}

function toggleCustomHeight() {
    const select = document.getElementById('height_type');
    const customGroup = document.getElementById('custom-height-group');

    if (select.value === 'custom') {
        customGroup.style.display = 'block';
        document.getElementById('height_custom').focus();
    } else {
        customGroup.style.display = 'none';
    }
}

function updateOpacityValue(value) {
    document.getElementById('opacity-value').textContent = Math.round(value * 100) + '%';
}

function syncColorInputs() {
    // Sync color picker with text input for all color fields
    const colorFields = ['caption_color', 'title_color', 'description_color', 'button_bg_color', 'button_text_color', 'button_hover_bg_color', 'background_color'];

    colorFields.forEach(fieldName => {
        const colorInput = document.getElementById(fieldName);
        const textInput = colorInput.nextElementSibling;

        if (colorInput && textInput) {
            colorInput.addEventListener('input', function() {
                textInput.value = this.value.toUpperCase();
            });
        }
    });
}

// Initialize form state on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleNewsletterFields();
    toggleCustomLinkInput();
    toggleBackgroundType();
    toggleCustomHeight();
    syncColorInputs();

    // Highlight current gradient preset if it matches
    const currentGradient = document.getElementById('background_gradient').value;
    document.querySelectorAll('.gradient-preset').forEach(preset => {
        const presetGradient = preset.getAttribute('onclick').match(/'([^']+)'/)[1];
        if (presetGradient === currentGradient) {
            preset.classList.add('selected');
        }
    });
});
</script>
<?php endif; ?>
