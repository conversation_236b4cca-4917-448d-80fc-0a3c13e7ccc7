# Monolith Design Hero System Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Technical Architecture](#technical-architecture)
3. [File Structure](#file-structure)
4. [Database Schema](#database-schema)
5. [Newsletter Integration](#newsletter-integration)
6. [Styling Architecture](#styling-architecture)
7. [Admin Panel Integration](#admin-panel-integration)
8. [Implementation Guide](#implementation-guide)
9. [Troubleshooting](#troubleshooting)

## System Overview

The Monolith Design Hero System is a comprehensive, database-driven solution for managing hero sections across the website. It provides two distinct types of hero sections:

### Hero Section Types
1. **Footer Hero CTA Sections** - Call-to-action sections that appear before the footer
2. **Header Hero Sections** - Page header sections with titles, breadcrumbs, and backgrounds

### Key Features
- **Database-driven content management** via admin panel
- **Newsletter signup integration** with email collection
- **Flexible background system** (images, gradients, solid colors)
- **Customizable overlay system** with color and opacity controls
- **Responsive glassmorphism design** copied from blog page
- **Dynamic content switching** (button vs newsletter form)
- **Multi-page support** with page-specific configurations

## Technical Architecture

### Core Components
```
Hero System
├── Database Layer (hero_sections, newsletter_subscribers)
├── Template Layer (hero-cta.php, newsletter-hero.php)
├── Admin Interface (Hero Sections management)
├── JavaScript Layer (AJAX form handling)
└── Styling Layer (Glassmorphism CSS)
```

### Data Flow
1. **Admin Input** → Database storage (`hero_sections` table)
2. **Page Load** → Template queries database for page-specific hero data
3. **Template Rendering** → Dynamic content based on database settings
4. **User Interaction** → Newsletter signup via AJAX to backend handler
5. **Email Storage** → Newsletter emails stored in `newsletter_subscribers` table

### Page Detection System
```php
// Automatic page detection
$hero_page_name = 'contact'; // Set in page files
$current_page = isset($hero_page_name) ? $hero_page_name : 'home';
$db_hero = getHeroSection($current_page);
```

## File Structure

### Core Template Files
```
templates/
├── hero-cta.php              # Footer hero CTA sections
├── newsletter-hero.php       # Blog page newsletter hero
└── (future) hero-header.php  # Page header hero sections

admin/
├── newsletter-signup.php     # Newsletter form handler
└── (admin interface files)   # Hero management panels

includes/
├── functions.php             # getHeroSection() function
└── hero-page-detection.php   # Page detection utilities
```

### Template Relationships
- **hero-cta.php**: Used by contact.php and other pages for footer CTAs
- **newsletter-hero.php**: Used by news.php for blog newsletter signup
- **Page files**: Set `$hero_page_name` variable before loading templates

### Integration Pattern
```php
// In page files (e.g., contact.php)
<?php 
loadTemplate('hero-cta', ['hero_page_name' => 'contact']); 
?>
```

## Database Schema

### hero_sections Table
```sql
CREATE TABLE hero_sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_name VARCHAR(100) NOT NULL,
    title VARCHAR(255),
    caption VARCHAR(255),
    description TEXT,
    button_text VARCHAR(100),
    button_link VARCHAR(255),
    background_type ENUM('image', 'gradient', 'color') DEFAULT 'gradient',
    background_image VARCHAR(255),
    background_gradient TEXT,
    background_color VARCHAR(7) DEFAULT '#a99eff',
    background_opacity DECIMAL(3,2) DEFAULT 0.60,
    height_type ENUM('small', 'medium', 'large', 'custom') DEFAULT 'medium',
    height_custom INT,
    caption_color VARCHAR(7) DEFAULT '#ffffff',
    title_color VARCHAR(7) DEFAULT '#ffffff',
    description_color VARCHAR(7) DEFAULT '#ffffff',
    show_newsletter_input BOOLEAN DEFAULT FALSE,
    newsletter_placeholder VARCHAR(255) DEFAULT 'Enter your email address',
    newsletter_button_text VARCHAR(100) DEFAULT 'Subscribe',
    newsletter_success_message VARCHAR(255) DEFAULT 'Thank you for subscribing!',
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### newsletter_subscribers Table
```sql
CREATE TABLE newsletter_subscribers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    source VARCHAR(100) DEFAULT 'unknown',
    page VARCHAR(100) DEFAULT 'unknown',
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'unsubscribed', 'bounced') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Key Database Fields

#### Background System
- `background_type`: Controls background rendering logic
- `background_color`: Used as overlay color for images, base color for gradients
- `background_opacity`: Controls overlay transparency
- `background_gradient`: Custom CSS gradient string
- `background_image`: Image URL for background

#### Newsletter System
- `show_newsletter_input`: Boolean toggle for newsletter vs button display
- `newsletter_placeholder`: Custom email input placeholder
- `newsletter_button_text`: Custom subscribe button text
- `newsletter_success_message`: Custom success message

#### Styling System
- `caption_color`, `title_color`, `description_color`: Text color controls
- `height_type`: Predefined height options
- `height_custom`: Custom height in pixels

## Newsletter Integration

### Frontend Form (hero-cta.php)
```html
<form class="hero-newsletter-form" id="heroNewsletterForm" method="POST" action="/admin/newsletter-signup.php">
    <div class="newsletter-input-group">
        <input type="email" name="email" class="newsletter-hero-input" placeholder="Enter your email address" required>
        <button type="submit" class="newsletter-hero-button">
            Subscribe
            <svg><!-- Arrow icon --></svg>
        </button>
    </div>
    <input type="hidden" name="source" value="hero_cta">
    <input type="hidden" name="page" value="contact">
</form>
```

### Backend Handler (newsletter-signup.php)
```php
// AJAX-enabled newsletter signup
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_var($_POST['email'] ?? '', FILTER_VALIDATE_EMAIL);
    $source = $_POST['source'] ?? 'unknown';
    $page = $_POST['page'] ?? 'unknown';
    
    // Store in database with duplicate checking
    // Return JSON response for AJAX handling
}
```

### JavaScript Integration
```javascript
// AJAX form submission with success/error handling
fetch(form.action, {
    method: 'POST',
    body: formData,
    headers: { 'X-Requested-With': 'XMLHttpRequest' }
})
.then(response => response.json())
.then(data => {
    // Handle success/error states
    // Show success message and reset form
});
```

## Styling Architecture

### Glassmorphism Design
The hero system uses a modern glassmorphism design copied from the blog page newsletter section:

#### Key Visual Elements
```css
.newsletter-input-group {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}
```

#### Design Features
- **Rounded corners** (50px border-radius)
- **Backdrop blur effect** for glassmorphism
- **Transparent backgrounds** with subtle borders
- **Smooth hover animations** with transform effects
- **Responsive design** that stacks on mobile

### Background Overlay System
```php
// Smart overlay logic based on background type
if ($background_type === 'image') {
    // Apply overlay on top of image
    $overlay_style = "background: rgba($r, $g, $b, $opacity);";
} else {
    // Use overlay color as base background
    $background_style = "background: rgba($r, $g, $b, $opacity);";
}
```

### Responsive Behavior
```css
@media (max-width: 768px) {
    .newsletter-input-group {
        flex-direction: column;
        border-radius: 12px;
        gap: 8px;
    }
}
```

## Admin Panel Integration

### Hero Sections Management
The admin panel provides comprehensive control over hero sections:

#### Content Controls
- **Caption Text**: Small text above main title
- **Main Title**: Primary heading text
- **Description**: Subtitle/description paragraph
- **Call to Action**: Button text and link configuration

#### Background Controls
- **Background Type**: Image, Gradient, or Color
- **Background Image**: Upload/select background image
- **Background Gradient**: Custom CSS gradient picker
- **Overlay Color**: Color picker for overlay (#a99eff default)
- **Overlay Opacity**: Slider for transparency control

#### Newsletter Controls
- **Enable Newsletter**: Toggle between button and email form
- **Email Placeholder**: Custom placeholder text
- **Subscribe Button Text**: Custom button text
- **Success Message**: Custom confirmation message

#### Styling Controls
- **Height Settings**: Small/Medium/Large/Custom options
- **Text Colors**: Individual color pickers for each text element
- **Active Status**: Enable/disable hero section

### Database Integration
```php
function getHeroSection($page_name) {
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT * FROM hero_sections WHERE page_name = ? AND active = 1");
    $stmt->execute([$page_name]);
    return $stmt->fetch();
}
```

## Implementation Guide

### Adding Hero Section to New Page

1. **Set Page Name Variable**
```php
// In your page file (e.g., services.php)
<?php 
loadTemplate('hero-cta', ['hero_page_name' => 'services']); 
?>
```

2. **Create Database Entry**
```sql
INSERT INTO hero_sections (page_name, title, description, active) 
VALUES ('services', 'Our Services', 'Professional architectural solutions', 1);
```

3. **Configure in Admin Panel**
- Navigate to Hero Sections admin
- Edit the services page hero
- Configure content, background, and styling

### Customizing Newsletter Integration

1. **Enable Newsletter Mode**
```php
// In admin panel or database
UPDATE hero_sections SET show_newsletter_input = 1 WHERE page_name = 'contact';
```

2. **Customize Messages**
```php
UPDATE hero_sections SET 
    newsletter_placeholder = 'Your custom placeholder',
    newsletter_button_text = 'Join Now',
    newsletter_success_message = 'Welcome to our newsletter!'
WHERE page_name = 'contact';
```

### Background Configuration

1. **Image Background**
```php
UPDATE hero_sections SET 
    background_type = 'image',
    background_image = '/uploads/hero-bg.jpg',
    background_color = '#a99eff',
    background_opacity = 0.7
WHERE page_name = 'contact';
```

2. **Gradient Background**
```php
UPDATE hero_sections SET 
    background_type = 'gradient',
    background_gradient = 'linear-gradient(135deg, rgba(55, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)'
WHERE page_name = 'contact';
```

## Troubleshooting

### Common Issues

#### 1. Hero Section Not Displaying
**Symptoms**: Hero section doesn't appear on page
**Causes**:
- `$hero_page_name` not set in page file
- Database entry missing or inactive
- Template not loaded correctly

**Solutions**:
```php
// Check page name variable
loadTemplate('hero-cta', ['hero_page_name' => 'your_page_name']);

// Verify database entry
SELECT * FROM hero_sections WHERE page_name = 'your_page_name' AND active = 1;

// Check template loading
if (!file_exists('templates/hero-cta.php')) {
    echo "Template file missing";
}
```

#### 2. Newsletter Signup Not Working
**Symptoms**: "Invalid request" error or form not submitting
**Causes**:
- AJAX request failing
- Database connection issues
- Missing POST data

**Solutions**:
```php
// Check database connection
try {
    $db = Database::getConnection();
    echo "Database connected";
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage();
}

// Verify POST data
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    var_dump($_POST); // Debug POST data
}

// Check AJAX headers
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH'])) {
    echo "AJAX request detected";
}
```

#### 3. Background Overlay Not Working
**Symptoms**: Overlay color not applying correctly
**Causes**:
- Incorrect background type detection
- Invalid color values
- CSS conflicts

**Solutions**:
```php
// Debug background settings
$hero = getHeroSection('page_name');
echo "Background type: " . $hero['background_type'];
echo "Background color: " . $hero['background_color'];
echo "Background opacity: " . $hero['background_opacity'];

// Validate color format
if (!preg_match('/^#[a-f0-9]{6}$/i', $hero['background_color'])) {
    echo "Invalid color format";
}
```

#### 4. Styling Issues
**Symptoms**: Form doesn't look like blog page design
**Causes**:
- CSS not loading
- Class name conflicts
- Missing backdrop-filter support

**Solutions**:
```css
/* Check browser support */
@supports (backdrop-filter: blur(10px)) {
    .newsletter-input-group {
        backdrop-filter: blur(10px);
    }
}

/* Fallback for unsupported browsers */
@supports not (backdrop-filter: blur(10px)) {
    .newsletter-input-group {
        background: rgba(255, 255, 255, 0.2);
    }
}
```

#### 5. Database Schema Issues
**Symptoms**: Missing fields or data not saving
**Solutions**:
```sql
-- Check table structure
DESCRIBE hero_sections;
DESCRIBE newsletter_subscribers;

-- Add missing columns
ALTER TABLE hero_sections ADD COLUMN show_newsletter_input BOOLEAN DEFAULT FALSE;
ALTER TABLE hero_sections ADD COLUMN newsletter_placeholder VARCHAR(255) DEFAULT 'Enter your email address';

-- Check data integrity
SELECT COUNT(*) FROM hero_sections WHERE active = 1;
SELECT COUNT(*) FROM newsletter_subscribers WHERE status = 'active';
```

### Performance Optimization

#### Database Queries
```php
// Use prepared statements
$stmt = $db->prepare("SELECT * FROM hero_sections WHERE page_name = ? AND active = 1");

// Add database indexes
CREATE INDEX idx_page_name ON hero_sections(page_name);
CREATE INDEX idx_active ON hero_sections(active);
```

#### CSS Optimization
```css
/* Use CSS custom properties for dynamic colors */
:root {
    --hero-overlay-color: #a99eff;
    --hero-overlay-opacity: 0.6;
}

.hero-overlay {
    background: rgba(var(--hero-overlay-color), var(--hero-overlay-opacity));
}
```

### Security Considerations

#### Email Validation
```php
// Strict email validation
$email = filter_var($_POST['email'] ?? '', FILTER_VALIDATE_EMAIL);
if (!$email || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    throw new InvalidArgumentException('Invalid email address');
}
```

#### SQL Injection Prevention
```php
// Always use prepared statements
$stmt = $db->prepare("INSERT INTO newsletter_subscribers (email, source, page) VALUES (?, ?, ?)");
$stmt->execute([$email, $source, $page]);
```

#### XSS Prevention
```php
// Escape output
echo htmlspecialchars($hero['title'], ENT_QUOTES, 'UTF-8');
echo htmlspecialchars($hero['description'], ENT_QUOTES, 'UTF-8');
```

## Hero Headers System (NEW)

### Overview
The Hero Headers system is a dedicated admin interface for managing page header hero sections across the website. This system is separate from the footer hero CTA sections to prevent conflicts and provide specialized controls.

### Key Features
- **Dedicated Admin Interface**: `/admin/hero-headers.php`
- **Page Header Control**: Manages titles, subtitles, and breadcrumbs
- **Background Management**: Images, gradients, and solid colors with overlay controls
- **CTA Button Integration**: Optional call-to-action buttons in headers
- **Height Customization**: Small/Medium/Large/Custom height options
- **Color Controls**: Individual color pickers for all text elements
- **Breadcrumb Management**: Show/hide and style breadcrumb navigation

### Database Schema - hero_headers
```sql
CREATE TABLE hero_headers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_name VARCHAR(100) NOT NULL UNIQUE,
    page_title VARCHAR(255),
    subtitle VARCHAR(255),
    show_breadcrumbs BOOLEAN DEFAULT TRUE,
    background_type ENUM('image', 'gradient', 'color') DEFAULT 'gradient',
    background_image VARCHAR(255),
    background_gradient TEXT DEFAULT 'linear-gradient(135deg, rgba(55, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)',
    background_color VARCHAR(7) DEFAULT '#a99eff',
    background_opacity DECIMAL(3,2) DEFAULT 0.60,
    height_type ENUM('small', 'medium', 'large', 'custom') DEFAULT 'medium',
    height_custom INT DEFAULT 400,
    title_color VARCHAR(7) DEFAULT '#ffffff',
    subtitle_color VARCHAR(7) DEFAULT '#ffffff',
    breadcrumb_color VARCHAR(7) DEFAULT '#ffffff',
    show_cta_button BOOLEAN DEFAULT FALSE,
    cta_button_text VARCHAR(100),
    cta_button_link VARCHAR(255),
    cta_button_color VARCHAR(7) DEFAULT '#E67E22',
    cta_button_text_color VARCHAR(7) DEFAULT '#ffffff',
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Implementation Guide

#### 1. Add Hero Header to Page
```php
// In your page file (e.g., about.php)
<?php
loadTemplate('hero-header', ['hero_page_name' => 'about']);
?>
```

#### 2. Configure in Admin Panel
- Navigate to `/admin/hero-headers.php`
- Create or edit hero headers for specific pages
- Customize content, backgrounds, and styling
- Enable/disable breadcrumbs and CTA buttons

#### 3. Template Structure
```php
// templates/hero-header.php
<section class="hero-header" style="background and height styles">
    <div class="hero-header-overlay"></div>
    <div class="container">
        <div class="hero-header-content">
            <nav class="hero-breadcrumb">Home › Current Page</nav>
            <h1 class="hero-header-title">Page Title</h1>
            <p class="hero-header-subtitle">Page Subtitle</p>
            <div class="hero-header-cta">
                <a href="#" class="hero-cta-button">CTA Button</a>
            </div>
        </div>
    </div>
</section>
```

### System Separation

#### Footer Hero CTAs vs Header Heroes
- **Footer Hero CTAs** (`hero_sections` table): Newsletter signup, call-to-action sections before footer
- **Header Heroes** (`hero_headers` table): Page headers with titles, breadcrumbs, and backgrounds
- **Separate Admin Interfaces**: Prevents conflicts and provides specialized controls
- **Different Templates**: `hero-cta.php` vs `hero-header.php`

### Files Created/Modified

#### New Files
- `admin/hero-headers.php` - Admin interface for hero headers
- `templates/hero-header.php` - Hero header template
- `test_delete/setup-hero-headers-table.php` - Database setup script
- `test_delete/test-hero-headers.php` - System testing script
- `test_delete/demo-hero-header.php` - Implementation demo

#### Modified Files
- `includes/functions.php` - Added `getHeroHeader()` function

### Testing and Setup

#### 1. Database Setup
```bash
# Run the setup script
http://localhost/monolith-design/test_delete/test-hero-headers.php
```

#### 2. Admin Access
```bash
# Access the admin interface
http://localhost/monolith-design/admin/hero-headers.php
```

#### 3. Demo Page
```bash
# View implementation demo
http://localhost/monolith-design/test_delete/demo-hero-header.php
```

### Troubleshooting Hero Headers

#### Common Issues

1. **Hero Header Not Displaying**
   - Check `$hero_page_name` is set correctly
   - Verify database entry exists and is active
   - Ensure template is loaded properly

2. **Admin Interface Access**
   - Verify admin authentication
   - Check database connection
   - Ensure hero_headers table exists

3. **Styling Issues**
   - Check CSS conflicts with existing styles
   - Verify color values are valid hex codes
   - Test responsive behavior on mobile

#### Debug Commands
```php
// Check hero header data
$hero = getHeroHeader('page_name');
var_dump($hero);

// Verify table exists
SHOW TABLES LIKE 'hero_headers';

// Check page entries
SELECT * FROM hero_headers WHERE active = 1;
```

---

**Last Updated**: July 14, 2025
**Version**: 2.0 (Added Hero Headers System)
**Author**: Monolith Design Development Team
