<?php
/**
 * Services Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Simple authentication - session already started in functions.php
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

// Handle file upload function
function handleIconUpload($file, $service_slug) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }

    $allowed_types = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];
    $allowed_extensions = ['svg', 'png', 'jpg', 'jpeg'];

    $file_info = pathinfo($file['name']);
    $extension = strtolower($file_info['extension']);

    if (!in_array($file['type'], $allowed_types) || !in_array($extension, $allowed_extensions)) {
        return false;
    }

    // Create filename: service-slug.extension
    $filename = $service_slug . '.' . $extension;
    $upload_path = '../assets/images/uploads/services/' . $filename;

    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return 'assets/images/uploads/services/' . $filename;
    }

    return false;
}

// Handle form submissions
$message = '';
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_service':
                $title = sanitizeInput($_POST['title']);
                $slug = sanitizeInput($_POST['slug']);
                $description = sanitizeInput($_POST['description']);
                $content = $_POST['content']; // Allow HTML
                $icon = sanitizeInput($_POST['icon']);
                $sort_order = intval($_POST['sort_order']);

                // Handle icon upload
                if (isset($_FILES['icon_upload']) && $_FILES['icon_upload']['error'] === UPLOAD_ERR_OK) {
                    $uploaded_icon = handleIconUpload($_FILES['icon_upload'], $slug);
                    if ($uploaded_icon) {
                        $icon = $uploaded_icon;
                    } else {
                        $message = 'Error uploading icon. Please check file format (SVG, PNG, JPG allowed).';
                        break;
                    }
                }

                $db = Database::getConnection();
                $stmt = $db->prepare("INSERT INTO services (title, slug, description, content, icon, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
                if ($stmt->execute([$title, $slug, $description, $content, $icon, $sort_order])) {
                    $message = 'Service added successfully!';
                } else {
                    $message = 'Error adding service.';
                }
                break;
                
            case 'update_service':
                $id = intval($_POST['id']);
                $title = sanitizeInput($_POST['title']);
                $slug = sanitizeInput($_POST['slug']);
                $description = sanitizeInput($_POST['description']);
                $content = $_POST['content']; // Allow HTML
                $icon = sanitizeInput($_POST['icon']);
                $sort_order = intval($_POST['sort_order']);
                $active = isset($_POST['active']) ? 1 : 0;

                // Handle icon upload
                if (isset($_FILES['icon_upload']) && $_FILES['icon_upload']['error'] === UPLOAD_ERR_OK) {
                    $uploaded_icon = handleIconUpload($_FILES['icon_upload'], $slug);
                    if ($uploaded_icon) {
                        $icon = $uploaded_icon;

                        // Delete old uploaded icon if it exists and is in uploads folder
                        $db = Database::getConnection();
                        $stmt = $db->prepare("SELECT icon FROM services WHERE id = ?");
                        $stmt->execute([$id]);
                        $old_service = $stmt->fetch();
                        if ($old_service && strpos($old_service['icon'], 'uploads/services/') !== false) {
                            $old_icon_path = '../' . $old_service['icon'];
                            if (file_exists($old_icon_path)) {
                                unlink($old_icon_path);
                            }
                        }
                    } else {
                        $message = 'Error uploading icon. Please check file format (SVG, PNG, JPG allowed).';
                        break;
                    }
                }

                $db = Database::getConnection();
                $stmt = $db->prepare("UPDATE services SET title = ?, slug = ?, description = ?, content = ?, icon = ?, sort_order = ?, active = ? WHERE id = ?");
                if ($stmt->execute([$title, $slug, $description, $content, $icon, $sort_order, $active, $id])) {
                    $message = 'Service updated successfully!';
                } else {
                    $message = 'Error updating service.';
                }
                break;
                
            case 'delete_service':
                $id = intval($_POST['id']);
                $db = Database::getConnection();
                $stmt = $db->prepare("DELETE FROM services WHERE id = ?");
                if ($stmt->execute([$id])) {
                    $message = 'Service deleted successfully!';
                } else {
                    $message = 'Error deleting service.';
                }
                break;
        }
    }
}

// Get all services
$db = Database::getConnection();
$stmt = $db->query("SELECT * FROM services ORDER BY sort_order ASC");
$services = $stmt->fetchAll();

// Get service for editing
$edit_service = null;
if (isset($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $stmt = $db->prepare("SELECT * FROM services WHERE id = ?");
    $stmt->execute([$edit_id]);
    $edit_service = $stmt->fetch();
}

// Get statistics
$stats = [
    'total' => count($services),
    'active' => count(array_filter($services, fn($s) => $s['active'] == 1)),
    'inactive' => count(array_filter($services, fn($s) => $s['active'] == 0)),
    'recent' => count(array_filter($services, fn($s) => strtotime($s['created_at']) > strtotime('-30 days')))
];

// Render the page using the new theme system
renderAdminPage('management', [
    'page_title' => 'Services Management',
    'page_icon' => 'fas fa-cogs',
    'page_description' => 'Manage your services portfolio with icons, descriptions, and detailed content.',
    'management_title' => 'Services Portfolio',
    'management_description' => 'Showcase your services with detailed descriptions, custom icons, and professional presentation.',
    'management_actions' => [
        [
            'url' => siteUrl('services'),
            'label' => 'View Services',
            'class' => 'btn-outline-primary',
            'icon' => 'fas fa-external-link-alt',
            'target' => '_blank'
        ]
    ],
    'show_search' => true,
    'search_placeholder' => 'Search services by title, description, or content...',
    'search_target' => '.searchable-item',
    'show_table' => true,
    'table_title' => 'All Services',
    'table_content_file' => __DIR__ . '/theme/content/services-table.php',
    'show_stats' => true,
    'stats_cards' => [
        [
            'icon' => 'fas fa-cogs',
            'number' => $stats['total'],
            'label' => 'Total Services'
        ],
        [
            'icon' => 'fas fa-check-circle',
            'number' => $stats['active'],
            'label' => 'Active'
        ],
        [
            'icon' => 'fas fa-pause-circle',
            'number' => $stats['inactive'],
            'label' => 'Inactive'
        ],
        [
            'icon' => 'fas fa-calendar-plus',
            'number' => $stats['recent'],
            'label' => 'Recent (30d)'
        ]
    ],
    'custom_content_before_table' => function() use ($edit_service) {
        include __DIR__ . '/theme/content/services-form.php';
    },
    'message' => $message,
    'error' => isset($error) ? $error : '',
    'services' => $services,
    'edit_service' => $edit_service,
    'custom_js' => '
        function toggleForm() {
            const form = document.getElementById("serviceForm");
            if (form) {
                form.classList.toggle("show");
                if (form.classList.contains("show")) {
                    document.getElementById("title").focus();
                }
            }
        }

        function cancelForm() {
            window.location.href = "services.php";
        }
    '
]);
?>
