<?php
/**
 * Hero Headers Table Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure hero_headers variable is available
if (!isset($hero_headers)) {
    $hero_headers = [];
}
?>

<?php if (empty($hero_headers)): ?>
    <div class="no-data-state">
        <div class="no-data-icon">
            <i class="fas fa-image"></i>
        </div>
        <h3>No hero headers yet</h3>
        <p>Hero headers appear at the top of pages with titles, breadcrumbs, and backgrounds. Create your first hero header to get started.</p>
        <button type="button" class="btn btn-primary" onclick="toggleAddForm()">
            <i class="fas fa-plus me-2"></i>
            Add First Hero Header
        </button>
    </div>
<?php else: ?>
    <div class="table-responsive">
        <table class="table table-hover table-striped">
            <thead>
                <tr>
                    <th width="50">#</th>
                    <th>Page</th>
                    <th>Title & Subtitle</th>
                    <th>Background</th>
                    <th>Height</th>
                    <th>Features</th>
                    <th>Status</th>
                    <th>Last Updated</th>
                    <th width="140">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($hero_headers as $index => $header): ?>
                <tr class="searchable-item" 
                    data-search="<?php echo htmlspecialchars(strtolower($header['page_name'] . ' ' . $header['page_title'] . ' ' . $header['subtitle'])); ?>">
                    <td class="text-center">
                        <span class="item-number"><?php echo $index + 1; ?></span>
                    </td>
                    <td>
                        <div class="page-info">
                            <strong class="page-name"><?php echo htmlspecialchars($header['page_name']); ?></strong>
                            <div class="page-url text-muted small">
                                <i class="fas fa-link me-1"></i>
                                /<?php echo htmlspecialchars($header['page_name']); ?>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="content-preview">
                            <div class="title-preview">
                                <strong><?php echo htmlspecialchars($header['page_title']); ?></strong>
                            </div>
                            <?php if (!empty($header['subtitle'])): ?>
                                <div class="subtitle-preview text-muted small">
                                    <?php echo htmlspecialchars(truncateText($header['subtitle'], 60)); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </td>
                    <td>
                        <div class="background-info">
                            <span class="badge bg-<?php echo $header['background_type'] === 'image' ? 'primary' : ($header['background_type'] === 'gradient' ? 'info' : 'secondary'); ?>">
                                <?php echo ucfirst($header['background_type']); ?>
                            </span>
                            <div class="color-preview mt-1">
                                <span class="color-swatch" style="background-color: <?php echo $header['background_color']; ?>"></span>
                                <small class="text-muted"><?php echo $header['background_opacity']; ?> opacity</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-outline-dark">
                            <?php echo ucfirst($header['height_type']); ?>
                            <?php if ($header['height_type'] === 'custom'): ?>
                                <br><small>(<?php echo $header['height_custom']; ?>px)</small>
                            <?php endif; ?>
                        </span>
                    </td>
                    <td>
                        <div class="features-list">
                            <?php if ($header['show_breadcrumbs']): ?>
                                <span class="feature-badge">
                                    <i class="fas fa-route"></i> Breadcrumbs
                                </span>
                            <?php endif; ?>
                            <?php if ($header['show_cta_button']): ?>
                                <span class="feature-badge">
                                    <i class="fas fa-mouse-pointer"></i> CTA Button
                                </span>
                            <?php endif; ?>
                        </div>
                    </td>
                    <td>
                        <span class="badge <?php echo $header['active'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $header['active'] ? 'Active' : 'Inactive'; ?>
                        </span>
                    </td>
                    <td>
                        <small class="text-muted">
                            <?php echo date('M j, Y', strtotime($header['updated_at'])); ?>
                        </small>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <a href="?edit=<?php echo $header['id']; ?>" 
                               class="btn btn-sm btn-outline-primary" 
                               title="Edit Hero Header">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" 
                                    class="btn btn-sm btn-outline-danger" 
                                    onclick="deleteHeroHeader(<?php echo $header['id']; ?>, '<?php echo htmlspecialchars($header['page_name']); ?>')"
                                    title="Delete Hero Header">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php endif; ?>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the hero header for "<span id="deletePageName"></span>"?</p>
                <p class="text-muted small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="deleteId">
                    <button type="submit" class="btn btn-danger">Delete Hero Header</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.color-swatch {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 3px;
    border: 1px solid #ddd;
    vertical-align: middle;
    margin-right: 5px;
}

.feature-badge {
    display: inline-block;
    background: #f8f9fa;
    color: #495057;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    margin-right: 4px;
    margin-bottom: 2px;
}

.feature-badge i {
    margin-right: 3px;
}

.features-list {
    max-width: 120px;
}

.page-info .page-name {
    color: #495057;
}

.content-preview .title-preview {
    font-weight: 500;
    color: #212529;
}

.action-buttons {
    display: flex;
    gap: 4px;
}

.action-buttons .btn {
    padding: 0.25rem 0.5rem;
}
</style>

<script>
function deleteHeroHeader(id, pageName) {
    document.getElementById('deletePageName').textContent = pageName;
    document.getElementById('deleteId').value = id;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
