<?php
/**
 * Setup Default Hero CTA Values
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>SETUP HERO CTA DEFAULTS</h1><pre>";

try {
    echo "=== SETTING UP DEFAULT HERO CTA VALUES ===\n\n";
    
    // Set default hero CTA values
    $defaults = [
        'hero_cta_caption' => "Ready to Build?",
        'hero_cta_title' => 'Ready to Get Started?',
        'hero_cta_description' => "Let's transform your vision into reality with our innovative architectural solutions and expert craftsmanship.",
        'hero_cta_button_text' => 'Start Your Project',
        'hero_cta_button_link' => 'contact',
        'hero_cta_background' => 'assets/images/demo-image/demo-images/slider101.jpg'
    ];
    
    foreach ($defaults as $key => $value) {
        $result = updateThemeOption($key, $value);
        if ($result) {
            echo "✅ Set $key: $value\n";
        } else {
            echo "❌ Failed to set $key\n";
        }
    }
    
    echo "\n=== VERIFYING VALUES ===\n";
    
    foreach ($defaults as $key => $expected_value) {
        $actual_value = getThemeOption($key, 'NOT FOUND');
        if ($actual_value === $expected_value) {
            echo "✅ $key: CORRECT\n";
        } else {
            echo "❌ $key: Expected '$expected_value', got '$actual_value'\n";
        }
    }
    
    echo "\n=== TESTING HERO CTA TEMPLATE WITH NEW VALUES ===\n";
    
    // Test the template with the new values
    $hero_data = []; // Use defaults from database
    
    ob_start();
    include __DIR__ . '/../templates/hero-cta.php';
    $output = ob_get_clean();
    
    echo "Template rendered: " . (strlen($output) > 0 ? 'YES' : 'NO') . "\n";
    echo "Output length: " . strlen($output) . " characters\n";
    
    // Check if the output contains our expected values
    if (strpos($output, 'Ready to Get Started?') !== false) {
        echo "✅ Title found in output\n";
    } else {
        echo "❌ Title not found in output\n";
    }
    
    if (strpos($output, 'Start Your Project') !== false) {
        echo "✅ Button text found in output\n";
    } else {
        echo "❌ Button text not found in output\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
