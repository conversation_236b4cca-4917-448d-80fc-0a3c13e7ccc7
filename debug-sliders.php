<?php
define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h2>Debugging Sliders</h2>";

try {
    $sliders = getSliders();
    echo "<h3>Database Sliders Count: " . count($sliders) . "</h3>";
    
    if (!empty($sliders)) {
        echo "<h4>Slider Details:</h4>";
        foreach ($sliders as $index => $slider) {
            echo "<p><strong>Slider " . ($index + 1) . ":</strong> " . htmlspecialchars($slider['title']) . " (Active: " . ($slider['active'] ? 'Yes' : 'No') . ")</p>";
        }
    } else {
        echo "<p>No sliders found in database!</p>";
    }
    
    // Test database connection
    $db = Database::getConnection();
    $stmt = $db->query("SELECT COUNT(*) as count FROM sliders WHERE active = 1");
    $result = $stmt->fetch();
    echo "<h4>Direct DB Query - Active Sliders: " . $result['count'] . "</h4>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
