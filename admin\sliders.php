<?php
/**
 * Slider Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_slider':
                try {
                    $db = Database::getConnection();

                    // Validate required fields
                    if (empty($_POST['title'])) {
                        $error = 'Title is required';
                        break;
                    }

                    // Handle background image upload
                    $background_image = '';
                    if (isset($_FILES['background_image']) && $_FILES['background_image']['error'] === UPLOAD_ERR_OK) {
                        $upload_result = uploadFile($_FILES['background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload_result['success']) {
                            $background_image = $upload_result['url'];
                        } else {
                            $error = 'Image upload failed: ' . $upload_result['message'];
                            break;
                        }
                    } elseif (isset($_FILES['background_image']) && $_FILES['background_image']['error'] !== UPLOAD_ERR_NO_FILE) {
                        $error = 'Image upload error: ' . $_FILES['background_image']['error'];
                        break;
                    } else {
                        $error = 'Background image is required';
                        break;
                    }

                    // Handle button link (custom URL or predefined page)
                    $button_link = $_POST['button_link'] ?? '#';
                    if ($button_link === 'custom' && !empty($_POST['custom_url'])) {
                        $button_link = $_POST['custom_url'];
                    }

                    // Handle custom height values
                    $height_desktop = $_POST['height_desktop'] ?? 500;
                    if ($height_desktop === 'custom') {
                        $height_desktop = $_POST['height_desktop_custom'] ?? 500;
                    }

                    $height_tablet = $_POST['height_tablet'] ?? 400;
                    if ($height_tablet === 'custom') {
                        $height_tablet = $_POST['height_tablet_custom'] ?? 400;
                    }

                    $height_mobile = $_POST['height_mobile'] ?? 300;
                    if ($height_mobile === 'custom') {
                        $height_mobile = $_POST['height_mobile_custom'] ?? 300;
                    }

                    $stmt = $db->prepare("
                        INSERT INTO sliders (title, subtitle, background_image, button_text, button_link,
                                           text_color, overlay_color, overlay_opacity, title_char_limit,
                                           description_char_limit, sort_order, active, animation_type,
                                           animation_duration, auto_play, pause_on_hover, show_navigation_dots,
                                           title_font_size, title_font_family, title_font_weight, title_text_transform,
                                           title_line_height, title_break_type, title_break_limit,
                                           description_break_type, description_break_limit, description_font_size,
                                           description_font_family, description_font_weight, description_text_transform,
                                           description_line_height, height_desktop, height_tablet, height_mobile,
                                           content_alignment, vertical_alignment, padding_top, padding_bottom)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");

                    $stmt->execute([
                        $_POST['title'],
                        $_POST['subtitle'] ?? '',
                        $background_image,
                        $_POST['button_text'] ?? 'Learn More',
                        $button_link,
                        $_POST['text_color'] ?? '#ffffff',
                        $_POST['overlay_color'] ?? '#000000',
                        $_POST['overlay_opacity'] ?? 0.5,
                        $_POST['title_char_limit'] ?? 50,
                        $_POST['description_char_limit'] ?? 100,
                        $_POST['sort_order'] ?? 0,
                        isset($_POST['active']) ? 1 : 0,
                        $_POST['animation_type'] ?? 'fade',
                        $_POST['animation_duration'] ?? 5.0,
                        isset($_POST['auto_play']) ? 1 : 0,
                        isset($_POST['pause_on_hover']) ? 1 : 0,
                        isset($_POST['show_navigation_dots']) ? 1 : 0,
                        $_POST['title_font_size'] ?? 3.5,
                        $_POST['title_font_family'] ?? 'System Default',
                        $_POST['title_font_weight'] ?? 700,
                        $_POST['title_text_transform'] ?? 'none',
                        $_POST['title_line_height'] ?? 1.2,
                        $_POST['title_break_type'] ?? 'none',
                        $_POST['title_break_limit'] ?? 50,
                        $_POST['description_break_type'] ?? 'none',
                        $_POST['description_break_limit'] ?? 100,
                        $_POST['description_font_size'] ?? 1.25,
                        $_POST['description_font_family'] ?? 'System Default',
                        $_POST['description_font_weight'] ?? 400,
                        $_POST['description_text_transform'] ?? 'none',
                        $_POST['description_line_height'] ?? 1.6,
                        $height_desktop,
                        $height_tablet,
                        $height_mobile,
                        $_POST['content_alignment'] ?? 'center',
                        $_POST['vertical_alignment'] ?? 'center',
                        $_POST['padding_top'] ?? 90,
                        $_POST['padding_bottom'] ?? 40
                    ]);

                    $message = 'Slider added successfully!';
                } catch (Exception $e) {
                    $error = 'Error adding slider: ' . $e->getMessage();
                }
                break;
                
            case 'update_slider':
                try {
                    $db = Database::getConnection();

                    // Validate required fields
                    if (empty($_POST['title'])) {
                        $error = 'Title is required';
                        break;
                    }

                    // Handle background image upload
                    $background_image = $_POST['current_background_image'] ?? '';
                    if (isset($_FILES['background_image']) && $_FILES['background_image']['error'] === UPLOAD_ERR_OK) {
                        $upload_result = uploadFile($_FILES['background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload_result['success']) {
                            $background_image = $upload_result['url'];
                        } else {
                            $error = 'Image upload failed: ' . $upload_result['message'];
                            break;
                        }
                    }

                    // Handle button link (custom URL or predefined page)
                    $button_link = $_POST['button_link'] ?? '#';
                    if ($button_link === 'custom' && !empty($_POST['custom_url'])) {
                        $button_link = $_POST['custom_url'];
                    }

                    // Handle custom height values
                    $height_desktop = $_POST['height_desktop'] ?? 500;
                    if ($height_desktop === 'custom') {
                        $height_desktop = $_POST['height_desktop_custom'] ?? 500;
                    }

                    $height_tablet = $_POST['height_tablet'] ?? 400;
                    if ($height_tablet === 'custom') {
                        $height_tablet = $_POST['height_tablet_custom'] ?? 400;
                    }

                    $height_mobile = $_POST['height_mobile'] ?? 300;
                    if ($height_mobile === 'custom') {
                        $height_mobile = $_POST['height_mobile_custom'] ?? 300;
                    }

                    $stmt = $db->prepare("
                        UPDATE sliders SET title = ?, subtitle = ?, background_image = ?, button_text = ?,
                                         button_link = ?, text_color = ?, overlay_color = ?, overlay_opacity = ?,
                                         title_char_limit = ?, description_char_limit = ?, sort_order = ?, active = ?,
                                         animation_type = ?, animation_duration = ?, auto_play = ?, pause_on_hover = ?,
                                         show_navigation_dots = ?, title_font_size = ?, title_font_family = ?, title_font_weight = ?,
                                         title_text_transform = ?, title_line_height = ?, title_break_type = ?, title_break_limit = ?,
                                         description_break_type = ?, description_break_limit = ?, description_font_size = ?,
                                         description_font_family = ?, description_font_weight = ?, description_text_transform = ?,
                                         description_line_height = ?, height_desktop = ?, height_tablet = ?, height_mobile = ?,
                                         content_alignment = ?, vertical_alignment = ?, padding_top = ?, padding_bottom = ?
                        WHERE id = ?
                    ");

                    $stmt->execute([
                        $_POST['title'],
                        $_POST['subtitle'] ?? '',
                        $background_image,
                        $_POST['button_text'] ?? 'Learn More',
                        $button_link,
                        $_POST['text_color'] ?? '#ffffff',
                        $_POST['overlay_color'] ?? '#000000',
                        $_POST['overlay_opacity'] ?? 0.5,
                        $_POST['title_char_limit'] ?? 50,
                        $_POST['description_char_limit'] ?? 100,
                        $_POST['sort_order'] ?? 0,
                        isset($_POST['active']) ? 1 : 0,
                        $_POST['animation_type'] ?? 'fade',
                        $_POST['animation_duration'] ?? 5.0,
                        isset($_POST['auto_play']) ? 1 : 0,
                        isset($_POST['pause_on_hover']) ? 1 : 0,
                        isset($_POST['show_navigation_dots']) ? 1 : 0,
                        $_POST['title_font_size'] ?? 3.5,
                        $_POST['title_font_family'] ?? 'System Default',
                        $_POST['title_font_weight'] ?? 700,
                        $_POST['title_text_transform'] ?? 'none',
                        $_POST['title_line_height'] ?? 1.2,
                        $_POST['title_break_type'] ?? 'none',
                        $_POST['title_break_limit'] ?? 50,
                        $_POST['description_break_type'] ?? 'none',
                        $_POST['description_break_limit'] ?? 100,
                        $_POST['description_font_size'] ?? 1.25,
                        $_POST['description_font_family'] ?? 'System Default',
                        $_POST['description_font_weight'] ?? 400,
                        $_POST['description_text_transform'] ?? 'none',
                        $_POST['description_line_height'] ?? 1.6,
                        $height_desktop,
                        $height_tablet,
                        $height_mobile,
                        $_POST['content_alignment'] ?? 'center',
                        $_POST['vertical_alignment'] ?? 'center',
                        $_POST['padding_top'] ?? 90,
                        $_POST['padding_bottom'] ?? 40,
                        $_POST['slider_id']
                    ]);

                    $message = 'Slider updated successfully!';
                } catch (Exception $e) {
                    $error = 'Error updating slider: ' . $e->getMessage();
                }
                break;
                
            case 'delete_slider':
                try {
                    $db = Database::getConnection();
                    $stmt = $db->prepare("DELETE FROM sliders WHERE id = ?");
                    $stmt->execute([$_POST['slider_id']]);
                    $message = 'Slider deleted successfully!';
                } catch (Exception $e) {
                    $error = 'Error deleting slider: ' . $e->getMessage();
                }
                break;
                
            case 'update_universal_height':
                try {
                    $use_universal = isset($_POST['use_universal_height']) ? '1' : '0';
                    $height_desktop = $_POST['universal_height_desktop'] ?? '600';
                    $height_tablet = $_POST['universal_height_tablet'] ?? '450';
                    $height_mobile = $_POST['universal_height_mobile'] ?? '350';
                    
                    updateThemeOption('use_universal_slider_height', $use_universal);
                    updateThemeOption('universal_slider_height_desktop', $height_desktop);
                    updateThemeOption('universal_slider_height_tablet', $height_tablet);
                    updateThemeOption('universal_slider_height_mobile', $height_mobile);
                    
                    $message = 'Universal height settings updated successfully!';
                } catch (Exception $e) {
                    $error = 'Error updating universal height settings: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get all sliders
try {
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM sliders ORDER BY sort_order ASC, id ASC");
    $sliders = $stmt->fetchAll();
} catch (Exception $e) {
    $sliders = [];
    $error = 'Error fetching sliders: ' . $e->getMessage();
}

// Get available pages for dropdown
function getAvailablePages() {
    $pages = [
        '' => 'Select a page...',
        'index' => 'Home',
        'about' => 'About',
        'services' => 'Services',
        'projects' => 'Projects',
        'team' => 'Team',
        'news' => 'News',
        'contact' => 'Contact'
    ];

    // Check for additional pages
    $additional_pages = [];
    $files = glob('*.php');
    foreach ($files as $file) {
        $page_name = basename($file, '.php');
        if (!in_array($page_name, ['index', 'about', 'services', 'projects', 'team', 'news', 'contact'])
            && !in_array($page_name, ['config', 'database', 'update-database'])
            && !str_starts_with($page_name, 'admin')
            && !str_starts_with($page_name, 'test_')) {
            $additional_pages[$page_name] = ucfirst(str_replace(['-', '_'], ' ', $page_name));
        }
    }

    if (!empty($additional_pages)) {
        $pages = array_merge($pages, $additional_pages);
    }

    $pages['custom'] = 'Custom URL...';

    return $pages;
}

$available_pages = getAvailablePages();

// Get font families
function getFontFamilies() {
    try {
        $db = Database::getConnection();
        $stmt = $db->query("SELECT * FROM font_families ORDER BY name ASC");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [
            ['name' => 'System Default', 'css_name' => 'system-ui, -apple-system, sans-serif'],
            ['name' => 'Arial', 'css_name' => 'Arial, sans-serif'],
            ['name' => 'Helvetica', 'css_name' => 'Helvetica, sans-serif'],
            ['name' => 'Georgia', 'css_name' => 'Georgia, serif'],
            ['name' => 'Times New Roman', 'css_name' => 'Times New Roman, serif']
        ];
    }
}

// Get animation presets
function getAnimationPresets() {
    try {
        $db = Database::getConnection();
        $stmt = $db->query("SELECT * FROM animation_presets ORDER BY name ASC");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [
            ['value' => 'fade', 'name' => 'Fade In/Out'],
            ['value' => 'slide', 'name' => 'Slide Left/Right'],
            ['value' => 'zoom', 'name' => 'Zoom In/Out'],
            ['value' => 'none', 'name' => 'No Animation']
        ];
    }
}

$font_families = getFontFamilies();
$animation_presets = getAnimationPresets();

// Handle edit slider
$edit_slider = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    try {
        $db = Database::getConnection();
        $stmt = $db->prepare("SELECT * FROM sliders WHERE id = ?");
        $stmt->execute([$_GET['edit']]);
        $edit_slider = $stmt->fetch();

        if (!$edit_slider) {
            $error = 'Slider not found';
        }
    } catch (Exception $e) {
        $error = 'Error loading slider: ' . $e->getMessage();
    }
}

// Calculate statistics
$stats = [
    'total' => count($sliders),
    'active' => count(array_filter($sliders, fn($s) => $s['active'] == 1)),
    'inactive' => count(array_filter($sliders, fn($s) => $s['active'] == 0)),
    'with_animation' => count(array_filter($sliders, fn($s) => $s['animation_type'] !== 'none'))
];

// Render the page using the new theme system
renderAdminPage('management', [
    'page_title' => 'Slider Management',
    'page_icon' => 'fas fa-images',
    'page_description' => 'Create and manage dynamic sliders with advanced typography, responsive heights, and animation controls.',
    'management_title' => 'Sliders',
    'management_description' => 'Design compelling sliders with professional typography controls, responsive layouts, and smooth animations.',
    'management_actions' => [
        [
            'url' => '#',
            'label' => 'Add New Slider',
            'class' => 'btn-primary',
            'icon' => 'fas fa-plus',
            'onclick' => 'toggleAddForm()'
        ]
    ],
    'show_search' => true,
    'search_placeholder' => 'Search sliders by title, subtitle, or button text...',
    'search_target' => '.searchable-item',
    'show_table' => true,
    'table_title' => 'All Sliders',
    'table_content_file' => __DIR__ . '/theme/content/sliders-table.php',
    'show_stats' => true,
    'stats_cards' => [
        [
            'icon' => 'fas fa-images',
            'number' => $stats['total'],
            'label' => 'Total Sliders'
        ],
        [
            'icon' => 'fas fa-check-circle',
            'number' => $stats['active'],
            'label' => 'Active'
        ],
        [
            'icon' => 'fas fa-pause-circle',
            'number' => $stats['inactive'],
            'label' => 'Inactive'
        ],
        [
            'icon' => 'fas fa-play',
            'number' => $stats['with_animation'],
            'label' => 'Animated'
        ]
    ],
    'custom_content_before_table' => function() use ($font_families, $animation_presets, $available_pages, $edit_slider) {
        include __DIR__ . '/theme/content/sliders-universal-height.php';
        echo '<div class="mt-4"></div>';

        // Show edit form if editing
        if ($edit_slider) {
            include __DIR__ . '/theme/content/sliders-form.php';
        } else {
            // Show add form (hidden by default)
            echo '<div id="add-slider-form" style="display: none;">';
            $edit_slider = null; // For add form
            include __DIR__ . '/theme/content/sliders-form.php';
            echo '</div>';
        }
    },
    'message' => $message,
    'error' => $error,
    'sliders' => $sliders,
    'font_families' => $font_families,
    'animation_presets' => $animation_presets,
    'available_pages' => $available_pages
]);

// Add JavaScript for form toggle
echo '<script>
function toggleAddForm() {
    const form = document.getElementById("add-slider-form");
    if (form.style.display === "none" || form.style.display === "") {
        form.style.display = "block";
        form.scrollIntoView({ behavior: "smooth" });
    } else {
        form.style.display = "none";
    }
}

function editSlider(sliderId) {
    window.location.href = "sliders.php?edit=" + sliderId;
}
</script>';
?>
