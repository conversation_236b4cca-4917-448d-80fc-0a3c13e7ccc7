<?php
/**
 * Demo Page - Hero Header Implementation
 * Shows how to integrate hero headers into pages
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

$pageTitle = 'Hero Header Demo - Monolith Design';
$pageDescription = 'Demonstration of the hero header system implementation';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
        }
        .demo-section {
            padding: 3rem 0;
            border-bottom: 1px solid #eee;
        }
        .demo-section:last-child {
            border-bottom: none;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
        .feature-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .feature-icon {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Hero Header Template Integration -->
    <?php 
    // This is how you integrate hero headers into any page
    loadTemplate('hero-header', ['hero_page_name' => 'contact']); 
    ?>

    <!-- Main Content -->
    <div class="container">
        <!-- Demo Introduction -->
        <section class="demo-section">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="mb-4">Hero Header System Demo</h2>
                    <p class="lead">This page demonstrates the new Hero Headers system that provides dedicated control for page header sections across the website.</p>
                </div>
            </div>
        </section>

        <!-- Implementation Guide -->
        <section class="demo-section">
            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <h3 class="mb-4"><i class="fas fa-code me-2"></i>Implementation Guide</h3>
                    
                    <div class="feature-card">
                        <h5><i class="fas fa-file-code feature-icon"></i>Step 1: Add to Page Template</h5>
                        <p>Include the hero header template in your page files:</p>
                        <div class="code-block">
&lt;?php<br>
// At the top of your page file (e.g., contact.php)<br>
loadTemplate('hero-header', ['hero_page_name' => 'contact']);<br>
?&gt;
                        </div>
                    </div>

                    <div class="feature-card">
                        <h5><i class="fas fa-database feature-icon"></i>Step 2: Configure in Admin Panel</h5>
                        <p>Manage hero headers through the admin interface:</p>
                        <div class="code-block">
URL: /admin/hero-headers.php<br>
- Create new hero headers for pages<br>
- Customize titles, subtitles, and backgrounds<br>
- Configure CTA buttons and styling<br>
- Control breadcrumb display
                        </div>
                    </div>

                    <div class="feature-card">
                        <h5><i class="fas fa-palette feature-icon"></i>Step 3: Customize Styling</h5>
                        <p>Hero headers support extensive customization:</p>
                        <div class="code-block">
Background Options:<br>
- Image backgrounds with overlay<br>
- CSS gradients<br>
- Solid colors<br><br>
Height Options:<br>
- Small (300px), Medium (400px), Large (600px)<br>
- Custom height in pixels<br><br>
Color Controls:<br>
- Title, subtitle, and breadcrumb colors<br>
- CTA button colors and text colors
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Overview -->
        <section class="demo-section">
            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <h3 class="mb-4"><i class="fas fa-star me-2"></i>Key Features</h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="feature-card">
                                <i class="fas fa-cogs feature-icon"></i>
                                <h5>Database-Driven</h5>
                                <p>All hero header content is stored in the database and managed through the admin panel.</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-card">
                                <i class="fas fa-mobile-alt feature-icon"></i>
                                <h5>Responsive Design</h5>
                                <p>Hero headers automatically adapt to different screen sizes with mobile-optimized layouts.</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-card">
                                <i class="fas fa-paint-brush feature-icon"></i>
                                <h5>Flexible Backgrounds</h5>
                                <p>Support for images, gradients, and solid colors with customizable overlay effects.</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-card">
                                <i class="fas fa-mouse-pointer feature-icon"></i>
                                <h5>CTA Integration</h5>
                                <p>Optional call-to-action buttons with customizable text, links, and styling.</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-card">
                                <i class="fas fa-route feature-icon"></i>
                                <h5>Breadcrumb Navigation</h5>
                                <p>Automatic breadcrumb generation with customizable colors and display options.</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-card">
                                <i class="fas fa-shield-alt feature-icon"></i>
                                <h5>Separation of Concerns</h5>
                                <p>Dedicated system for page headers, separate from footer hero CTA sections.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Database Schema -->
        <section class="demo-section">
            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <h3 class="mb-4"><i class="fas fa-table me-2"></i>Database Schema</h3>
                    
                    <div class="feature-card">
                        <h5>hero_headers Table Structure</h5>
                        <div class="code-block">
CREATE TABLE hero_headers (<br>
&nbsp;&nbsp;id INT AUTO_INCREMENT PRIMARY KEY,<br>
&nbsp;&nbsp;page_name VARCHAR(100) NOT NULL UNIQUE,<br>
&nbsp;&nbsp;page_title VARCHAR(255),<br>
&nbsp;&nbsp;subtitle VARCHAR(255),<br>
&nbsp;&nbsp;show_breadcrumbs BOOLEAN DEFAULT TRUE,<br>
&nbsp;&nbsp;background_type ENUM('image', 'gradient', 'color'),<br>
&nbsp;&nbsp;background_image VARCHAR(255),<br>
&nbsp;&nbsp;background_gradient TEXT,<br>
&nbsp;&nbsp;background_color VARCHAR(7) DEFAULT '#a99eff',<br>
&nbsp;&nbsp;background_opacity DECIMAL(3,2) DEFAULT 0.60,<br>
&nbsp;&nbsp;height_type ENUM('small', 'medium', 'large', 'custom'),<br>
&nbsp;&nbsp;height_custom INT DEFAULT 400,<br>
&nbsp;&nbsp;title_color VARCHAR(7) DEFAULT '#ffffff',<br>
&nbsp;&nbsp;subtitle_color VARCHAR(7) DEFAULT '#ffffff',<br>
&nbsp;&nbsp;breadcrumb_color VARCHAR(7) DEFAULT '#ffffff',<br>
&nbsp;&nbsp;show_cta_button BOOLEAN DEFAULT FALSE,<br>
&nbsp;&nbsp;cta_button_text VARCHAR(100),<br>
&nbsp;&nbsp;cta_button_link VARCHAR(255),<br>
&nbsp;&nbsp;cta_button_color VARCHAR(7) DEFAULT '#E67E22',<br>
&nbsp;&nbsp;cta_button_text_color VARCHAR(7) DEFAULT '#ffffff',<br>
&nbsp;&nbsp;active BOOLEAN DEFAULT TRUE,<br>
&nbsp;&nbsp;created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,<br>
&nbsp;&nbsp;updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP<br>
);
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Admin Links -->
        <section class="demo-section">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h3 class="mb-4"><i class="fas fa-tools me-2"></i>Admin Tools</h3>
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        <a href="../admin/hero-headers.php" class="btn btn-primary">
                            <i class="fas fa-cog me-2"></i>Manage Hero Headers
                        </a>
                        <a href="test-hero-headers.php" class="btn btn-success">
                            <i class="fas fa-play me-2"></i>Run System Test
                        </a>
                        <a href="../admin/index.php" class="btn btn-secondary">
                            <i class="fas fa-dashboard me-2"></i>Admin Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Monolith Design Co. - Hero Header System Demo</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
