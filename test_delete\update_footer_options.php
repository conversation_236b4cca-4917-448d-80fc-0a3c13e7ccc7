<?php
define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Set the correct company links with Home first
$company_links = [
    ['title' => 'Home', 'url' => ''],
    ['title' => 'About Us', 'url' => 'about'],
    ['title' => 'Services', 'url' => 'services'], 
    ['title' => 'Projects', 'url' => 'projects'],
    ['title' => 'Blog', 'url' => 'blog'],
    ['title' => 'Careers', 'url' => 'careers'],
    ['title' => 'Privacy Policy', 'url' => 'privacy'],
    ['title' => 'Terms of Service', 'url' => 'terms'],
    ['title' => 'Contact', 'url' => 'contact'],
];

$services_links = [
    ['title' => 'Architectural Design', 'url' => 'service/architectural-design'],
    ['title' => 'Structural Engineering', 'url' => 'service/structural-engineering'],
    ['title' => 'Construction Management', 'url' => 'service/construction-management'],
    ['title' => 'Sustainable Design', 'url' => 'service/sustainable-design'],
];

$projects_links = [
    ['title' => 'All Projects', 'url' => 'projects'],
    ['title' => 'Featured Work', 'url' => 'work'],
    ['title' => 'Commercial', 'url' => 'projects'],
    ['title' => 'Residential', 'url' => 'projects'],
];

// Update theme options
updateThemeOption('footer_company_links', json_encode($company_links));
updateThemeOption('footer_services_links', json_encode($services_links));
updateThemeOption('footer_projects_links', json_encode($projects_links));

echo "Footer navigation links updated successfully!\n";
echo "Company links: " . json_encode($company_links) . "\n";
echo "Services links: " . json_encode($services_links) . "\n";
echo "Projects links: " . json_encode($projects_links) . "\n";
?>
