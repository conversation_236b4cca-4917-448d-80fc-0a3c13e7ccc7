<?php
/**
 * News Details Page - Individual Article/Blog Post
 * Complete single-page template for news content display
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get news slug from URL
$slug = isset($_GET['slug']) ? sanitizeInput($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: ' . siteUrl('news'));
    exit;
}

// Fetch news article from database
$db = Database::getConnection();
$stmt = $db->prepare("SELECT * FROM blog_posts WHERE slug = ? AND active = 1");
$stmt->execute([$slug]);
$article = $stmt->fetch();

// If no article found in database, create demo content
if (!$article) {
    $demo_articles = [
        'smart-building-technologies-integration' => [
            'id' => 1,
            'title' => 'Smart Building Technologies: The Integration Revolution',
            'slug' => 'smart-building-technologies-integration',
            'excerpt' => 'How IoT, AI, and automation are transforming modern buildings into intelligent, responsive environments that adapt to user needs.',
            'content' => '<p>The integration of smart technologies in modern buildings represents a paradigm shift in how we design, construct, and operate architectural spaces. From IoT sensors to AI-driven systems, buildings are becoming increasingly intelligent and responsive.</p>

<h3>The Internet of Things in Architecture</h3>
<p>IoT devices embedded throughout a building create a network of interconnected systems that monitor everything from air quality to occupancy patterns.</p>

<h3>Artificial Intelligence and Machine Learning</h3>
<p>AI systems can predict building maintenance needs, optimize energy consumption, and adapt environmental controls based on user behavior patterns.</p>

<h3>The Future of Smart Buildings</h3>
<p>As technology continues to evolve, we can expect buildings to become even more autonomous and responsive to human needs.</p>',
            'category' => 'Technology',
            'tags' => 'smart buildings, IoT, automation, AI, technology',
            'author' => 'David Kim',
            'published_at' => '2023-12-10 14:30:00',
            'featured_image' => 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1200&h=600&fit=crop',
            'created_at' => '2023-12-10 14:30:00'
        ],
        'sustainable-architecture-trends' => [
            'id' => 2,
            'title' => 'Sustainable Architecture: Building for Tomorrow',
            'slug' => 'sustainable-architecture-trends',
            'excerpt' => 'Exploring innovative approaches to eco-friendly design and construction that minimize environmental impact while maximizing efficiency.',
            'content' => '<p>Sustainable architecture represents a fundamental shift in how we approach building design and construction. By integrating eco-friendly materials, energy-efficient systems, and innovative design principles, architects are creating structures that not only minimize environmental impact but also enhance the quality of life for occupants.</p>

<h3>Green Building Materials</h3>
<p>The selection of sustainable materials is crucial in reducing the carbon footprint of construction projects. From recycled steel and reclaimed wood to innovative bio-based materials, architects now have access to a wide range of eco-friendly options.</p>

<h3>Energy Efficiency and Renewable Systems</h3>
<p>Modern sustainable buildings incorporate advanced energy management systems, solar panels, and smart technologies that significantly reduce energy consumption and operational costs.</p>

<h3>The Future of Sustainable Design</h3>
<p>As climate change concerns continue to grow, sustainable architecture will play an increasingly important role in creating a more environmentally responsible built environment.</p>',
            'category' => 'Sustainability',
            'tags' => 'sustainability, green building, eco-friendly, environment',
            'author' => 'Sarah Chen',
            'published_at' => '2023-12-15 09:00:00',
            'featured_image' => 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=1200&h=600&fit=crop',
            'created_at' => '2023-12-15 09:00:00'
        ],
        'urban-planning-community-design' => [
            'id' => 3,
            'title' => 'Reimagining Urban Spaces: Community-Centered Design',
            'slug' => 'urban-planning-community-design',
            'excerpt' => 'How modern urban planning prioritizes community needs, creating spaces that foster connection and quality of life.',
            'content' => '<p>Urban planning has evolved from purely functional approaches to embrace community-centered design principles that prioritize human connection and quality of life. This shift represents a fundamental change in how we think about creating livable, sustainable cities.</p>

<h3>Community Engagement in Planning</h3>
<p>Modern urban planning processes actively involve community members in decision-making, ensuring that developments reflect the actual needs and desires of residents rather than top-down assumptions.</p>

<h3>Mixed-Use Development</h3>
<p>By integrating residential, commercial, and recreational spaces, planners create vibrant neighborhoods where people can live, work, and play within walking distance, reducing reliance on transportation and fostering community connections.</p>

<h3>Green Spaces and Public Amenities</h3>
<p>Strategic placement of parks, community centers, and public gathering spaces creates focal points for social interaction and provides essential services that enhance quality of life for all residents.</p>',
            'category' => 'Urban Planning',
            'tags' => 'urban planning, community design, public spaces',
            'author' => 'Marcus Rodriguez',
            'published_at' => '2023-12-08 14:30:00',
            'featured_image' => 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=1200&h=600&fit=crop',
            'created_at' => '2023-12-08 14:30:00'
        ]
    ];
    
    if (isset($demo_articles[$slug])) {
        $article = $demo_articles[$slug];
    } else {
        header('Location: ' . siteUrl('404'));
        exit;
    }
    
    // Set up related articles
    $related_articles = [];
    $article_keys = array_keys($demo_articles);
    $current_index = array_search($slug, $article_keys);
    
    // Get next and previous articles
    $next_article = null;
    $prev_article = null;
    
    if ($current_index !== false) {
        if ($current_index < count($article_keys) - 1) {
            $next_slug = $article_keys[$current_index + 1];
            $next_article = [
                'slug' => $next_slug,
                'title' => $demo_articles[$next_slug]['title']
            ];
        }
        if ($current_index > 0) {
            $prev_slug = $article_keys[$current_index - 1];
            $prev_article = [
                'slug' => $prev_slug,
                'title' => $demo_articles[$prev_slug]['title']
            ];
        }
    }
} else {
    $stmt = $db->prepare("SELECT id, slug, title FROM blog_posts WHERE id > ? AND active = 1 ORDER BY id ASC LIMIT 1");
    $stmt->execute([$article['id']]);
    $next_article = $stmt->fetch();

    $stmt = $db->prepare("SELECT id, slug, title FROM blog_posts WHERE id < ? AND active = 1 ORDER BY id DESC LIMIT 1");
    $stmt->execute([$article['id']]);
    $prev_article = $stmt->fetch();
}

$pageTitle = $article['title'] . ' - ' . SITE_NAME;
$pageDescription = $article['excerpt'] ?: 'Read our latest insights on ' . $article['title'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Open Graph meta tags for social sharing -->
    <meta property="og:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta property="og:image" content="<?php echo $article['featured_image'] ?: themeUrl('images/logo.svg'); ?>">
    <meta property="og:url" content="<?php echo siteUrl('news/' . $article['slug']); ?>">
    <meta property="og:type" content="article">
    
    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="twitter:image" content="<?php echo $article['featured_image'] ?: themeUrl('images/logo.svg'); ?>">
    
    <!-- Article-specific styles -->
    <style>
        /* Breadcrumb in Hero */
        .breadcrumb-hero {
            position: absolute;
            top: 2rem;
            left: 0;
            right: 0;
            z-index: 2;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            transition: color 0.3s;
        }

        .breadcrumb a:hover {
            color: white;
        }

        .breadcrumb span {
            color: rgba(255, 255, 255, 0.6);
        }

        /* Article Content Wrapper */
        .article-content-wrapper {
            position: relative;
            z-index: 1;
        }
        
        /* Article Hero Section */
        .article-hero {
            height: 500px;
            display: flex;
            align-items: center;
            color: white;
            text-align: left;
            position: relative;
            margin-top: 80px; /* Account for fixed header */
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .article-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.4));
            z-index: 1;
        }

        .article-hero .container {
            position: relative;
            z-index: 2;
        }
        
        .article-meta-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .article-hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            line-height: 1.2;
        }
        
        .article-hero .lead {
            font-size: 1.25rem;
            color: white;
            opacity: 0.9;
            max-width: 800px;
            line-height: 1.6;
        }
        
        /* Article Content */
        .article-content {
            padding: 4rem 0;
        }
        
        .article-body {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
        }
        
        .article-body h2,
        .article-body h3,
        .article-body h4 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .article-body h2 {
            font-size: 2rem;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }
        
        .article-body h3 {
            font-size: 1.5rem;
            color: var(--primary-color);
        }
        
        .article-body p {
            margin-bottom: 1.5rem;
        }
        
        /* Article Tags */
        .article-tags {
            padding: 2rem 0;
            border-top: 1px solid #E5E5E5;
            margin-top: 2rem;
        }
        
        .tags-list {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }
        
        .tag {
            background: var(--light-bg);
            color: var(--text-color);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.9rem;
            text-decoration: none;
            border: 1px solid #E5E5E5;
        }
        
        /* Social Share */
        .social-share {
            padding: 2rem 0;
            text-align: center;
            border-top: 1px solid #E5E5E5;
            margin-top: 2rem;
        }
        
        .share-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }
        
        .share-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid #E5E5E5;
            color: white;
        }
        
        .share-facebook { background: #1877F2; border-color: #1877F2; }
        .share-twitter { background: #1DA1F2; border-color: #1DA1F2; }
        .share-linkedin { background: #0A66C2; border-color: #0A66C2; }
        .share-email { background: #666; border-color: #666; }
        .share-copy { background: var(--accent-color); border-color: var(--accent-color); }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .article-hero h1 {
                font-size: 2rem;
            }
            
            .article-meta-info {
                flex-direction: column;
                gap: 0.5rem;
                text-align: left;
            }
            
            .share-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Article Hero -->
    <section class="article-hero" style="background-image: url('<?php echo ensureAbsoluteUrl($article['featured_image'] ?: 'https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=1600&h=900&fit=crop'); ?>');">
        <!-- Breadcrumb Navigation -->
        <nav class="breadcrumb-hero">
            <div class="container">
                <div class="breadcrumb">
                    <a href="<?php echo siteUrl(); ?>"><?php echo getThemeOption('breadcrumb_home_text', 'Home'); ?></a>
                    <span>•</span>
                    <a href="<?php echo siteUrl('news'); ?>"><?php echo getThemeOption('breadcrumb_news_text', 'News'); ?></a>
                    <span>•</span>
                    <span><?php echo htmlspecialchars($article['title']); ?></span>
                </div>
            </div>
        </nav>

        <div class="container">
            <div class="article-content-wrapper">
                <div class="article-meta-info">
                    <span><?php echo htmlspecialchars($article['category']); ?></span>
                    <span>•</span>
                    <span><?php echo date('F j, Y', strtotime($article['published_at'])); ?></span>
                    <span>•</span>
                    <span>By <?php echo htmlspecialchars($article['author']); ?></span>
                </div>
                <h1><?php echo htmlspecialchars($article['title']); ?></h1>
                <p class="lead"><?php echo htmlspecialchars($article['excerpt']); ?></p>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main>
        <!-- Article Content -->
        <section class="article-content">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="article-body">
                            <?php echo $article['content'] ?? $article['excerpt'] ?? 'Content not available.'; ?>
                        </div>
                        
                        <!-- Article Tags -->
                        <div class="article-tags">
                            <h4>Tags:</h4>
                            <div class="tags-list">
                                <?php 
                                $tags = explode(',', $article['tags']);
                                foreach ($tags as $tag): 
                                    $tag = trim($tag);
                                    if (!empty($tag)):
                                ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </div>
                        </div>
                        
                        <!-- Social Share -->
                        <div class="social-share">
                            <h4>Share this article</h4>
                            <div class="share-links">
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                                   class="share-link share-facebook" target="_blank">
                                    <i class="fab fa-facebook-f"></i> Facebook
                                </a>
                                <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>&text=<?php echo urlencode($article['title']); ?>" 
                                   class="share-link share-twitter" target="_blank">
                                    <i class="fab fa-twitter"></i> Twitter
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                                   class="share-link share-linkedin" target="_blank">
                                    <i class="fab fa-linkedin-in"></i> LinkedIn
                                </a>
                                <a href="mailto:?subject=<?php echo urlencode($article['title']); ?>&body=<?php echo urlencode('Check out this article: ' . siteUrl('news/' . $article['slug'])); ?>" 
                                   class="share-link share-email">
                                    <i class="fas fa-envelope"></i> Email
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Newsletter Hero Section -->
    <?php include 'templates/newsletter-hero.php'; ?>

    <!-- Footer -->
    <?php loadTemplate('footer'); ?>

    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Scripts -->
    <script src="<?php echo themeUrl('js/main.js'); ?>"></script>
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
</body>
</html>
