<?php
/**
 * Simple script to add projects hero header
 */

require_once '../includes/config.php';
require_once '../includes/database.php';

try {
    $db = Database::getConnection();
    
    // Check if projects hero header already exists
    $stmt = $db->prepare("SELECT id FROM hero_headers WHERE page_name = 'projects'");
    $stmt->execute();
    $existing = $stmt->fetch();
    
    if ($existing) {
        echo "Projects hero header already exists (ID: {$existing['id']})\n";
    } else {
        // Insert projects hero header
        $stmt = $db->prepare("
            INSERT INTO hero_headers (
                page_name, page_title, subtitle, show_breadcrumbs,
                background_type, background_gradient, background_color, background_opacity,
                height_type, height_custom, title_color, subtitle_color, breadcrumb_color,
                show_cta_button, cta_button_text, cta_button_link, cta_button_color, cta_button_text_color,
                active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            'projects',
            'Our Projects',
            'Explore our portfolio of innovative architectural solutions and completed projects',
            1,
            'gradient',
            'linear-gradient(135deg, rgba(55, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)',
            '#a99eff',
            0.60,
            'medium',
            400,
            '#ffffff',
            '#ffffff',
            '#ffffff',
            1,
            'Start Your Project',
            'contact',
            '#E67E22',
            '#ffffff',
            1
        ]);
        
        if ($result) {
            echo "Projects hero header created successfully!\n";
        } else {
            echo "Failed to create projects hero header\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
