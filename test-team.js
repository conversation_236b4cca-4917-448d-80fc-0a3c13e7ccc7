const { test, expect } = require('@playwright/test');

// Team Management System Tests
test.describe('Team Management System', () => {
  const baseURL = 'http://localhost:8888/monolith-design'; // Adjust MAMP port if needed
  const adminURL = `${baseURL}/admin`;
  
  // Test data
  const testTeamMember = {
    name: '<PERSON> Test Member',
    position: 'Senior Test Architect',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/johntest',
    bio: '<PERSON> is a dedicated test architect with over 10 years of experience in quality assurance and automated testing. He specializes in creating robust testing frameworks.',
    sortOrder: 1
  };

  const updatedTeamMember = {
    name: '<PERSON> Updated Member',
    position: 'Lead Quality Architect',
    email: '<EMAIL>',
    bio: 'Updated biography with new information about <PERSON>'s expanded role and responsibilities.'
  };

  const ceoData = {
    name: '<PERSON>',
    title: 'Chief Executive Officer & Principal Architect',
    bio: 'Updated CEO biography for testing purposes.',
    achievements: 'Licensed Architect in 15 states\nLEED AP BD+C Certified Professional\nAIA Gold Medal Recipient (2024)'
  };

  // Helper function to login to admin
  async function loginToAdmin(page) {
    await page.goto(`${adminURL}/login.php`);
    await page.fill('input[name="username"]', 'admin'); // Adjust credentials as needed
    await page.fill('input[name="password"]', 'admin123'); // Adjust credentials as needed
    await page.click('button[type="submit"]');
    await page.waitForURL(`${adminURL}/index.php`);
  }

  test.beforeEach(async ({ page }) => {
    // Set viewport for consistent testing
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('should display team page correctly', async ({ page }) => {
    await page.goto(`${baseURL}/team.php`);
    
    // Check page title and meta
    await expect(page).toHaveTitle(/Our Team/);
    
    // Check main sections exist
    await expect(page.locator('h1')).toContainText('Our Team');
    await expect(page.locator('.ceo-spotlight')).toBeVisible();
    await expect(page.locator('.team-grid')).toBeVisible();
    
    // Check CEO section
    await expect(page.locator('.ceo-info h2')).toBeVisible();
    await expect(page.locator('.ceo-title')).toBeVisible();
    await expect(page.locator('.ceo-bio')).toBeVisible();
    await expect(page.locator('.ceo-achievements')).toBeVisible();
    
    // Check team members section
    await expect(page.locator('h2:has-text("Our Professional Team")')).toBeVisible();
    
    // Check responsive design
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('.team-members')).toBeVisible();
    
    console.log('✅ Team page displays correctly');
  });

  test('should access team admin panel', async ({ page }) => {
    await loginToAdmin(page);
    await page.goto(`${adminURL}/team.php`);
    
    // Check admin page loads
    await expect(page.locator('h1:has-text("Team Management")')).toBeVisible();
    
    // Check main sections exist
    await expect(page.locator('h2:has-text("Add New Team Member")')).toBeVisible();
    await expect(page.locator('h2:has-text("Team Settings")')).toBeVisible();
    await expect(page.locator('h2:has-text("CEO Information")')).toBeVisible();
    
    // Check form fields exist
    await expect(page.locator('input[name="name"]')).toBeVisible();
    await expect(page.locator('input[name="position"]')).toBeVisible();
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="linkedin_url"]')).toBeVisible();
    await expect(page.locator('textarea[name="bio"]')).toBeVisible();
    await expect(page.locator('input[name="photo"]')).toBeVisible();
    
    console.log('✅ Team admin panel accessible');
  });

  test('should add new team member', async ({ page }) => {
    await loginToAdmin(page);
    await page.goto(`${adminURL}/team.php`);
    
    // Fill out the add team member form
    await page.fill('input[name="name"]', testTeamMember.name);
    await page.fill('input[name="position"]', testTeamMember.position);
    await page.fill('input[name="email"]', testTeamMember.email);
    await page.fill('input[name="linkedin_url"]', testTeamMember.linkedin);
    await page.fill('textarea[name="bio"]', testTeamMember.bio);
    await page.fill('input[name="sort_order"]', testTeamMember.sortOrder.toString());
    
    // Ensure active checkbox is checked
    await page.check('input[name="active"]');
    
    // Submit the form
    await page.click('button[type="submit"]:has-text("Add Team Member")');
    
    // Check for success message
    await expect(page.locator('.message.success')).toBeVisible();
    await expect(page.locator('.message.success')).toContainText('Team member added successfully');
    
    // Verify team member appears in the list
    await expect(page.locator(`text=${testTeamMember.name}`)).toBeVisible();
    await expect(page.locator(`text=${testTeamMember.position}`)).toBeVisible();
    
    console.log('✅ New team member added successfully');
  });

  test('should display team member on frontend', async ({ page }) => {
    await page.goto(`${baseURL}/team.php`);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check if test team member is visible
    await expect(page.locator(`.team-member:has-text("${testTeamMember.name}")`)).toBeVisible();
    
    // Check team member details
    const teamMemberCard = page.locator(`.team-member:has-text("${testTeamMember.name}")`);
    await expect(teamMemberCard.locator('.team-member-name')).toContainText(testTeamMember.name);
    await expect(teamMemberCard.locator('.team-member-role')).toContainText(testTeamMember.position);
    
    // Check hover effects work
    await teamMemberCard.hover();
    
    // Check social links if present
    if (testTeamMember.linkedin) {
      await expect(teamMemberCard.locator('a[href*="linkedin"]')).toBeVisible();
    }
    if (testTeamMember.email) {
      await expect(teamMemberCard.locator(`a[href*="mailto:${testTeamMember.email}"]`)).toBeVisible();
    }
    
    console.log('✅ Team member displays correctly on frontend');
  });

  test('should edit existing team member', async ({ page }) => {
    await loginToAdmin(page);
    await page.goto(`${adminURL}/team.php`);
    
    // Find and click edit button for test member
    const editButton = page.locator(`tr:has-text("${testTeamMember.name}") .btn:has-text("Edit")`);
    await editButton.click();
    
    // Wait for edit form to load
    await expect(page.locator(`h2:has-text("Edit Team Member: ${testTeamMember.name}")`)).toBeVisible();
    
    // Update team member information
    await page.fill('input[name="name"]', updatedTeamMember.name);
    await page.fill('input[name="position"]', updatedTeamMember.position);
    await page.fill('input[name="email"]', updatedTeamMember.email);
    await page.fill('textarea[name="bio"]', updatedTeamMember.bio);
    
    // Submit the update
    await page.click('button[type="submit"]:has-text("Update Team Member")');
    
    // Check for success message
    await expect(page.locator('.message.success')).toBeVisible();
    await expect(page.locator('.message.success')).toContainText('Team member updated successfully');
    
    // Verify updated information appears in the list
    await expect(page.locator(`text=${updatedTeamMember.name}`)).toBeVisible();
    await expect(page.locator(`text=${updatedTeamMember.position}`)).toBeVisible();
    
    console.log('✅ Team member updated successfully');
  });

  test('should toggle team member active status', async ({ page }) => {
    await loginToAdmin(page);
    await page.goto(`${adminURL}/team.php`);
    
    // Find the toggle button for test member
    const toggleButton = page.locator(`tr:has-text("${updatedTeamMember.name}") button:has-text("Disable")`);
    
    // Click toggle button
    await toggleButton.click();
    
    // Check for success message
    await expect(page.locator('.message.success')).toBeVisible();
    await expect(page.locator('.message.success')).toContainText('Team member status updated');
    
    // Verify status changed to inactive
    await expect(page.locator(`tr:has-text("${updatedTeamMember.name}") .status-inactive`)).toBeVisible();
    
    // Toggle back to active
    const enableButton = page.locator(`tr:has-text("${updatedTeamMember.name}") button:has-text("Enable")`);
    await enableButton.click();
    
    // Verify status changed back to active
    await expect(page.locator(`tr:has-text("${updatedTeamMember.name}") .status-active`)).toBeVisible();
    
    console.log('✅ Team member status toggle working');
  });

  test('should update CEO information', async ({ page }) => {
    await loginToAdmin(page);
    await page.goto(`${adminURL}/team.php`);
    
    // Scroll to CEO section
    await page.locator('h2:has-text("CEO Information")').scrollIntoViewIfNeeded();
    
    // Update CEO information
    await page.fill('input[name="ceo_name"]', ceoData.name);
    await page.fill('input[name="ceo_title"]', ceoData.title);
    await page.fill('textarea[name="ceo_bio"]', ceoData.bio);
    await page.fill('textarea[name="ceo_achievements"]', ceoData.achievements);
    
    // Submit CEO update
    await page.click('button:has-text("Update CEO Information")');
    
    // Check for success message
    await expect(page.locator('.message.success')).toBeVisible();
    await expect(page.locator('.message.success')).toContainText('CEO information updated successfully');
    
    console.log('✅ CEO information updated successfully');
  });

  test('should display updated CEO information on frontend', async ({ page }) => {
    await page.goto(`${baseURL}/team.php`);
    
    // Check updated CEO information
    await expect(page.locator('.ceo-info h2')).toContainText(ceoData.name);
    await expect(page.locator('.ceo-title')).toContainText(ceoData.title);
    await expect(page.locator('.ceo-bio')).toContainText('Updated CEO biography');
    
    // Check achievements list
    const achievementsList = page.locator('.ceo-achievements');
    await expect(achievementsList).toContainText('Licensed Architect in 15 states');
    await expect(achievementsList).toContainText('AIA Gold Medal Recipient (2024)');
    
    console.log('✅ Updated CEO information displays on frontend');
  });

  test('should enable team details functionality', async ({ page }) => {
    await loginToAdmin(page);
    await page.goto(`${adminURL}/team.php`);
    
    // Enable team details
    await page.check('input[name="enable_team_details"]');
    await page.click('button:has-text("Update Settings")');
    
    // Check for success message
    await expect(page.locator('.message.success')).toBeVisible();
    await expect(page.locator('.message.success')).toContainText('Team settings updated successfully');
    
    console.log('✅ Team details functionality enabled');
  });

  test('should display View Profile buttons when team details enabled', async ({ page }) => {
    await page.goto(`${baseURL}/team.php`);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check if View Profile buttons are visible
    const viewProfileButtons = page.locator('.view-profile-btn');
    await expect(viewProfileButtons.first()).toBeVisible();
    await expect(viewProfileButtons.first()).toContainText('View Profile');
    
    console.log('✅ View Profile buttons visible when team details enabled');
  });

  test('should navigate to team details page', async ({ page }) => {
    await page.goto(`${baseURL}/team.php`);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Click on first View Profile button
    const viewProfileButton = page.locator('.view-profile-btn').first();
    await viewProfileButton.click();
    
    // Check if redirected to team details page
    await expect(page.url()).toContain('team-details.php?id=');
    
    // Check team details page elements
    await expect(page.locator('.team-detail-hero')).toBeVisible();
    await expect(page.locator('.team-profile')).toBeVisible();
    await expect(page.locator('.team-expertise')).toBeVisible();
    await expect(page.locator('.back-btn')).toBeVisible();
    
    // Test back navigation
    await page.click('.back-btn');
    await expect(page.url()).toContain('team.php');
    
    console.log('✅ Team details page navigation working');
  });

  test('should test responsive design', async ({ page }) => {
    await page.goto(`${baseURL}/team.php`);
    
    // Test desktop view
    await page.setViewportSize({ width: 1280, height: 720 });
    await expect(page.locator('.team-members')).toBeVisible();
    await expect(page.locator('.ceo-content')).toBeVisible();
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('.team-members')).toBeVisible();
    await page.waitForTimeout(500); // Allow animations to complete
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('.team-members')).toBeVisible();
    await page.waitForTimeout(500); // Allow animations to complete
    
    console.log('✅ Responsive design working correctly');
  });

  test('should test team member card hover effects', async ({ page }) => {
    await page.goto(`${baseURL}/team.php`);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Find first team member card
    const teamMemberCard = page.locator('.team-member').first();
    await expect(teamMemberCard).toBeVisible();
    
    // Test hover effect
    await teamMemberCard.hover();
    
    // Check if overlay becomes visible on hover
    const overlay = teamMemberCard.locator('.team-member-overlay');
    await expect(overlay).toBeVisible();
    
    // Check social links in overlay
    const socialLinks = teamMemberCard.locator('.social-link');
    if (await socialLinks.count() > 0) {
      await expect(socialLinks.first()).toBeVisible();
    }
    
    console.log('✅ Team member card hover effects working');
  });

  test('should clean up test data', async ({ page }) => {
    await loginToAdmin(page);
    await page.goto(`${adminURL}/team.php`);
    
    // Delete test team member
    const deleteButton = page.locator(`tr:has-text("${updatedTeamMember.name}") button:has-text("Delete")`);
    
    // Handle confirmation dialog
    page.on('dialog', async dialog => {
      expect(dialog.message()).toContain('Are you sure');
      await dialog.accept();
    });
    
    await deleteButton.click();
    
    // Check for success message
    await expect(page.locator('.message.success')).toBeVisible();
    await expect(page.locator('.message.success')).toContainText('Team member deleted successfully');
    
    // Verify team member is no longer in the list
    await expect(page.locator(`text=${updatedTeamMember.name}`)).not.toBeVisible();
    
    console.log('✅ Test data cleaned up successfully');
  });

  test('should test image upload functionality', async ({ page }) => {
    await loginToAdmin(page);
    await page.goto(`${adminURL}/team.php`);
    
    // Create a simple test image (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB3, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    
    // Fill team member form
    await page.fill('input[name="name"]', 'Image Test Member');
    await page.fill('input[name="position"]', 'Test Position');
    
    // Upload test image
    await page.setInputFiles('input[name="photo"]', {
      name: 'test-image.png',
      mimeType: 'image/png',
      buffer: testImageBuffer
    });
    
    // Submit form
    await page.click('button[type="submit"]:has-text("Add Team Member")');
    
    // Check for success (image upload should work)
    await expect(page.locator('.message.success, .message.error')).toBeVisible();
    
    // If successful, verify image preview appears
    const successMessage = page.locator('.message.success');
    if (await successMessage.isVisible()) {
      console.log('✅ Image upload functionality working');
      
      // Clean up - delete the test member
      const deleteButton = page.locator(`tr:has-text("Image Test Member") button:has-text("Delete")`);
      if (await deleteButton.isVisible()) {
        page.on('dialog', async dialog => await dialog.accept());
        await deleteButton.click();
      }
    } else {
      console.log('ℹ️ Image upload may need server configuration');
    }
  });
});

// Run all tests
console.log('🚀 Starting Team Management System Tests...');
console.log('📋 Test Coverage:');
console.log('  • Team page display');
console.log('  • Admin panel access');
console.log('  • Add/Edit/Delete team members');
console.log('  • Toggle member status');
console.log('  • CEO information management');
console.log('  • Team details functionality');
console.log('  • Responsive design');
console.log('  • Image upload');
console.log('  • Navigation and UX');
console.log('🔍 Running comprehensive tests...');