<?php if (empty($sliders)): ?>
    <div class="text-center py-5">
        <i class="fas fa-images fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No sliders found</h5>
        <p class="text-muted">Create your first slider to get started with dynamic content presentation.</p>
    </div>
<?php else: ?>
    <div class="row">
        <?php foreach ($sliders as $index => $slider): ?>
            <div class="col-12 mb-4">
                <div class="card searchable-item" data-search="<?php echo htmlspecialchars(strtolower($slider['title'] . ' ' . $slider['subtitle'] . ' ' . $slider['button_text'])); ?>">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <!-- Preview Column -->
                            <div class="col-md-2">
                                <div class="slider-preview" style="
                                    width: 120px;
                                    height: 80px;
                                    background-image: url('<?php echo ensureAbsoluteUrl($slider['background_image']); ?>');
                                    background-size: cover;
                                    background-position: center;
                                    border-radius: 8px;
                                    position: relative;
                                    overflow: hidden;
                                ">
                                    <div style="
                                        position: absolute;
                                        top: 0;
                                        left: 0;
                                        right: 0;
                                        bottom: 0;
                                        background: <?php echo $slider['overlay_color']; ?>;
                                        opacity: <?php echo $slider['overlay_opacity']; ?>;
                                        border-radius: 8px;
                                    "></div>
                                    <div style="
                                        position: absolute;
                                        top: 50%;
                                        left: 50%;
                                        transform: translate(-50%, -50%);
                                        color: <?php echo $slider['text_color']; ?>;
                                        font-size: 10px;
                                        text-align: center;
                                        z-index: 2;
                                        font-weight: bold;
                                    ">
                                        SLIDE <?php echo $index + 1; ?>
                                    </div>
                                </div>
                                <div class="text-center mt-2">
                                    <span class="badge bg-secondary">Order: <?php echo $slider['sort_order']; ?></span>
                                </div>
                            </div>

                            <!-- Content Column -->
                            <div class="col-md-4">
                                <h6 class="mb-1"><?php echo htmlspecialchars($slider['title']); ?></h6>
                                <?php if (!empty($slider['subtitle'])): ?>
                                    <p class="text-muted small mb-2"><?php echo htmlspecialchars($slider['subtitle']); ?></p>
                                <?php endif; ?>
                                <?php if (!empty($slider['button_text'])): ?>
                                    <span class="badge bg-primary"><?php echo htmlspecialchars($slider['button_text']); ?></span>
                                    <?php if (!empty($slider['button_link']) && $slider['button_link'] !== '#'): ?>
                                        <i class="fas fa-external-link-alt text-muted ms-1" style="font-size: 10px;"></i>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>

                            <!-- Details Column -->
                            <div class="col-md-4">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted d-block">Heights:</small>
                                        <small><i class="fas fa-desktop"></i> <?php echo $slider['height_desktop']; ?>px</small><br>
                                        <small><i class="fas fa-tablet-alt"></i> <?php echo $slider['height_tablet']; ?>px</small><br>
                                        <small><i class="fas fa-mobile-alt"></i> <?php echo $slider['height_mobile']; ?>px</small>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted d-block">Animation:</small>
                                        <small class="text-capitalize"><?php echo $slider['animation_type']; ?> (<?php echo $slider['animation_duration']; ?>s)</small><br>
                                        <div class="mt-1">
                                            <?php if ($slider['auto_play']): ?>
                                                <span class="badge bg-success" style="font-size: 9px;">Auto</span>
                                            <?php endif; ?>
                                            <?php if ($slider['pause_on_hover']): ?>
                                                <span class="badge bg-info" style="font-size: 9px;">Pause</span>
                                            <?php endif; ?>
                                            <?php if ($slider['show_navigation_dots']): ?>
                                                <span class="badge bg-secondary" style="font-size: 9px;">Dots</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Actions Column -->
                            <div class="col-md-2 text-end">
                                <div class="mb-2">
                                    <span class="badge <?php echo $slider['active'] ? 'bg-success' : 'bg-secondary'; ?>">
                                        <?php echo $slider['active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </div>
                                <div class="btn-group-vertical btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="editSlider(<?php echo $slider['id']; ?>)">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this slider?');">
                                        <input type="hidden" name="action" value="delete_slider">
                                        <input type="hidden" name="slider_id" value="<?php echo $slider['id']; ?>">
                                        <button type="submit" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>
