<?php
/**
 * Blog Posts Table Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure posts variable is available
if (!isset($posts)) {
    $posts = [];
}
?>

<?php if (empty($posts)): ?>
    <div class="no-data-state">
        <div class="no-data-icon">
            <i class="fas fa-blog"></i>
        </div>
        <h3>No blog posts yet</h3>
        <p>Start creating engaging content for your website visitors.</p>
        <button type="button" class="btn btn-primary" onclick="toggleForm()">
            <i class="fas fa-plus me-2"></i>
            Create Your First Post
        </button>
    </div>
<?php else: ?>
    <div class="table-responsive">
        <table class="table table-hover table-striped">
            <thead>
                <tr>
                    <th width="50">#</th>
                    <th>Post</th>
                    <th>Category</th>
                    <th>Status</th>
                    <th>Author</th>
                    <th>Date</th>
                    <th width="120">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($posts as $index => $post): ?>
                <tr class="searchable-item"
                    data-search="<?php echo htmlspecialchars(strtolower($post['title'] . ' ' . $post['category'] . ' ' . $post['author'] . ' ' . $post['tags'])); ?>">
                    <td class="text-center">
                        <span class="text-muted"><?php echo $index + 1; ?></span>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <?php if ($post['featured_image']): ?>
                                <div class="post-thumbnail me-3">
                                    <img src="<?php echo ensureAbsoluteUrl($post['featured_image']); ?>" 
                                         alt="<?php echo htmlspecialchars($post['title']); ?>" 
                                         class="img-thumbnail">
                                </div>
                            <?php endif; ?>
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($post['title']); ?></h6>
                                <?php if ($post['excerpt']): ?>
                                    <small class="text-muted d-block">
                                        <?php echo htmlspecialchars(substr($post['excerpt'], 0, 80)); ?>
                                        <?php if (strlen($post['excerpt']) > 80): ?>...<?php endif; ?>
                                    </small>
                                <?php endif; ?>
                                <?php if ($post['status'] === 'published'): ?>
                                    <small>
                                        <a href="<?php echo siteUrl('news/' . $post['slug']); ?>" 
                                           target="_blank" 
                                           class="text-accent text-decoration-none">
                                            <i class="fas fa-external-link-alt me-1"></i>View Post
                                        </a>
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-light text-dark">
                            <?php echo htmlspecialchars($post['category']); ?>
                        </span>
                    </td>
                    <td>
                        <span class="badge <?php echo $post['status'] === 'published' ? 'bg-success' : 'bg-warning text-dark'; ?>">
                            <i class="fas <?php echo $post['status'] === 'published' ? 'fa-check-circle' : 'fa-clock'; ?> me-1"></i>
                            <?php echo ucfirst($post['status']); ?>
                        </span>
                    </td>
                    <td>
                        <small class="text-muted"><?php echo htmlspecialchars($post['author']); ?></small>
                    </td>
                    <td>
                        <?php if ($post['published_at']): ?>
                            <small class="text-muted">
                                <?php echo date('M j, Y', strtotime($post['published_at'])); ?>
                            </small>
                        <?php else: ?>
                            <small class="text-muted fst-italic">Not published</small>
                        <?php endif; ?>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="?edit=<?php echo $post['id']; ?>" 
                               class="btn btn-outline-primary" 
                               title="Edit Post">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form method="POST" 
                                  style="display: inline;" 
                                  onsubmit="return confirm('Are you sure you want to delete this post? This action cannot be undone.');">
                                <input type="hidden" name="action" value="delete_post">
                                <input type="hidden" name="id" value="<?php echo $post['id']; ?>">
                                <button type="submit" 
                                        class="btn btn-outline-danger" 
                                        title="Delete Post">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php
    $total_items = count($posts);
    $per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
    include __DIR__ . '/../components/pagination.php';
    ?>
<?php endif; ?>

<style>
.post-thumbnail img {
    width: 60px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
}

.no-data-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-data-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

.badge {
    font-size: 0.75rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
