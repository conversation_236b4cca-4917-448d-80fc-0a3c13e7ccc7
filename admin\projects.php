<?php
/**
 * Projects Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Session is already started in functions.php

// Check admin authentication
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_project':
                $title = sanitizeInput($_POST['title']);
                $slug = createSlug($_POST['slug'] ?: $title);
                $description = sanitizeInput($_POST['description']);
                $content = $_POST['content']; // Keep HTML formatting
                $category = sanitizeInput($_POST['category']);
                $client = sanitizeInput($_POST['client']);
                $location = sanitizeInput($_POST['location']);
                $completion_date = sanitizeInput($_POST['completion_date']);
                $active = isset($_POST['active']) ? 1 : 0;
                $featured_image = '';
                $gallery_images = '';
                
                // Handle featured image upload
                if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                    $upload = uploadFile($_FILES['featured_image'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload['success']) {
                        $featured_image = $upload['url'];
                    } else {
                        $error = 'Error uploading featured image: ' . $upload['message'];
                        break;
                    }
                }
                
                // Handle gallery images upload
                $gallery_urls = [];
                if (isset($_FILES['gallery_images']) && is_array($_FILES['gallery_images']['name'])) {
                    for ($i = 0; $i < count($_FILES['gallery_images']['name']); $i++) {
                        if ($_FILES['gallery_images']['error'][$i] === UPLOAD_ERR_OK) {
                            $file = [
                                'name' => $_FILES['gallery_images']['name'][$i],
                                'type' => $_FILES['gallery_images']['type'][$i],
                                'tmp_name' => $_FILES['gallery_images']['tmp_name'][$i],
                                'error' => $_FILES['gallery_images']['error'][$i],
                                'size' => $_FILES['gallery_images']['size'][$i]
                            ];
                            $upload = uploadFile($file, ['jpg', 'jpeg', 'png', 'webp']);
                            if ($upload['success']) {
                                $gallery_urls[] = $upload['url'];
                            }
                        }
                    }
                }
                $gallery_json = json_encode($gallery_urls);

                if (empty($error)) {
                    $stmt = $db->prepare("INSERT INTO projects (title, slug, description, content, category, client, location, completion_date, active, featured_image, gallery) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

                    if ($stmt->execute([$title, $slug, $description, $content, $category, $client, $location, $completion_date, $active, $featured_image, $gallery_json])) {
                        $message = 'Project added successfully!';
                    } else {
                        $error = 'Error adding project to database.';
                    }
                }
                break;
                
            case 'update_project':
                $id = (int)$_POST['id'];
                $title = sanitizeInput($_POST['title']);
                $slug = createSlug($_POST['slug'] ?: $title);
                $description = sanitizeInput($_POST['description']);
                $content = $_POST['content'];
                $category = sanitizeInput($_POST['category']);
                $client = sanitizeInput($_POST['client']);
                $location = sanitizeInput($_POST['location']);
                $completion_date = sanitizeInput($_POST['completion_date']);
                $active = isset($_POST['active']) ? 1 : 0;

                // Initialize update query parameters
                $featured_image_sql = '';
                $params = [$title, $slug, $description, $content, $category, $client, $location, $completion_date, $active];
                
                // Handle featured image upload
                if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                    $upload = uploadFile($_FILES['featured_image'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload['success']) {
                        $featured_image_sql = ', featured_image = ?';
                        $params[] = $upload['url'];
                    } else {
                        $error = 'Error uploading featured image: ' . $upload['message'];
                        break;
                    }
                }
                
                // Handle gallery images upload
                $gallery_image_sql = '';
                if (isset($_FILES['gallery_images']) && is_array($_FILES['gallery_images']['name'])) {
                    $gallery_urls = [];
                    for ($i = 0; $i < count($_FILES['gallery_images']['name']); $i++) {
                        if ($_FILES['gallery_images']['error'][$i] === UPLOAD_ERR_OK) {
                            $file = [
                                'name' => $_FILES['gallery_images']['name'][$i],
                                'type' => $_FILES['gallery_images']['type'][$i],
                                'tmp_name' => $_FILES['gallery_images']['tmp_name'][$i],
                                'error' => $_FILES['gallery_images']['error'][$i],
                                'size' => $_FILES['gallery_images']['size'][$i]
                            ];
                            $upload = uploadFile($file, ['jpg', 'jpeg', 'png', 'webp']);
                            if ($upload['success']) {
                                $gallery_urls[] = $upload['url'];
                            }
                        }
                    }
                    if (!empty($gallery_urls)) {
                        $gallery_image_sql = ', gallery = ?';
                        $params[] = json_encode($gallery_urls);
                    }
                }

                $params[] = $id;
                $stmt = $db->prepare("UPDATE projects SET title = ?, slug = ?, description = ?, content = ?, category = ?, client = ?, location = ?, completion_date = ?, active = ?, updated_at = NOW(){$featured_image_sql}{$gallery_image_sql} WHERE id = ?");
                
                if ($stmt->execute($params)) {
                    $message = 'Project updated successfully!';
                } else {
                    $error = 'Error updating project.';
                }
                break;
                
            case 'delete_project':
                $id = (int)$_POST['id'];
                $stmt = $db->prepare("DELETE FROM projects WHERE id = ?");
                if ($stmt->execute([$id])) {
                    $message = 'Project deleted successfully!';
                } else {
                    $error = 'Error deleting project.';
                }
                break;
        }
    }
}

// Get projects for listing
$projects = [];
$stmt = $db->prepare("SELECT * FROM projects ORDER BY completion_date DESC, created_at DESC");
$stmt->execute();
$projects = $stmt->fetchAll();

// Get statistics
$stats = [
    'total' => count($projects),
    'active' => count(array_filter($projects, fn($p) => $p['active'] == 1)),
    'inactive' => count(array_filter($projects, fn($p) => $p['active'] == 0)),
    'recent' => count(array_filter($projects, fn($p) => strtotime($p['created_at']) > strtotime('-30 days')))
];

// Get project for editing if requested
$edit_project = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $stmt = $db->prepare("SELECT * FROM projects WHERE id = ?");
    $stmt->execute([$_GET['edit']]);
    $edit_project = $stmt->fetch();
}

// Render the page using the new theme system
renderAdminPage('management', [
    'page_title' => 'Projects Management',
    'page_icon' => 'fas fa-project-diagram',
    'page_description' => 'Manage your portfolio projects with images, details, and client information.',
    'management_title' => 'Project Portfolio',
    'management_description' => 'Showcase your work with detailed project information, images, and client testimonials.',
    'management_actions' => [
        [
            'url' => siteUrl('projects'),
            'label' => 'View Projects',
            'class' => 'btn-outline-primary',
            'icon' => 'fas fa-external-link-alt',
            'target' => '_blank'
        ]
    ],
    'show_search' => true,
    'search_placeholder' => 'Search projects by title, category, client, or location...',
    'search_target' => '.searchable-item',
    'show_table' => true,
    'table_title' => 'All Projects',
    'table_content_file' => __DIR__ . '/theme/content/projects-table.php',
    'show_stats' => true,
    'stats_cards' => [
        [
            'icon' => 'fas fa-project-diagram',
            'number' => $stats['total'],
            'label' => 'Total Projects'
        ],
        [
            'icon' => 'fas fa-check-circle',
            'number' => $stats['active'],
            'label' => 'Active'
        ],
        [
            'icon' => 'fas fa-pause-circle',
            'number' => $stats['inactive'],
            'label' => 'Inactive'
        ],
        [
            'icon' => 'fas fa-calendar-plus',
            'number' => $stats['recent'],
            'label' => 'Recent (30d)'
        ]
    ],
    'custom_content_before_table' => function() use ($edit_project) {
        include __DIR__ . '/theme/content/projects-form.php';
    },
    'message' => $message,
    'error' => $error,
    'projects' => $projects,
    'edit_project' => $edit_project,
    'custom_js' => '
        function toggleForm() {
            const form = document.getElementById("projectForm");
            if (form) {
                form.classList.toggle("show");
                if (form.classList.contains("show")) {
                    document.getElementById("title").focus();
                }
            }
        }

        function cancelForm() {
            window.location.href = "projects.php";
        }

        // Auto-generate slug from title
        document.getElementById("title").addEventListener("input", function() {
            const slugField = document.getElementById("slug");
            if (!slugField.value || slugField.dataset.autoGenerated) {
                const slug = this.value
                    .toLowerCase()
                    .replace(/[^a-z0-9]+/g, "-")
                    .replace(/^-+|-+$/g, "");
                slugField.value = slug;
                slugField.dataset.autoGenerated = "true";
            }
        });

        document.getElementById("slug").addEventListener("input", function() {
            this.dataset.autoGenerated = "false";
        });
    '
]);
?>
