<?php
/**
 * Blog Post Form Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure edit_post variable is available
if (!isset($edit_post)) {
    $edit_post = null;
}
?>

<div class="admin-card">
    <div class="admin-card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas <?php echo $edit_post ? 'fa-edit' : 'fa-plus'; ?> me-2"></i>
            <?php echo $edit_post ? 'Edit Blog Post' : 'Add New Blog Post'; ?>
        </h5>
        <button type="button"
                class="btn btn-outline-secondary btn-sm"
                onclick="<?php echo $edit_post ? 'cancelForm()' : 'toggleForm()'; ?>">
            <i class="fas <?php echo $edit_post ? 'fa-times' : 'fa-plus'; ?> me-1"></i>
            <?php echo $edit_post ? 'Cancel Edit' : 'Add New Post'; ?>
        </button>
    </div>
    
    <div class="admin-card-body">
        <form method="POST"
              enctype="multipart/form-data"
              class="post-form show"
              id="postForm">
            <input type="hidden" name="MAX_FILE_SIZE" value="<?php echo MAX_FILE_SIZE; ?>">
            <input type="hidden" name="action" value="<?php echo $edit_post ? 'update_post' : 'add_post'; ?>">
            <?php if ($edit_post): ?>
                <input type="hidden" name="id" value="<?php echo $edit_post['id']; ?>">
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Main Content -->
                    <div class="form-group mb-3">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-1"></i>
                            Post Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               class="form-control" 
                               required 
                               value="<?php echo $edit_post ? htmlspecialchars($edit_post['title']) : ''; ?>"
                               onkeyup="generateSlug()"
                               placeholder="Enter an engaging post title">
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="slug" class="form-label">
                            <i class="fas fa-link me-1"></i>
                            URL Slug
                        </label>
                        <input type="text" 
                               id="slug" 
                               name="slug" 
                               class="form-control" 
                               value="<?php echo $edit_post ? htmlspecialchars($edit_post['slug']) : ''; ?>"
                               placeholder="Auto-generated from title">
                        <div class="form-text">Leave empty to auto-generate from title</div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="excerpt" class="form-label">
                            <i class="fas fa-align-left me-1"></i>
                            Excerpt/Summary
                        </label>
                        <textarea id="excerpt" 
                                  name="excerpt" 
                                  class="form-control" 
                                  rows="3" 
                                  placeholder="Brief summary of the post for listings and meta description"><?php echo $edit_post ? htmlspecialchars($edit_post['excerpt']) : ''; ?></textarea>
                        <div class="form-text">This appears in post listings and search results</div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="content" class="form-label">
                            <i class="fas fa-file-alt me-1"></i>
                            Content <span class="text-danger">*</span>
                        </label>
                        <textarea id="content" 
                                  name="content" 
                                  class="form-control content-editor" 
                                  required 
                                  rows="12"
                                  placeholder="Write your blog post content here. HTML formatting is supported."><?php echo $edit_post ? htmlspecialchars($edit_post['content']) : ''; ?></textarea>
                        <div class="form-text">
                            HTML formatting supported. Use &lt;h2&gt;, &lt;h3&gt;, &lt;p&gt;, &lt;ul&gt;, &lt;ol&gt;, &lt;blockquote&gt;, etc.
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- Sidebar Settings -->
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cog me-1"></i>
                                Post Settings
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <div class="form-group mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select id="status" name="status" class="form-select" required>
                                    <option value="draft" <?php echo ($edit_post && $edit_post['status'] === 'draft') ? 'selected' : ''; ?>>
                                        <i class="fas fa-clock"></i> Draft
                                    </option>
                                    <option value="published" <?php echo ($edit_post && $edit_post['status'] === 'published') ? 'selected' : ''; ?>>
                                        <i class="fas fa-check-circle"></i> Published
                                    </option>
                                </select>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select id="category" name="category" class="form-select" required>
                                    <option value="">Select Category</option>
                                    <option value="Architecture" <?php echo ($edit_post && $edit_post['category'] === 'Architecture') ? 'selected' : ''; ?>>Architecture</option>
                                    <option value="Engineering" <?php echo ($edit_post && $edit_post['category'] === 'Engineering') ? 'selected' : ''; ?>>Engineering</option>
                                    <option value="Design" <?php echo ($edit_post && $edit_post['category'] === 'Design') ? 'selected' : ''; ?>>Design</option>
                                    <option value="Construction" <?php echo ($edit_post && $edit_post['category'] === 'Construction') ? 'selected' : ''; ?>>Construction</option>
                                    <option value="Sustainability" <?php echo ($edit_post && $edit_post['category'] === 'Sustainability') ? 'selected' : ''; ?>>Sustainability</option>
                                    <option value="Innovation" <?php echo ($edit_post && $edit_post['category'] === 'Innovation') ? 'selected' : ''; ?>>Innovation</option>
                                    <option value="News" <?php echo ($edit_post && $edit_post['category'] === 'News') ? 'selected' : ''; ?>>Company News</option>
                                </select>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="author" class="form-label">Author</label>
                                <input type="text" 
                                       id="author" 
                                       name="author" 
                                       class="form-control" 
                                       value="<?php echo $edit_post ? htmlspecialchars($edit_post['author']) : 'Monolith Design Team'; ?>">
                            </div>
                            
                            <div class="form-group mb-0">
                                <label for="tags" class="form-label">Tags</label>
                                <input type="text" 
                                       id="tags" 
                                       name="tags" 
                                       class="form-control" 
                                       value="<?php echo $edit_post ? htmlspecialchars($edit_post['tags']) : ''; ?>"
                                       placeholder="modern, sustainable, commercial">
                                <div class="form-text">Separate tags with commas</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-card mb-3">
                        <div class="admin-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-image me-1"></i>
                                Featured Image
                            </h6>
                        </div>
                        <div class="admin-card-body">
                            <?php if ($edit_post && $edit_post['featured_image']): ?>
                                <div class="current-image mb-3">
                                    <img src="<?php echo ensureAbsoluteUrl($edit_post['featured_image']); ?>" 
                                         alt="Current Featured Image" 
                                         class="img-fluid rounded">
                                    <small class="text-muted d-block mt-1">Current featured image</small>
                                </div>
                            <?php endif; ?>
                            <input type="file" 
                                   id="featured_image" 
                                   name="featured_image" 
                                   class="form-control" 
                                   accept="image/*">
                            <div class="form-text">
                                Recommended: 1200x600px or larger<br>
                                Formats: JPG, PNG, WebP
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2 mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas <?php echo $edit_post ? 'fa-save' : 'fa-plus'; ?> me-1"></i>
                    <?php echo $edit_post ? 'Update Post' : 'Create Post'; ?>
                </button>
                <button type="button" class="btn btn-secondary" onclick="cancelForm()">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.post-form {
    display: none;
}

.post-form.show {
    display: block;
}

.content-editor {
    min-height: 300px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.current-image img {
    max-height: 150px;
    object-fit: cover;
}

.admin-card .admin-card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-text {
    font-size: 0.8rem;
}
</style>
