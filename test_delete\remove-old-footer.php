<?php
/**
 * Clean Up Script - Remove Old Footer System
 * Run this once to clean up old footer files
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';

echo "<h2>🧹 Footer Cleanup Script</h2>\n";

// Files to backup before deletion
$filesToBackup = [
    'templates/footer.php',
];

// Create backup directory
$backupDir = 'backups/old-footer-' . date('Y-m-d-H-i-s');
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
    echo "✅ Created backup directory: $backupDir\n";
}

// Backup old footer files
foreach ($filesToBackup as $file) {
    if (file_exists($file)) {
        $backupPath = $backupDir . '/' . basename($file);
        if (copy($file, $backupPath)) {
            echo "✅ Backed up: $file → $backupPath\n";
        } else {
            echo "❌ Failed to backup: $file\n";
        }
    }
}

// Check for old footer usage in PHP files
echo "\n<h3>🔍 Scanning for Old Footer Usage</h3>\n";
$phpFiles = glob('*.php');
$oldFooterUsage = [];

foreach ($phpFiles as $file) {
    $content = file_get_contents($file);
    if (strpos($content, "loadTemplate('footer')") !== false) {
        $oldFooterUsage[] = $file;
    }
}

if (empty($oldFooterUsage)) {
    echo "✅ No old footer usage found in PHP files\n";
} else {
    echo "⚠️ Old footer usage found in:\n";
    foreach ($oldFooterUsage as $file) {
        echo "   - $file\n";
    }
}

// Remove old footer CSS conflicts
echo "\n<h3>🎨 CSS Cleanup Recommendations</h3>\n";
echo "1. Old footer CSS is scattered in:\n";
echo "   - assets/css/style.css (lines 886-1500+)\n";
echo "   - assets/css/arkify-style.css\n";
echo "2. ✅ New footer CSS is clean: components/footer/css/footer.css\n";
echo "3. 💡 Consider removing old footer CSS blocks from style.css\n";

echo "\n<h3>✅ Cleanup Complete</h3>\n";
echo "Your footer system is now clean and optimized!\n";
?>
