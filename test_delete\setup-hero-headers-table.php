<?php
/**
 * Setup Hero Headers Table
 * Creates the database table for page header hero sections
 */

define('MONOLITH_ACCESS', true);
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>HERO HEADERS TABLE SETUP</h1><pre>";

try {
    $db = Database::getConnection();
    
    // Check if table exists
    $stmt = $db->query("SHOW TABLES LIKE 'hero_headers'");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "✅ hero_headers table already exists\n";
    } else {
        echo "📝 Creating hero_headers table...\n";
        
        $sql = "
        CREATE TABLE hero_headers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page_name VARCHAR(100) NOT NULL UNIQUE,
            page_title VARCHAR(255),
            subtitle VARCHAR(255),
            show_breadcrumbs BOOLEAN DEFAULT TRUE,
            background_type ENUM('image', 'gradient', 'color') DEFAULT 'gradient',
            background_image VARCHAR(255),
            background_gradient TEXT DEFAULT 'linear-gradient(135deg, rgba(55, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)',
            background_color VARCHAR(7) DEFAULT '#a99eff',
            background_opacity DECIMAL(3,2) DEFAULT 0.60,
            height_type ENUM('small', 'medium', 'large', 'custom') DEFAULT 'medium',
            height_custom INT DEFAULT 400,
            title_color VARCHAR(7) DEFAULT '#ffffff',
            subtitle_color VARCHAR(7) DEFAULT '#ffffff',
            breadcrumb_color VARCHAR(7) DEFAULT '#ffffff',
            show_cta_button BOOLEAN DEFAULT FALSE,
            cta_button_text VARCHAR(100),
            cta_button_link VARCHAR(255),
            cta_button_color VARCHAR(7) DEFAULT '#E67E22',
            cta_button_text_color VARCHAR(7) DEFAULT '#ffffff',
            active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_page_name (page_name),
            INDEX idx_active (active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $db->exec($sql);
        echo "✅ hero_headers table created successfully\n";
    }
    
    // Show table structure
    echo "\n📋 Table structure:\n";
    $stmt = $db->query("DESCRIBE hero_headers");
    $columns = $stmt->fetchAll();
    
    printf("%-25s %-30s %-10s %-10s\n", "Field", "Type", "Null", "Key");
    echo str_repeat("-", 80) . "\n";
    
    foreach ($columns as $column) {
        printf("%-25s %-30s %-10s %-10s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'], 
            $column['Key']
        );
    }
    
    // Insert default entries for common pages
    echo "\n📝 Creating default hero header entries...\n";
    
    $default_pages = [
        ['page_name' => 'home', 'page_title' => 'Welcome to Monolith Design', 'subtitle' => 'Innovative Architecture & Engineering Solutions'],
        ['page_name' => 'about', 'page_title' => 'About Us', 'subtitle' => 'Crafting architectural excellence through innovation'],
        ['page_name' => 'contact', 'page_title' => 'Contact Us', 'subtitle' => 'Get in touch to discuss your project'],
        ['page_name' => 'services', 'page_title' => 'Our Services', 'subtitle' => 'Professional architectural and engineering solutions'],
        ['page_name' => 'projects', 'page_title' => 'Our Projects', 'subtitle' => 'Showcasing our architectural achievements'],
        ['page_name' => 'team', 'page_title' => 'Our Team', 'subtitle' => 'Meet the experts behind our success'],
        ['page_name' => 'news', 'page_title' => 'News & Insights', 'subtitle' => 'Latest updates from the world of architecture']
    ];
    
    $stmt = $db->prepare("
        INSERT IGNORE INTO hero_headers (page_name, page_title, subtitle) 
        VALUES (?, ?, ?)
    ");
    
    foreach ($default_pages as $page) {
        $stmt->execute([$page['page_name'], $page['page_title'], $page['subtitle']]);
        echo "  ✅ Created hero header for: {$page['page_name']}\n";
    }
    
    // Show current entries count
    $stmt = $db->query("SELECT COUNT(*) as count FROM hero_headers");
    $count = $stmt->fetch()['count'];
    echo "\n📊 Total hero headers: $count\n";
    
    // Show sample entries
    echo "\n📋 Sample entries:\n";
    $stmt = $db->query("SELECT page_name, page_title, subtitle, active FROM hero_headers LIMIT 5");
    $entries = $stmt->fetchAll();
    
    printf("%-15s %-30s %-40s %-8s\n", "Page", "Title", "Subtitle", "Active");
    echo str_repeat("-", 95) . "\n";
    
    foreach ($entries as $entry) {
        printf("%-15s %-30s %-40s %-8s\n", 
            $entry['page_name'], 
            substr($entry['page_title'], 0, 28),
            substr($entry['subtitle'], 0, 38),
            $entry['active'] ? 'Yes' : 'No'
        );
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
