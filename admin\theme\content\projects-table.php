<?php
/**
 * Projects Table Content Template
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Ensure projects variable is available
if (!isset($projects)) {
    $projects = [];
}
?>

<?php if (empty($projects)): ?>
    <div class="no-data-state">
        <div class="no-data-icon">
            <i class="fas fa-project-diagram"></i>
        </div>
        <h3>No projects yet</h3>
        <p>Start showcasing your work by adding your first project with images and details.</p>
        <button type="button" class="btn btn-primary" onclick="toggleForm()">
            <i class="fas fa-plus me-2"></i>
            Add Your First Project
        </button>
    </div>
<?php else: ?>
    <div class="table-responsive">
        <table class="table table-hover table-striped">
            <thead>
                <tr>
                    <th width="50">#</th>
                    <th>Project</th>
                    <th>Category</th>
                    <th>Client</th>
                    <th>Location</th>
                    <th>Completion</th>
                    <th>Status</th>
                    <th width="140">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($projects as $index => $project): ?>
                <tr class="searchable-item" 
                    data-search="<?php echo htmlspecialchars(strtolower($project['title'] . ' ' . $project['category'] . ' ' . $project['client'] . ' ' . $project['location'] . ' ' . $project['description'])); ?>">
                    <td class="text-center">
                        <span class="text-muted"><?php echo $index + 1; ?></span>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <?php if ($project['featured_image']): ?>
                                <div class="project-thumbnail me-3">
                                    <img src="<?php echo ensureAbsoluteUrl($project['featured_image']); ?>" 
                                         alt="<?php echo htmlspecialchars($project['title']); ?>" 
                                         class="rounded">
                                </div>
                            <?php endif; ?>
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($project['title']); ?></h6>
                                <small class="text-muted">
                                    <?php 
                                    $description = htmlspecialchars($project['description']);
                                    echo strlen($description) > 60 ? substr($description, 0, 60) . '...' : $description;
                                    ?>
                                </small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <?php if ($project['category']): ?>
                            <span class="badge bg-info">
                                <?php echo htmlspecialchars($project['category']); ?>
                            </span>
                        <?php else: ?>
                            <span class="text-muted">-</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($project['client']): ?>
                            <span class="fw-medium"><?php echo htmlspecialchars($project['client']); ?></span>
                        <?php else: ?>
                            <span class="text-muted">-</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($project['location']): ?>
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($project['location']); ?>
                            </small>
                        <?php else: ?>
                            <span class="text-muted">-</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($project['completion_date']): ?>
                            <small class="text-muted">
                                <?php echo date('M Y', strtotime($project['completion_date'])); ?>
                            </small>
                        <?php else: ?>
                            <span class="text-muted">-</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($project['active']): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Active
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary">
                                <i class="fas fa-pause-circle me-1"></i>
                                Inactive
                            </span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" 
                                    class="btn btn-outline-primary" 
                                    data-bs-toggle="modal" 
                                    data-bs-target="#projectModal<?php echo $project['id']; ?>"
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <a href="?edit=<?php echo $project['id']; ?>" 
                               class="btn btn-outline-secondary" 
                               title="Edit Project">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form method="POST" 
                                  style="display: inline;" 
                                  onsubmit="return confirm('Are you sure you want to delete this project? This action cannot be undone.');">
                                <input type="hidden" name="action" value="delete_project">
                                <input type="hidden" name="id" value="<?php echo $project['id']; ?>">
                                <button type="submit" 
                                        class="btn btn-outline-danger" 
                                        title="Delete Project">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <?php 
    $total_items = count($projects);
    $per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
    include __DIR__ . '/../components/pagination.php'; 
    ?>
    
    <!-- Project Details Modals -->
    <?php foreach ($projects as $project): ?>
        <div class="modal fade" id="projectModal<?php echo $project['id']; ?>" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-project-diagram me-2"></i>
                            Project Details
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4><?php echo htmlspecialchars($project['title']); ?></h4>
                                
                                <?php if ($project['featured_image']): ?>
                                <div class="mb-3">
                                    <img src="<?php echo ensureAbsoluteUrl($project['featured_image']); ?>" 
                                         alt="<?php echo htmlspecialchars($project['title']); ?>" 
                                         class="img-fluid rounded">
                                </div>
                                <?php endif; ?>
                                
                                <div class="mb-3">
                                    <h6>Description</h6>
                                    <p><?php echo nl2br(htmlspecialchars($project['description'])); ?></p>
                                </div>
                                
                                <?php if ($project['content']): ?>
                                <div class="mb-3">
                                    <h6>Project Details</h6>
                                    <div class="border rounded p-3 bg-light">
                                        <?php echo $project['content']; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php 
                                $gallery = json_decode($project['gallery'], true);
                                if ($gallery && !empty($gallery)): 
                                ?>
                                <div class="mb-3">
                                    <h6>Project Gallery</h6>
                                    <div class="row g-2">
                                        <?php foreach ($gallery as $image): ?>
                                        <div class="col-md-4">
                                            <img src="<?php echo ensureAbsoluteUrl($image); ?>" 
                                                 alt="Gallery Image" 
                                                 class="img-fluid rounded">
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4">
                                <div class="project-info">
                                    <h6>Project Information</h6>
                                    
                                    <div class="info-item">
                                        <label class="fw-bold">Category:</label>
                                        <span><?php echo $project['category'] ? htmlspecialchars($project['category']) : 'Not specified'; ?></span>
                                    </div>
                                    
                                    <div class="info-item">
                                        <label class="fw-bold">Client:</label>
                                        <span><?php echo $project['client'] ? htmlspecialchars($project['client']) : 'Not specified'; ?></span>
                                    </div>
                                    
                                    <div class="info-item">
                                        <label class="fw-bold">Location:</label>
                                        <span><?php echo $project['location'] ? htmlspecialchars($project['location']) : 'Not specified'; ?></span>
                                    </div>
                                    
                                    <div class="info-item">
                                        <label class="fw-bold">Completion Date:</label>
                                        <span><?php echo $project['completion_date'] ? date('F j, Y', strtotime($project['completion_date'])) : 'Not specified'; ?></span>
                                    </div>
                                    
                                    <div class="info-item">
                                        <label class="fw-bold">Status:</label>
                                        <span>
                                            <?php if ($project['active']): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    Active
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-pause-circle me-1"></i>
                                                    Inactive
                                                </span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    
                                    <div class="info-item">
                                        <label class="fw-bold">Created:</label>
                                        <span><?php echo date('F j, Y', strtotime($project['created_at'])); ?></span>
                                    </div>
                                    
                                    <?php if ($project['updated_at']): ?>
                                    <div class="info-item">
                                        <label class="fw-bold">Last Updated:</label>
                                        <span><?php echo date('F j, Y', strtotime($project['updated_at'])); ?></span>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="?edit=<?php echo $project['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>
                            Edit Project
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
<?php endif; ?>

<style>
.project-thumbnail img {
    width: 60px;
    height: 60px;
    object-fit: cover;
}

.project-info .info-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.project-info .info-item:last-child {
    border-bottom: none;
}

.project-info .info-item label {
    min-width: 100px;
    margin-bottom: 0;
}

.no-data-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-data-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
