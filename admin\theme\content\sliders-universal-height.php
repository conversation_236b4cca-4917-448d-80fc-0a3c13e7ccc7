<?php
$use_universal_height = getThemeOption('use_universal_slider_height', '0');
$universal_height_desktop = getThemeOption('universal_slider_height_desktop', '600');
$universal_height_tablet = getThemeOption('universal_slider_height_tablet', '450');
$universal_height_mobile = getThemeOption('universal_slider_height_mobile', '350');
?>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-arrows-alt-v"></i> Universal Height Settings</h5>
        <small class="text-muted">Apply consistent heights across all sliders</small>
    </div>
    <div class="card-body">
        <form method="POST" id="universalHeightForm">
            <input type="hidden" name="action" value="update_universal_height">
            
            <div class="mb-3">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="use_universal_height" name="use_universal_height" 
                           <?php echo $use_universal_height ? 'checked' : ''; ?> onchange="toggleUniversalHeight()">
                    <label class="form-check-label" for="use_universal_height">
                        <strong>Enable Universal Height</strong>
                    </label>
                </div>
                <div class="form-text">When enabled, all sliders will use these height settings instead of individual heights.</div>
            </div>

            <div id="universal_height_controls" style="display: <?php echo $use_universal_height ? 'block' : 'none'; ?>;">
                <div class="row">
                    <div class="col-md-4">
                        <label for="universal_height_desktop" class="form-label">
                            <i class="fas fa-desktop"></i> Desktop Height (px)
                        </label>
                        <input type="number" class="form-control" id="universal_height_desktop" name="universal_height_desktop" 
                               value="<?php echo $universal_height_desktop; ?>" min="200" max="1200">
                        <div class="form-text">Recommended: 500-800px</div>
                    </div>
                    <div class="col-md-4">
                        <label for="universal_height_tablet" class="form-label">
                            <i class="fas fa-tablet-alt"></i> Tablet Height (px)
                        </label>
                        <input type="number" class="form-control" id="universal_height_tablet" name="universal_height_tablet" 
                               value="<?php echo $universal_height_tablet; ?>" min="200" max="1000">
                        <div class="form-text">Recommended: 350-600px</div>
                    </div>
                    <div class="col-md-4">
                        <label for="universal_height_mobile" class="form-label">
                            <i class="fas fa-mobile-alt"></i> Mobile Height (px)
                        </label>
                        <input type="number" class="form-control" id="universal_height_mobile" name="universal_height_mobile" 
                               value="<?php echo $universal_height_mobile; ?>" min="200" max="800">
                        <div class="form-text">Recommended: 250-500px</div>
                    </div>
                </div>

                <div class="mt-3">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Note:</strong> Universal height settings will override individual slider heights. 
                        This ensures consistent appearance across all sliders on your website.
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end mt-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Universal Height Settings
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function toggleUniversalHeight() {
    const checkbox = document.getElementById('use_universal_height');
    const controls = document.getElementById('universal_height_controls');
    
    if (checkbox.checked) {
        controls.style.display = 'block';
    } else {
        controls.style.display = 'none';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleUniversalHeight();
});
</script>
