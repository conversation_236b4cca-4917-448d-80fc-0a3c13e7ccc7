<?php
/**
 * Complete Hero Headers Setup
 * 1. Add padding columns to database
 * 2. Add missing projects hero header
 * 3. Update existing records with default padding values
 */

define('MONOLITH_ACCESS', true);
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';

try {
    $db = Database::getConnection();
    
    echo "<h2>Complete Hero Headers Setup</h2>\n";
    
    // Step 1: Check and add padding columns
    echo "<h3>Step 1: Database Schema Update</h3>\n";
    
    $stmt = $db->query("DESCRIBE hero_headers");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $has_padding_top = in_array('padding_top', $columns);
    $has_padding_bottom = in_array('padding_bottom', $columns);
    
    if (!$has_padding_top || !$has_padding_bottom) {
        echo "<p>Adding padding control columns...</p>\n";
        
        if (!$has_padding_top) {
            $db->exec("ALTER TABLE hero_headers ADD COLUMN padding_top VARCHAR(20) DEFAULT '4rem' AFTER height_custom");
            echo "<p>✅ Added padding_top column</p>\n";
        }
        
        if (!$has_padding_bottom) {
            $db->exec("ALTER TABLE hero_headers ADD COLUMN padding_bottom VARCHAR(20) DEFAULT '4rem' AFTER padding_top");
            echo "<p>✅ Added padding_bottom column</p>\n";
        }
    } else {
        echo "<p>✅ Padding columns already exist</p>\n";
    }
    
    // Step 2: Update existing records with default padding values
    echo "<h3>Step 2: Update Existing Records</h3>\n";
    $stmt = $db->prepare("UPDATE hero_headers SET padding_top = '4rem', padding_bottom = '4rem' WHERE padding_top IS NULL OR padding_bottom IS NULL");
    $updated = $stmt->execute();
    
    if ($updated) {
        echo "<p>✅ Updated existing records with default padding values</p>\n";
    }
    
    // Step 3: Add missing projects hero header
    echo "<h3>Step 3: Add Missing Projects Hero Header</h3>\n";
    
    $stmt = $db->prepare("SELECT id FROM hero_headers WHERE page_name = 'projects'");
    $stmt->execute();
    $existing = $stmt->fetch();
    
    if ($existing) {
        echo "<p>✅ Projects hero header already exists (ID: {$existing['id']})</p>\n";
    } else {
        $stmt = $db->prepare("
            INSERT INTO hero_headers (
                page_name, page_title, subtitle, show_breadcrumbs,
                background_type, background_gradient, background_color, background_opacity,
                height_type, height_custom, padding_top, padding_bottom, title_color, subtitle_color, breadcrumb_color,
                show_cta_button, cta_button_text, cta_button_link, cta_button_color, cta_button_text_color,
                active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            'projects',
            'Our Projects',
            'Explore our portfolio of innovative architectural solutions and completed projects',
            1,
            'gradient',
            'linear-gradient(135deg, rgba(55, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.45) 100%)',
            '#a99eff',
            0.60,
            'large',
            600,
            '5rem',
            '5rem',
            '#ffffff',
            '#ffffff',
            '#ffffff',
            1,
            'View All Projects',
            'projects',
            '#E67E22',
            '#ffffff',
            1
        ]);
        
        if ($result) {
            echo "<p>✅ Projects hero header created successfully!</p>\n";
        } else {
            echo "<p>❌ Failed to create projects hero header</p>\n";
        }
    }
    
    // Step 4: Display current status
    echo "<h3>Step 4: Current Hero Headers Status</h3>\n";
    $stmt = $db->query("SELECT page_name, page_title, padding_top, padding_bottom, active FROM hero_headers ORDER BY page_name");
    $headers = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>\n";
    echo "<tr style='background: #f0f0f0;'><th>Page</th><th>Title</th><th>Top Padding</th><th>Bottom Padding</th><th>Status</th></tr>\n";
    foreach ($headers as $header) {
        $status = $header['active'] ? 'Active' : 'Inactive';
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$header['page_name']}</td>";
        echo "<td style='padding: 8px;'>{$header['page_title']}</td>";
        echo "<td style='padding: 8px;'>{$header['padding_top']}</td>";
        echo "<td style='padding: 8px;'>{$header['padding_bottom']}</td>";
        echo "<td style='padding: 8px;'>{$status}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<h3>✅ Setup Complete!</h3>\n";
    echo "<p>All hero headers are now properly configured with padding controls.</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
}
?>
