<?php
/**
 * Projects Page - Portfolio Showcase
 * Displays our architectural and design projects
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get projects from database
$projects = [];
try {
    $db = Database::getConnection();
    $stmt = $db->prepare("SELECT * FROM projects WHERE active = 1 ORDER BY completion_date DESC, created_at DESC");
    $stmt->execute();
    $projects = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching projects: " . $e->getMessage());
}

// Get featured project (first project)
$featured_project = !empty($projects) ? $projects[0] : null;

// Get other projects (excluding featured)
$other_projects = array_slice($projects, 1);

$pageTitle = 'Our Projects - Architectural Excellence Showcase';
$pageDescription = 'Explore our portfolio of completed projects including commercial buildings, residential developments, and innovative architectural designs.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Projects Page Specific CSS -->
    <style>
        /* ===== PROJECTS PAGE STYLES ===== */
        
        /* Filter Tabs */
        .project-filters {
            padding: 4rem 0 2rem;
            background: #f8f9fa;
        }
        
        .filter-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .filter-tab {
            padding: 1rem 2rem;
            background: white;
            border: 2px solid #e9ecef;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-weight: 600;
        }
        
        .filter-tab:hover,
        .filter-tab.active {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
            text-decoration: none;
        }
        
        /* Projects Grid */
        .projects-grid {
            padding: 4rem 0 8rem;
            background: #f8f9fa;
        }
        
        .projects-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .project-card {
            background: white;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
        }
        
        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }
        
        .project-image {
            height: 300px;
            overflow: hidden;
            position: relative;
        }
        
        .project-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .project-card:hover .project-image img {
            transform: scale(1.1);
        }
        
        .project-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(230, 126, 34, 0.9), rgba(0, 0, 0, 0.7));
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .project-card:hover .project-overlay {
            opacity: 1;
        }
        
        .project-view-btn {
            background: white;
            color: var(--accent-color);
            padding: 1rem 2rem;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .project-view-btn:hover {
            transform: scale(1.1);
            color: var(--accent-color);
            text-decoration: none;
        }
        
        .project-info {
            padding: 2.5rem;
        }
        
        .project-category {
            color: var(--accent-color);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1rem;
        }
        
        .project-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .project-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .project-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }
        
        .project-year {
            color: #999;
            font-size: 0.9rem;
        }
        
        .project-location {
            color: #666;
            font-size: 0.9rem;
        }
        
        /* Featured Project Section */
        .featured-project {
            padding: 8rem 0;
            background: white;
        }
        
        .featured-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6rem;
            align-items: center;
        }
        
        .featured-image {
            position: relative;
        }
        
        .featured-image img {
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .featured-image::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: -20px;
            bottom: -20px;
            border: 3px solid var(--accent-color);
            z-index: -1;
        }
        
        .featured-info h2 {
            color: var(--accent-color);
            font-size: 2.8rem;
            margin-bottom: 1rem;
        }
        
        .featured-category {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 2rem;
            font-weight: 300;
        }
        
        .featured-description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 2rem;
        }
        
        .featured-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: #f8f9fa;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--accent-color);
            display: block;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* Large screens - ensure 3 columns */
        @media (min-width: 1200px) {
            .projects-container {
                grid-template-columns: repeat(3, 1fr);
                max-width: 1200px;
                margin: 0 auto;
            }
        }

        /* Medium screens - 2 columns */
        @media (min-width: 768px) and (max-width: 1199px) {
            .projects-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .featured-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }
            

            
            .featured-info h2 {
                font-size: 2.2rem;
            }
            
            .projects-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }
        
        @media (max-width: 768px) {

            
            .project-filters,
            .projects-grid,
            .featured-project {
                padding: 4rem 0;
            }
            
            .filter-tabs {
                flex-direction: column;
                align-items: center;
            }
            
            .featured-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Page Hero Section -->
    <?php
    // Hero data for projects page
    $hero_title = 'Our Projects';
    $hero_subtitle = 'Architectural Excellence Showcase';
    $hero_background = 'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=1600&h=900&fit=crop'; // Modern building background
    $hero_overlay_class = 'hero-faded-overlay';
    $breadcrumb_items = [
        ['title' => 'Home', 'url' => 'index.php'],
        ['title' => 'Projects', 'url' => '']
    ];

    // Load hero template
    include 'templates/page-hero.php';
    ?>

    <!-- Featured Project -->
    <?php if ($featured_project): ?>
    <section class="featured-project">
        <div class="container">
            <div class="featured-content">
                <div class="featured-image">
                    <img src="<?php echo $featured_project['featured_image'] ? ensureAbsoluteUrl($featured_project['featured_image']) : 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop'; ?>" alt="<?php echo htmlspecialchars($featured_project['title']); ?>">
                </div>
                <div class="featured-info">
                    <h2><?php echo htmlspecialchars($featured_project['title']); ?></h2>
                    <div class="featured-category"><?php echo htmlspecialchars($featured_project['category']); ?></div>
                    <div class="featured-description">
                        <p><?php echo htmlspecialchars($featured_project['description']); ?></p>

                        <?php if ($featured_project['content']): ?>
                        <p><?php echo htmlspecialchars(substr($featured_project['content'], 0, 200)) . '...'; ?></p>
                        <?php endif; ?>
                    </div>

                    <div class="featured-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?php echo htmlspecialchars($featured_project['client']); ?></span>
                            <span class="stat-label">Client</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo htmlspecialchars($featured_project['location']); ?></span>
                            <span class="stat-label">Location</span>
                        </div>
                        <?php if ($featured_project['completion_date']): ?>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo date('Y', strtotime($featured_project['completion_date'])); ?></span>
                            <span class="stat-label">Completed</span>
                        </div>
                        <?php endif; ?>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $featured_project['active'] ? 'Active' : 'Completed'; ?></span>
                            <span class="stat-label">Status</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Project Filters -->
    <section class="project-filters">
        <div class="container">
            <div class="filter-tabs">
                <a href="#" class="filter-tab active" data-filter="all">All Projects</a>
                <a href="#" class="filter-tab" data-filter="commercial">Commercial</a>
                <a href="#" class="filter-tab" data-filter="residential">Residential</a>
                <a href="#" class="filter-tab" data-filter="institutional">Institutional</a>
                <a href="#" class="filter-tab" data-filter="renovation">Renovation</a>
            </div>
        </div>
    </section>

    <!-- Projects Grid -->
    <section class="projects-grid">
        <div class="container">
            <div class="projects-container">
                <?php if (!empty($other_projects)): ?>
                    <?php foreach ($other_projects as $project): ?>
                    <div class="project-card" data-category="<?php echo strtolower($project['category']); ?>">
                        <div class="project-image">
                            <img src="<?php echo $project['featured_image'] ? ensureAbsoluteUrl($project['featured_image']) : 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=600&h=400&fit=crop'; ?>" alt="<?php echo htmlspecialchars($project['title']); ?>">
                            <div class="project-overlay">
                                <a href="<?php echo siteUrl('projects/' . $project['slug']); ?>" class="project-view-btn">View Project</a>
                            </div>
                        </div>
                        <div class="project-info">
                            <div class="project-category"><?php echo htmlspecialchars($project['category']); ?></div>
                            <h3 class="project-title"><?php echo htmlspecialchars($project['title']); ?></h3>
                            <p class="project-description"><?php echo htmlspecialchars($project['description']); ?></p>
                            <div class="project-meta">
                                <?php if ($project['completion_date']): ?>
                                <span class="project-year"><?php echo date('Y', strtotime($project['completion_date'])); ?></span>
                                <?php endif; ?>
                                <span class="project-location"><?php echo htmlspecialchars($project['location']); ?></span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div style="text-align: center; padding: 3rem; color: #666;">
                        <p>No projects found. Please check back soon for updates!</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Hero CTA Section -->
    <?php include 'templates/hero-cta.php'; ?>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
    
    <!-- Project Filter JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            const projectCards = document.querySelectorAll('.project-card');
            
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all tabs
                    filterTabs.forEach(t => t.classList.remove('active'));
                    
                    // Add active class to clicked tab
                    this.classList.add('active');
                    
                    const filterValue = this.getAttribute('data-filter');
                    
                    // Filter projects
                    projectCards.forEach(card => {
                        if (filterValue === 'all' || card.getAttribute('data-category') === filterValue) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>
