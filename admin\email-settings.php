<?php
/**
 * Email Settings - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

$message = '';
$messageType = '';

// Handle form submission
if ($_POST && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'update_email_settings':
            try {
                updateThemeOption('smtp_enabled', isset($_POST['smtp_enabled']) ? '1' : '0');
                updateThemeOption('smtp_host', sanitizeInput($_POST['smtp_host']));
                updateThemeOption('smtp_port', sanitizeInput($_POST['smtp_port']));
                updateThemeOption('smtp_username', sanitizeInput($_POST['smtp_username']));
                updateThemeOption('smtp_password', sanitizeInput($_POST['smtp_password']));
                updateThemeOption('smtp_encryption', sanitizeInput($_POST['smtp_encryption']));
                updateThemeOption('from_email', sanitizeInput($_POST['from_email']));
                updateThemeOption('from_name', sanitizeInput($_POST['from_name']));
                updateThemeOption('admin_email', sanitizeInput($_POST['admin_email']));
                
                $message = 'Email settings updated successfully!';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'Error updating email settings: ' . $e->getMessage();
                $messageType = 'error';
            }
            break;
            
        case 'test_email':
            $testEmail = sanitizeInput($_POST['test_email']);
            if (validateEmail($testEmail)) {
                $testData = [
                    'name' => 'Test User',
                    'email' => $testEmail,
                    'phone' => 'Test Phone',
                    'service' => 'Test Service',
                    'message' => 'This is a test email to verify your email configuration is working properly.'
                ];
                
                $template = getEmailTemplate('contact_notification', $testData);
                if ($template) {
                    $sent = sendEmail($testEmail, $template['subject'], $template['body']);
                    if ($sent) {
                        $message = 'Test email sent successfully to ' . $testEmail;
                        $messageType = 'success';
                    } else {
                        $message = 'Failed to send test email. Please check your email configuration.';
                        $messageType = 'error';
                    }
                } else {
                    $message = 'Error generating email template.';
                    $messageType = 'error';
                }
            } else {
                $message = 'Please enter a valid email address for testing.';
                $messageType = 'error';
            }
            break;
    }
}

// Get current settings
$settings = [
    'smtp_enabled' => getThemeOption('smtp_enabled', '0'),
    'smtp_host' => getThemeOption('smtp_host', 'smtp.gmail.com'),
    'smtp_port' => getThemeOption('smtp_port', '587'),
    'smtp_username' => getThemeOption('smtp_username', ''),
    'smtp_password' => getThemeOption('smtp_password', ''),
    'smtp_encryption' => getThemeOption('smtp_encryption', 'tls'),
    'from_email' => getThemeOption('from_email', '<EMAIL>'),
    'from_name' => getThemeOption('from_name', 'Monolith Design Co.'),
    'admin_email' => getThemeOption('admin_email', ADMIN_EMAIL)
];

// Render the page using the new theme system
renderAdminPage('settings', [
    'page_title' => 'Email Settings',
    'page_icon' => 'fas fa-envelope-open',
    'page_description' => 'Configure SMTP settings and email preferences for contact form notifications.',
    'show_page_header' => true,
    'content_file' => __DIR__ . '/theme/content/email-settings.php',
    'message' => $message,
    'messageType' => $messageType,
    'settings' => $settings
]);
?>
